using System;
using System.Collections;
using System.Collections.Generic;
using System.Diagnostics;
using Game.Hotfix.Config;
using Sirenix.Utilities;
using UnityEngine;
using UnityEngine.UI;
using UnityGameFramework.Runtime;

namespace Game.Hotfix
{
    public partial class UIUnionMileStoneForm : UGuiFormEx
    {
        private List<union_common_milestone> list = new();
        private Dictionary<int, Action> timeActDic = new();
        private List<int> timeActList = new();
        private float deltaTime = 0;

        protected override void OnInit(object userData)
        {
            base.OnInit(userData);

            InitBind();

            list = GameEntry.LDLTable.GetTable<union_common_milestone>();

            m_TableViewV.GetItemCount = () => list.Count;
            m_TableViewV.GetItemGo = () => m_goItem;
            m_TableViewV.UpdateItemCell = OnUpdateItem;
        }

        protected override void OnOpen(object userData)
        {
            base.OnOpen(userData);

            var minIndex = -1;
            var moveIndex = -1;
            for (int i = 0; i < list.Count; i++)
            {
                var data = GameEntry.LogicData.UnionData.GetMileStoneData(list[i].id);
                if (data.Status == 1)
                {
                    moveIndex = i;
                    break;
                }
                else if (data.Status == 0 && minIndex == -1)
                {
                    minIndex = i;
                }
            }
            if (moveIndex == -1)
            {
                if (minIndex != -1) { moveIndex = minIndex; }
                else { moveIndex = 0; }
            }

            timeActDic.Clear();
            timeActList.Clear();
            m_TableViewV.InitTableViewByIndex(moveIndex);
        }

        protected override void OnClose(bool isShutdown, object userData)
        {
            base.OnClose(isShutdown, userData);
        }

        public override void OnRefresh(object userData)
        {
            base.OnRefresh(userData);
        }

        protected override void OnUpdate(float elapseSeconds, float realElapseSeconds)
        {
            base.OnUpdate(elapseSeconds, realElapseSeconds);

            deltaTime += elapseSeconds;
            if (deltaTime >= 1)
            {
                deltaTime = 0;
                if (timeActList.Count > 0)
                {
                    for (int i = 0; i < timeActList.Count; i++)
                    {
                        timeActDic.TryGetValue(timeActList[i], out Action act);
                        act?.Invoke();
                    }
                }
            }
        }

        private void OnUpdateItem(int index, GameObject go)
        {
            var rootTrans = go.transform;
            var bg = rootTrans.Find("bg").GetComponent<UIImage>();
            var nameTxt = rootTrans.Find("bg/nameTxt").GetComponent<UIText>();
            var icon = rootTrans.Find("bg/icon").GetComponent<UIImage>();
            var titleTxt = rootTrans.Find("bg/titleTxt").GetComponent<UIText>();
            var line = rootTrans.Find("bg/line").GetComponent<UIImage>();
            var forTxt = rootTrans.Find("bg/forTxt").GetComponent<UIText>();
            var listBg = rootTrans.Find("bg/listBg").GetComponent<UIImage>();
            var progressBg = rootTrans.Find("bg/progressBg").gameObject;
            var progressSp = rootTrans.Find("bg/progressBg/progressSp").GetComponent<UIImage>();
            var progressTxt = rootTrans.Find("bg/progressBg/progressTxt").GetComponent<UIText>();
            var timeTxt = rootTrans.Find("bg/timeTxt").GetComponent<UIText>();
            var showTxt = rootTrans.Find("bg/showTxt").GetComponent<UIText>();
            var lockBg = rootTrans.Find("bg/lockBg").GetComponent<UIImage>();
            var finishSp = rootTrans.Find("bg/finishSp").gameObject;
            var shareBtn = rootTrans.Find("bg/shareBtn").GetComponent<UIButton>();
            var goBtn = rootTrans.Find("bg/goBtn").GetComponent<UIButton>();
            var maskBg = rootTrans.Find("bg/maskBg").gameObject;

            var config = list[index];
            var data = GameEntry.LogicData.UnionData.GetMileStoneData(config.id);

            var iconStr = config.mission_ico;
            if (!iconStr.IsNullOrWhitespace())
            {
                icon.SetImage(config.mission_ico, true);
                lockBg.SetImage(config.mission_ico, true);
            }
            nameTxt.text = ToolScriptExtend.GetLang(config.mission_name);

            if (config.mission_type == mission_type.mission_type_5)
            {
                var techConfig = GameEntry.LogicData.UnionData.GetTechLvConfig(config.mission_value.value);
                if (techConfig != null)
                    titleTxt.text = ToolScriptExtend.GetLangFormat(config.mission_desc, ToolScriptExtend.GetLang(techConfig.tech_name));
            }
            else
            {
                titleTxt.text = ToolScriptExtend.GetLangFormat(config.mission_desc, config.mission_value.value + "");
            }

            var isLeader = GameEntry.LogicData.UnionData.IsLeader();
            forTxt.text = isLeader ? ToolScriptExtend.GetLang(80200023) : ToolScriptExtend.GetLang(80200024);

            var time = (long)TimeComponent.Now - GameEntry.LogicData.UnionData.CreateTime;
            var totalTime = config.countdown + config.duration;
            var remainTime = totalTime - time;
            remainTime = remainTime < 0 ? 0 : remainTime;
            var openState = 2; // 0：未开始 1：进行中 2：已结束
            if (remainTime > 0) { openState = config.countdown >= time ? 0 : 1; }

            bg.SetImage(openState == 0 ? "Sprite/ui_lianmeng/lichengbei_dikuang1_1.png" : "Sprite/ui_lianmeng/lichengbei_dikuang1.png");
            line.SetImage(openState == 0 ? "Sprite/ui_lianmeng/lichengbei_xian2.png" : "Sprite/ui_lianmeng/lichengbei_xian1.png");
            listBg.SetImage(openState == 0 ? "Sprite/ui_lianmeng/lichengbei_dikuang2_1.png" : "Sprite/ui_lianmeng/lichengbei_dikuang2.png");
            lockBg.gameObject.SetActive(openState == 0);
            progressBg.SetActive(openState == 1 && data.Status == 0);
            timeTxt.gameObject.SetActive(openState != 2 && data.Status == 0);
            finishSp.SetActive(data.Status == 1 || data.Status == 2);
            maskBg.SetActive(data.Status == 4);
            icon.SetImageGray(data.Status == 3);

            var maxNum = config.mission_value.value;
            if (config.mission_type == mission_type.mission_type_5)
            {
                maxNum = 1;
            }
            var ratio = (float)data.Process / maxNum;
            ratio = ratio >= 1 ? 1 : ratio;
            var width = 606 * ratio;
            if (width >= 70)
            {
                progressSp.rectTransform.sizeDelta = new Vector2(606 * ratio, 50);
                progressSp.type = Image.Type.Sliced;
            }
            else
            {
                progressSp.rectTransform.sizeDelta = new Vector2(76, 50);
                progressSp.type = Image.Type.Filled;
                progressSp.fillMethod = Image.FillMethod.Horizontal;
                progressSp.fillAmount = width / 76;
            }
            progressTxt.text = data.Process + "/" + maxNum;

            if (data.Status == 0)
            {
                showTxt.text = "";
            }
            else if (data.Status == 1 || data.Status == 2)
            {
                var showStr = ToolScriptExtend.GetLangFormat(80200027, TimeHelper.ToDateTimeText(data.FinishTime));
                showTxt.text = $"<color=#008939>{showStr}</color>";
            }
            else if (data.Status == 3)
            {
                var showStr = ToolScriptExtend.GetLang(80200029) + $"\n{data.Process}/{config.mission_value.value}";
                showTxt.text = $"<color=#272d37>{showStr}</color>";
            }
            else if (data.Status == 4)
            {
                var showStr1 = ToolScriptExtend.GetLangFormat(80200027, TimeHelper.ToDateTimeText(data.FinishTime));
                var showStr2 = $"\n{ToolScriptExtend.GetLang(80200030)}";
                showTxt.text = $"<color=#008939>{showStr1}</color><color=#f05656>{showStr2}</color>";
            }

            var itemIdx = Convert.ToInt32(rootTrans.name);
            OnRemoveTimeAct(itemIdx);

            if (openState == 0 || (openState == 1 && data.Status == 0))
            {
                var showTime = openState == 0 ? (int)(config.countdown - time) : (int)remainTime;
                void OnCheckTime()
                {
                    if (time <= 0)
                    {
                        OnUpdateItem(index, go);
                        return;
                    }

                    if (config.countdown > time)
                    {
                        var day = Math.Ceiling((float)showTime / 86400);
                        if (day >= 2)
                        {
                            timeTxt.text = day + ToolScriptExtend.GetLang(1100094);
                        }
                        else
                        {
                            timeTxt.text = TimeHelper.FormatGameTimeWithDays(showTime);
                        }
                    }
                    else
                    {
                        timeTxt.text = TimeHelper.FormatGameTimeWithDays(showTime);
                    }
                    showTime--;
                }
                OnCheckTime();
                OnAddTimeAct(itemIdx, OnCheckTime);
            }

            shareBtn.onClick.RemoveAllListeners();
            shareBtn.onClick.AddListener(() =>
            {
                // 打开分享
            });

            goBtn.onClick.RemoveAllListeners();
            goBtn.onClick.AddListener(() =>
            {
                // 前往
            });

            var tableViewH = rootTrans.Find("bg/tableViewH").GetComponent<Mosframe.TableViewH>();
            var rewardItem = rootTrans.Find("bg/rewardItem").gameObject;

            var rewardList = isLeader ? config.r5_reward : config.member_reward;
            var count = rewardList.Count;
            width = count * 140;
            width = width < 600 ? width : 600;

            var rect = tableViewH.GetComponent<RectTransform>();
            rect.sizeDelta = new Vector2(width, 150);

            var scrollRect = tableViewH.GetComponent<ScrollRect>();
            scrollRect.horizontal = width == 600;

            tableViewH.GetItemCount = () => count;
            tableViewH.GetItemGo = () => rewardItem;
            tableViewH.UpdateItemCell = (idx, item) =>
            {
                var itemObj = item.transform.Find("itemObj");
                var maskSp = item.transform.Find("maskBg").gameObject;

                maskSp.SetActive(data.Status == 2);

                var reward = rewardList[idx];
                if (itemObj.childCount < 1)
                {
                    BagManager.CreatItem(itemObj, reward.item_id, reward.num, (UIItemModule) =>
                    {
                        UIItemModule.SetScale(0.55f);
                        UIItemModule.SetClick(() =>
                        {
                            if (data.Status == 1)
                            {
                                GameEntry.LogicData.UnionData.OnReqUnionMileStoneReward(data.Id, () =>
                                {
                                    OnUpdateItem(index, go);
                                });
                            }
                            else { UIItemModule.OpenTips(); }
                        });
                    });
                }
                else
                {
                    var itemModule = itemObj.GetChild(0).GetComponent<UIItemModule>();
                    itemModule.SetData(reward.item_id, reward.num);
                    itemModule.DisplayInfo();
                }
            };

            if (tableViewH.itemPrototype == null) { tableViewH.InitTableViewByIndex(0); }
            else { tableViewH.ReloadData(); }
        }

        private void OnAddTimeAct(int index, Action act)
        {
            if (!timeActDic.TryAdd(index, act))
            {
                timeActDic[index] = act;
            }
            else
            {
                timeActList.Add(index);
            }
        }

        private void OnRemoveTimeAct(int index)
        {
            timeActDic.Remove(index);
            timeActList.Remove(index);
        }

        private void OnBtnExitClick()
        {
            Close();
        }
    }
}

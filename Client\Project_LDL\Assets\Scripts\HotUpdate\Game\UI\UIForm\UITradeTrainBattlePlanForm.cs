using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;
using UnityGameFramework.Runtime;
using GameFramework.Event;
using Game.Hotfix.Config;
using DG.Tweening;

namespace Game.Hotfix
{
    public partial class UITradeTrainBattlePlanForm : UGuiFormEx
    {
        readonly List<Team.TeamType> teamTypeAttack = new()
        {
            Team.TeamType.TradeTrainAttack1,
            Team.TeamType.TradeTrainAttack2,
            Team.TeamType.TradeTrainAttack3,
        };

        readonly List<Team.TeamType> teamTypeDefend = new()
        {
            Team.TeamType.TradeTrainDefend1,
            Team.TeamType.TradeTrainDefend2,
            Team.TeamType.TradeTrainDefend3,
        };

        readonly List<Vector2> heroPos = new()
        {
            new(-128f, 74f),
            new(128, 74f),
            new(-128, -55f),
            new(0, -55f),
            new(128, -55f),
        };

        Dictionary<int, string> bgDict = new()
        {
            { 1, "Sprite/ui_maoyi/maoyi_zuoazhan_list_dikuang1.png" },
            { 2, "Sprite/ui_maoyi/maoyi_zuoazhan_list_dikuang2.png" },
            { 3, "Sprite/ui_maoyi/maoyi_zuoazhan_list_dikuang3.png" },
        };

        Trade.TradeCargoTransport trainData;
        int curTeamIndexLeft = 1;
        int curTeamIndexRight = 1;
        bool isPlayingAnim;

        protected override void OnInit(object userData)
        {
            base.OnInit(userData);

            InitBind();
            InitPanel();
        }

        protected override void OnOpen(object userData)
        {
            base.OnOpen(userData);

            if (userData is Trade.TradeCargoTransport data)
            {
                trainData = data;
            }

            HideDefault();
            RefreshPanel();
            GameEntry.Event.Subscribe(TeamChangeEventArgs.EventId, OnTeamChange);
        }

        protected override void OnClose(bool isShutdown, object userData)
        {
            base.OnClose(isShutdown, userData);
            GameEntry.Event.Unsubscribe(TeamChangeEventArgs.EventId, OnTeamChange);
            Timers.Instance.Remove("PlayVSEffect");
            isPlayingAnim = false;
        }

        protected override void OnDepthChanged(int uiGroupDepth, int depthInUIGroup)
        {
            base.OnDepthChanged(uiGroupDepth, depthInUIGroup);

            SetParticleSystemSortingOrder(m_goVSEffect, Depth);
            SetParticleSystemSortingOrder(m_goCrashEffect, Depth);

            foreach (Transform item in m_transTeamLeft)
            {
                SetParticleSystemSortingOrder(item.gameObject, Depth);
            }

            foreach (Transform item in m_transTeamRight)
            {
                SetParticleSystemSortingOrder(item.gameObject, Depth);
            }
        }

        public override void OnRefresh(object userData)
        {
            base.OnRefresh(userData);
        }

        void OnTeamChange(object sender, GameEventArgs e)
        {
            if (e is TeamChangeEventArgs teamChangeArgs)
            {

            }

            RefreshTeam(m_transTeamLeft);
        }

        private void OnBtnBackClick()
        {
            Close();
        }

        private void OnBtnFightClick()
        {
            PlayFightAnim();
            return;

            if (trainData == null)
            {
                ColorLog.Red("火车数据为空");
                return;
            }

            List<Team.TeamType> attack = new();
            List<Team.TeamType> defend = new();

            foreach (var item in teamTypeAttack)
            {
                List<Fight.FormationHero> teamData = GameEntry.LogicData.TeamData.GetTeam(item);
                if (teamData != null && teamData.Count > 0)
                {
                    attack.Add(item);
                }
            }

            GameEntry.TradeTruckData.RequestTrainStartFight(trainData.Id, attack, defend, (result) =>
            {
                ColorLog.Pink("火车开始战斗", result);
                GameEntry.UI.OpenUIForm(EnumUIForm.UITradeTrainBattleResultForm, trainData);
            });
        }

        private void OnBtnExchange1Click()
        {

        }

        private void OnBtnExchange2Click()
        {

        }

        void InitPanel()
        {

        }

        void HideDefault()
        {
            m_goVSEffect.SetActive(false);
            m_goCrashEffect.SetActive(false);
        }

        void RefreshPanel()
        {
            RefreshPlayer(m_transPlayerLeft);
            RefreshPlayer(m_transPlayerRight);
            RefreshTeam(m_transTeamLeft);
            RefreshTeam(m_transTeamRight);
        }

        void RefreshPlayer(Transform parent)
        {
            UIText txtName = parent.Find("txtName").GetComponent<UIText>();
            UIText txtPower = parent.Find("txtPower").GetComponent<UIText>();

            ulong roleID = 0;
            if (parent == m_transPlayerLeft)
            {
                roleID = GameEntry.RoleData.RoleID;
            }
            else if (parent == m_transPlayerRight && trainData != null)
            {
                roleID = trainData.RoleId;
            }

            if (roleID != 0)
            {
                GameEntry.RoleData.RequestRoleQueryLocalSingle(roleID, (roleBrief) =>
                {
                    ColorLog.Pink("查询玩家信息", roleBrief);
                    if (roleBrief != null)
                    {
                        txtName.text = roleBrief.Name;
                        txtPower.text = ToolScriptExtend.FormatNumberWithUnit(roleBrief.Power);
                    }
                });
            }
        }

        void RefreshTeam(Transform parent)
        {
            foreach (Transform teamParent in parent)
            {
                int index = int.Parse(teamParent.gameObject.name.Substring(teamParent.gameObject.name.Length - 1, 1));
                Team.TeamType teamType = Team.TeamType.TradeTrainAttack1;
                if (parent == m_transTeamLeft)
                {
                    teamType = teamTypeAttack[index - 1];
                }
                else if (parent == m_transTeamRight)
                {
                    teamType = teamTypeDefend[index - 1];
                }

                UIButton btnBg = teamParent.Find("bg").GetComponent<UIButton>();
                UIImage bg = teamParent.Find("bg").GetComponent<UIImage>();
                UIText txtNumber = teamParent.Find("txtNumber").GetComponent<UIText>();
                UIText txtPower = teamParent.Find("txtPower").GetComponent<UIText>();
                Transform heroParent = teamParent.Find("hero");

                List<Fight.FormationHero> teamData = GameEntry.LogicData.TeamData.GetTeam(teamType);

                txtPower.gameObject.SetActive(false);
                txtPower.text = string.Empty;

                if (teamData != null)
                {
                    ulong powerTotal = 0;
                    foreach (var data in teamData)
                    {
                        var heroVo = GameEntry.LogicData.HeroData.GetHeroModule((itemid)data.HeroId);
                        powerTotal += heroVo.power;
                    }
                    txtPower.text = ToolScriptExtend.FormatNumberWithUnit(powerTotal);
                    txtPower.gameObject.SetActive(powerTotal > 0);
                }

                for (int i = 0; i < 5; i++)
                {
                    Transform transHeroItem;
                    if (i < heroParent.childCount)
                    {
                        transHeroItem = heroParent.GetChild(i);
                    }
                    else
                    {
                        transHeroItem = Instantiate(m_transHeroItem, heroParent);
                    }
                    RectTransform rect = transHeroItem.GetComponent<RectTransform>();
                    rect.anchoredPosition = heroPos[i];
                    transHeroItem.gameObject.SetActive(true);
                    GameObject empty = transHeroItem.Find("empty").gameObject;
                    UIHeroItem item = transHeroItem.Find("UIHeroItem").GetComponent<UIHeroItem>();
                    if (teamData != null)
                    {
                        Fight.FormationHero heroData = null;
                        foreach (var hero in teamData)
                        {
                            if (hero.Pos == i + 1)
                            {
                                heroData = hero;
                            }
                        }

                        if (heroData != null)
                        {
                            HeroModule heroModule = GameEntry.LogicData.HeroData.GetHeroModule((itemid)heroData.HeroId);
                            item.Refresh(heroModule);
                            item.HideTeamBg();
                            item.gameObject.SetActive(true);
                            empty.SetActive(false);
                        }
                        else
                        {
                            item.gameObject.SetActive(false);
                            empty.SetActive(true);
                        }
                    }
                    else
                    {
                        item.gameObject.SetActive(false);
                        empty.SetActive(true);
                    }
                }

                btnBg.onClick.RemoveAllListeners();
                btnBg.onClick.AddListener(() =>
                {
                    if (parent == m_transTeamRight) return;
                    UITeamFormParam param = new()
                    {
                        Index = index - 1,
                        TeamFormType = UITeamFormType.TradeTrainAttack
                    };
                    GameEntry.UI.OpenUIForm(EnumUIForm.UITeamForm, param);
                    isPlayingAnim = false;
                    m_goVSEffect.SetActive(false);
                    m_goCrashEffect.SetActive(false);
                    Timers.Instance.Remove("PlayVSEffect");
                });
            }
        }

        void PlayFightAnim()
        {
            if (isPlayingAnim)
            {
                ColorLog.Yellow("正在播放动画");
                return;
            }

            isPlayingAnim = true;

            m_goVSEffect.SetActive(true);
            m_goCrashEffect.SetActive(false);

            Timers.Instance.Remove("PlayVSEffect");

            Timers.Instance.Add("PlayVSEffect", 1f, (param) =>
            {
                m_goVSEffect.SetActive(false);
                PlayCrashAnim(curTeamIndexLeft, curTeamIndexRight);
            }, 1);
        }

        void PlayCrashAnim(int teamIndexLeft, int teamIndexRight)
        {
            Transform teamLeft = m_transTeamLeft.Find("team" + teamIndexLeft);
            Transform teamRight = m_transTeamRight.Find("team" + teamIndexRight);

            float duration1 = 0.3f;
            float duration2 = 0.05f;

            Sequence sequence = DOTween.Sequence();
            sequence.Insert(0f, teamLeft.DOLocalMoveX(-460f, duration1).SetEase(Ease.InQuad));
            sequence.Insert(0f, teamRight.DOLocalMoveX(460f, duration1).SetEase(Ease.InQuad));
            sequence.Insert(duration1, teamLeft.DOLocalMoveX(0f, duration2).SetEase(Ease.InQuad));
            sequence.Insert(duration1, teamRight.DOLocalMoveX(0f, duration2).SetEase(Ease.InQuad));
            sequence.AppendCallback(() =>
            {
                m_goCrashEffect.SetActive(true);
            });
            sequence.Insert(duration1 + duration2, teamLeft.DOPunchPosition(new Vector3(0f, 70f, 0), 0.4f, 10, 1f));
            sequence.Insert(duration1 + duration2, teamRight.DOPunchPosition(new Vector3(0f, 70f, 0), 0.4f, 10, 1f));
            sequence.OnComplete(() =>
            {
                m_goCrashEffect.SetActive(false);
                SetTeamGrey(teamLeft, true, true);
                PlayWinEffect(teamRight);
                isPlayingAnim = false;
            });
        }

        void SetTeamGrey(Transform parent, bool isGrey, bool isLeft)
        {
            UIImage bg = parent.Find("bg").GetComponent<UIImage>();
            UIImage powerIcon = parent.Find("txtPower/Image").GetComponent<UIImage>();
            Transform heroParent = parent.Find("hero");
            if (isGrey)
            {
                bg.SetImage(bgDict[1]);
                powerIcon.SetImageGray(true);
                foreach (Transform item in heroParent)
                {
                    UIHeroItem heroItem = item.Find("UIHeroItem").GetComponent<UIHeroItem>();
                    heroItem.SetGrey(true);
                }
            }
            else
            {
                if (isLeft)
                {
                    bg.SetImage(bgDict[3]);
                }
                else
                {
                    bg.SetImage(bgDict[2]);
                }
                foreach (Transform item in heroParent)
                {
                    UIHeroItem heroItem = item.Find("UIHeroItem").GetComponent<UIHeroItem>();
                    heroItem.SetGrey(false);
                }
            }
        }

        void PlayWinEffect(Transform parent)
        {
            Transform effect = parent.Find("battle_huoche_shaoguang");
            effect.gameObject.SetActive(false);
            effect.gameObject.SetActive(true);
        }

        void PlayWheelAnim(int teamIndexLeft, int teamIndexRight)
        {
            Transform teamLeft = m_transTeamLeft.Find("team" + teamIndexLeft);
            Transform teamRight = m_transTeamRight.Find("team" + teamIndexRight);
        }
    }
}

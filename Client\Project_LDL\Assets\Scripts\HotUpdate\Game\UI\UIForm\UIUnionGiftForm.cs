using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.InteropServices.ComTypes;
using Game.Hotfix.Config;
using GameFramework.Event;
using UnityEngine;
using UnityEngine.UI;
using UnityEngine.UIElements;
using UnityGameFramework.Runtime;

namespace Game.Hotfix
{
    public partial class UIUnionGiftForm : UGuiFormEx
    {
        private int selectIdx = -1;
        private float deltaTime = 0;
        private int saveTime;
        private int expId = 15;
        private List<long> giftList;
        private Dictionary<int, Action> timeActDic = new();
        private List<int> timeActList = new();
        private List<union_gift_lv> configList = new();
        protected override void OnInit(object userData)
        {
            base.OnInit(userData);

            InitBind();

            m_txtNone.text = ToolScriptExtend.GetLang(80300002);

            var config = GameEntry.LogicData.UnionData.GetUnionConst(42);
            saveTime = config != null ? int.Parse(config[0]) : 0;

            m_imgExp.SetImage(ToolScriptExtend.GetItemIcon(expId));

            var list = GameEntry.LDLTable.GetTable<union_gift_lv>();
            for (int i = 0; i < list.Count; i++)
            {
                if (list[i].function_id > 0)
                    configList.Add(list[i]);
            }

            m_TableViewVLevel.GetItemCount = () => configList.Count;
            m_TableViewVLevel.GetItemGo = () => m_goTipItem;
            m_TableViewVLevel.UpdateItemCell = (index, go) =>
            {
                var rootTrans = go.transform.Find("bg");
                var bg = rootTrans.GetComponent<UIImage>();
                var iconBg = rootTrans.Find("iconBg").GetComponent<UIImage>();
                var icon = rootTrans.Find("icon").GetComponent<UIImage>();
                var nameTxt = rootTrans.Find("nameTxt").GetComponent<UIText>();
                var showTxt = rootTrans.Find("showTxt").GetComponent<UIText>();
                var lockTxt = rootTrans.Find("lockTxt").GetComponent<UIText>();
                var stateSp = rootTrans.Find("lockTxt/stateSp").GetComponent<UIImage>();

                var data = configList[index];
                nameTxt.text = ToolScriptExtend.GetLang(data.function_name);
                showTxt.text = ToolScriptExtend.GetLang(data.function_desc);
                lockTxt.text = ToolScriptExtend.GetLangFormat(80300006, data.id + "");
                icon.SetImage(data.function_ico);

                var isUnlock = data.id <= GameEntry.LogicData.UnionData.Level;
                ColorUtility.TryParseHtmlString(isUnlock ? "#5ef186" : "#fb7075", out Color colorStr);
                lockTxt.color = colorStr;
                bg.SetImage(isUnlock ? "Sprite/ui_lianmeng/gift_dikuang5.png" : "Sprite/ui_lianmeng/gift_dikuang7.png");
                iconBg.SetImage(isUnlock ? "Sprite/ui_lianmeng/gift_dikuang6.png" : "Sprite/ui_lianmeng/gift_dikuang8.png");
                stateSp.SetImage(isUnlock ? "Sprite/ui_lianmeng/gift_icon_gou.png" : "Sprite/ui_lianmeng/gift_icon_suo.png", true);

                nameTxt.SetHyperlinkCallback((refValue, innerValue) =>
                {
                    var showStr = nameTxt.text;
                    var idxList = ToolScriptExtend.FindStrAllIndex(showStr, innerValue + "<");
                    if (idxList.Count <= 0) return;

                    var curIdx = idxList[0];
                    idxList = ToolScriptExtend.FindStrAllIndex(showStr, innerValue);
                    for (int i = 0; i < idxList.Count; i++)
                    {
                        if (curIdx == idxList[i])
                        {
                            curIdx = i;
                            break;
                        }
                    }

                    showStr = ToolScriptExtend.GetRealStr(showStr, "<color=", "</color>");
                    showStr = ToolScriptExtend.GetRealStr(showStr, "<a href=", "</a>");

                    var startIndex = 0;
                    idxList = ToolScriptExtend.FindStrAllIndex(showStr, innerValue);
                    for (int i = 0; i < idxList.Count; i++)
                    {
                        if (curIdx == i)
                        {
                            startIndex = idxList[i];
                            break;
                        }
                    }
                    var endIndex = startIndex + innerValue.Length;
                    nameTxt.AnchorUIToTextSegment(startIndex, endIndex, m_goTipDesc.transform, new Vector2(0, -20));

                    var halfWidth = Screen.width / 2;
                    var halfBgWidth = 275;
                    var posX = m_goTipDesc.transform.localPosition.x;
                    var posY = m_goTipDesc.transform.localPosition.y;
                    float offsetX = 0;
                    if (posX - halfBgWidth < -halfWidth)
                    {
                        offsetX = Mathf.Abs(posX - halfBgWidth + halfWidth);
                    }
                    else if (posX + halfBgWidth > halfWidth)
                    {
                        offsetX = -(posX + halfBgWidth - halfWidth);
                    }
                    if (offsetX != 0)
                        m_goTipDesc.transform.SetLocalPosition(posX + offsetX, posY, 0);

                    m_txtTipDesc.text = ToolScriptExtend.GetLang(int.Parse(refValue));
                    m_btnTipDesc.gameObject.SetActive(true);

                    var rect = m_goTipDesc.GetComponent<RectTransform>();
                    LayoutRebuilder.ForceRebuildLayoutImmediate(rect);
                });
            };

            var count = m_transBtnList.childCount;
            for (int i = 0; i < count; i++)
            {
                var rootTrans = m_transBtnList.GetChild(i);
                var btn = rootTrans.GetComponent<UIButton>();
                var rectTrans = rootTrans.GetComponent<RectTransform>();
                var bg = rootTrans.Find("bg");
                var selectBg = rootTrans.Find("selectBg");

                rectTrans.sizeDelta = new Vector2(504, 102);
                bg.gameObject.SetActive(true);
                selectBg.gameObject.SetActive(false);

                int index = i;
                btn.onClick.RemoveAllListeners();
                btn.onClick.AddListener(() =>
                {
                    OnSelectBtn(index);
                });

                var redId = index == 0 ? EnumRed.Union_Gift_Normal : EnumRed.Union_Gift_High;
                RedPointManager.Instance.CreateRedPointItem(rootTrans, redId, (redPointItem) =>
                {
                    redPointItem.transform.localPosition = new Vector3(230, 30, 0);
                });
            }

            m_TableViewV.GetItemCount = () => giftList.Count;
            m_TableViewV.GetItemGo = () => m_goItem;
            m_TableViewV.UpdateItemCell = OnUpdateItem;
        }

        protected override void OnOpen(object userData)
        {
            base.OnOpen(userData);

            if (!GameEntry.Event.Check(UnionChangeEventArgs.EventId, OnUpdateEvent))
                GameEntry.Event.Subscribe(UnionChangeEventArgs.EventId, OnUpdateEvent);

            m_togAnons.onValueChanged.RemoveAllListeners();
            m_togAnons.isOn = GameEntry.LogicData.UnionData.IsAnonymousGift;
            m_togAnons.onValueChanged.AddListener((isOn) =>
            {
                GameEntry.LogicData.UnionData.OnReqUnionAnonymousGiftSwitch(isOn ? 1 : 0, (val) =>
                {
                    GameEntry.UI.OpenUIForm(EnumUIForm.UIFlyTextForm, new FlyTextParams()
                    {
                        Content = val ? ToolScriptExtend.GetLang(80300031) : ToolScriptExtend.GetLang(80300030),
                    });
                });
            });

            if (giftList != null)
            {
                giftList.Clear();
                OnUpdateList(false);
            }

            if (selectIdx != 0)
            {
                OnSelectBtn(0);
            }
            else
            {
                OnUpdateInfo();
            }
            OnBtnTipMaskClick();
            OnBtnTipDescClick();
        }

        protected override void OnClose(bool isShutdown, object userData)
        {
            base.OnClose(isShutdown, userData);
            if (GameEntry.Event.Check(UnionChangeEventArgs.EventId, OnUpdateEvent))
                GameEntry.Event.Unsubscribe(UnionChangeEventArgs.EventId, OnUpdateEvent);
        }

        public override void OnRefresh(object userData)
        {
            base.OnRefresh(userData);
        }

        protected override void OnUpdate(float elapseSeconds, float realElapseSeconds)
        {
            base.OnUpdate(elapseSeconds, realElapseSeconds);

            deltaTime += elapseSeconds;
            if (deltaTime >= 1)
            {
                deltaTime = 0;
                if (timeActList.Count > 0)
                {
                    for (int i = 0; i < timeActList.Count; i++)
                    {
                        timeActDic.TryGetValue(timeActList[i], out Action act);
                        act?.Invoke();
                    }
                }
            }
        }

        private void OnUpdateEvent(object sender, GameEventArgs e)
        {
            var unionChangeEventArgs = (UnionChangeEventArgs)e;
            if (unionChangeEventArgs.ChangeType == 0 || unionChangeEventArgs.ChangeType == 1)
            {
                OnUpdateShow();
                OnUpdateList(true);
            }
        }

        private void OnSelectBtn(int index, bool isInit = false)
        {
            if (selectIdx == index && !isInit) return;

            Transform trans;
            if (selectIdx > -1)
            {
                trans = m_transBtnList.GetChild(selectIdx);
                if (trans != null)
                {
                    var rectTrans = trans.GetComponent<RectTransform>();
                    var bg = trans.Find("bg").gameObject;
                    var selectBg = trans.Find("selectBg").gameObject;
                    rectTrans.sizeDelta = new Vector2(504, 102);
                    bg.SetActive(true);
                    selectBg.SetActive(false);
                }
            }

            trans = m_transBtnList.GetChild(index);
            if (trans != null)
            {
                var rectTrans = trans.GetComponent<RectTransform>();
                var bg = trans.Find("bg").gameObject;
                var selectBg = trans.Find("selectBg").gameObject;
                rectTrans.sizeDelta = new Vector2(524, 102);
                bg.SetActive(false);
                selectBg.SetActive(true);
            }

            var rect = m_transBtnList.GetComponent<RectTransform>();
            LayoutRebuilder.ForceRebuildLayoutImmediate(rect);

            selectIdx = index;
            OnUpdateInfo();
        }

        private void OnUpdateInfo()
        {
            timeActDic.Clear();
            timeActList.Clear();
            GameEntry.LogicData.UnionData.OnReqUnionGiftList((PbGameconfig.gift_tag_type)(selectIdx + 1), (idList) =>
            {
                giftList = idList;
                OnUpdateList(false);
            });
            m_txtGo.text = selectIdx == 0 ? ToolScriptExtend.GetLang(80300003) : ToolScriptExtend.GetLang(80300004);

            OnUpdateShow();
        }

        private void OnUpdateShow()
        {
            var unionData = GameEntry.LogicData.UnionData;
            var levelConfig = GameEntry.LDLTable.GetTableById<union_gift_lv>(unionData.Level);
            if (levelConfig != null)
            {
                m_txtLevel.text = ToolScriptExtend.GetLangFormat(80300036, levelConfig.id + "");

                var curNum = unionData.GiftExp;
                var maxNum = levelConfig.next_exp;
                var ratio = (float)curNum / maxNum;
                ratio = ratio > 1 ? 1 : ratio;
                ratio = ratio < 0 ? 0 : ratio;
                m_imgProgress.rectTransform.sizeDelta = new Vector2(754 * ratio, 43);
                m_txtProgress.text = $"{ToolScriptExtend.FormatNumberWithSeparator(curNum)}/{ToolScriptExtend.FormatNumberWithSeparator(maxNum)}";
            }
        }

        private void OnUpdateList(bool isReload)
        {
            var unionData = GameEntry.LogicData.UnionData;
            var showType = (PbGameconfig.gift_tag_type)(selectIdx + 1);
            giftList.Sort((a, b) =>
            {
                var aData = unionData.GetGiftData(showType, a);
                var bData = unionData.GetGiftData(showType, b);
                if (aData.Status == bData.Status)
                {
                    if (aData.Status == Common.RewardStatus.Receivable) { return bData.Time.CompareTo(aData.Time); }
                    else { return bData.ReceiveTime.CompareTo(aData.ReceiveTime); }
                }
                return aData.Status.CompareTo(bData.Status);
            });
            if (isReload) { m_TableViewV.ReloadData(); }
            else { m_TableViewV.InitTableViewByIndex(0); }

            var redNum = 0;
            var count = giftList.Count;
            for (int i = 0; i < count; i++)
            {
                var data = unionData.GetGiftData(showType, giftList[i]);
                if (data.Status == Common.RewardStatus.Receivable) redNum++;
            }
            m_txtRed.text = redNum + "";

            var isBtnShow = false;
            if (showType == PbGameconfig.gift_tag_type._1)
            {
                isBtnShow = redNum > 0;
                m_txtOneKey.text = ToolScriptExtend.GetLang(80300032);
                m_btnOneKey.rectTransform.anchoredPosition = new Vector2(0, 97);
            }
            else
            {
                isBtnShow = redNum > 0 && unionData.GetGiftFuncUnlock(6);
                m_txtOneKey.text = ToolScriptExtend.GetLang(80300033);
                m_btnOneKey.rectTransform.anchoredPosition = new Vector2(320, 97);
                m_togAnons.rectTransform.anchoredPosition = new Vector2(isBtnShow ? -288 : -100, 97);

                var togTxt = m_togAnons.transform.Find("m_txtAuto80300027").GetComponent<RectTransform>();
                togTxt.sizeDelta = new Vector2(isBtnShow ? 360 : 600, 120);
            }
            m_btnOneKey.gameObject.SetActive(isBtnShow);
            m_togAnons.gameObject.SetActive(showType == PbGameconfig.gift_tag_type._2);
            m_txtNone.gameObject.SetActive(count <= 0);
        }

        private void OnUpdateItem(int index, GameObject go)
        {
            var rootTrans = go.transform.Find("bg");
            var bg = rootTrans.GetComponent<UIImage>();
            var icon = rootTrans.Find("icon").GetComponent<UIImage>();
            var iconBtn = icon.GetComponent<UIButton>();
            var nameTxt = rootTrans.Find("nameTxt").GetComponent<UIText>();
            var timeTxt = rootTrans.Find("timeTxt").GetComponent<UIText>();
            var showTxt = rootTrans.Find("showTxt").GetComponent<UIText>();
            var numTxt = rootTrans.Find("numTxt").GetComponent<UIText>();
            var rewardSp = rootTrans.Find("numTxt/rewardSp").GetComponent<UIImage>();
            var getBtn = rootTrans.Find("getBtn").GetComponent<UIButton>();
            var btnTxt = rootTrans.Find("getBtn/btnTxt").GetComponent<UIText>();

            var unionData = GameEntry.LogicData.UnionData;
            var showType = (PbGameconfig.gift_tag_type)(selectIdx + 1);
            var data = unionData.GetGiftData(showType, giftList[index]);
            var config = GameEntry.LDLTable.GetTableById<union_gift_common>((int)data.Type);
            var args = data.Args;
            var roleId = args.Count > 0 ? args[0] : 0;
            var serverId = args.Count > 1 ? args[1] : 0;
            ulong itemId = 0;
            if (selectIdx == 0)
            {
                icon.SetImage(config.exp_ico);
                nameTxt.text = ToolScriptExtend.GetLang(config.gift_name);
                numTxt.text = $"+{config.add_exp}";
                GameEntry.RoleData.RequestRoleQueryLocalSingle(roleId, (roleBrief) =>
                {
                    showTxt.text = ToolScriptExtend.GetLangFormat(config.gift_show_way, roleBrief.Name);
                });
            }
            else
            {
                var payId = args.Count > 2 ? args[2] : 0;
                itemId = args.Count > 3 ? args[3] : 0;
                icon.SetImage(ToolScriptExtend.GetItemIcon((itemid)itemId));
                nameTxt.text = ToolScriptExtend.GetItemName((itemid)itemId);
                numTxt.text = $"+{unionData.GetHighGiftExp((int)itemId)}";

                var payConfig = GameEntry.LDLTable.GetTableById<payment>((int)payId);
                var goodsName = payConfig != null ? ToolScriptExtend.GetLang(payConfig.union_gift_name) : "";
                if (data.Announcement)
                {
                    showTxt.text = ToolScriptExtend.GetLangFormat(config.gift_show_way_noname, goodsName);
                }
                else
                {
                    GameEntry.RoleData.RequestRoleQueryLocalSingle(roleId, (roleBrief) =>
                    {
                        showTxt.text = ToolScriptExtend.GetLangFormat(config.gift_show_way, roleBrief.Name, goodsName);
                    });
                }
            }
            bg.SetImage(data.Status == Common.RewardStatus.Received ? "Sprite/ui_lianmeng/gift_dikuang3.png" : "Sprite/ui_lianmeng/gift_dikuang2.png");
            icon.gameObject.SetActive(data.Status != Common.RewardStatus.Received);
            rewardSp.SetImage(ToolScriptExtend.GetItemIcon(expId));
            getBtn.enabled = data.Status != Common.RewardStatus.Received;
            ToolScriptExtend.SetGameObjectGrey(getBtn.transform, data.Status == Common.RewardStatus.Received);
            btnTxt.text = data.Status == Common.RewardStatus.Received ? ToolScriptExtend.GetLang(1100262) : ToolScriptExtend.GetLang(1100261);
            
            var itemObj = rootTrans.Find("itemObj");
            if (data.Status == Common.RewardStatus.Received)
            {
                if (data.Article.Count > 0)
                {
                    var itemInfo = data.Article[0];
                    if (itemObj)
                    {
                        var itemModule = itemObj.GetComponent<UIItemModule>();
                        itemModule.SetData((itemid)itemInfo.Code, itemInfo.Amount);
                        itemModule.DisplayInfo();
                        itemObj.gameObject.SetActive(true);
                    }
                    else
                    {
                        BagManager.CreatItem(rootTrans, (itemid)itemInfo.Code, itemInfo.Amount, (itemModule) =>
                        {
                            itemModule.itemObj.name = "itemObj";
                            itemModule.itemObj.transform.SetLocalPositionX(-400);
                            itemModule.SetScale(0.7f);
                            itemModule.SetClick(() =>
                            {
                                itemModule.OpenTips();
                            });
                        });
                    }
                }
            }
            else
            {
                if (itemObj != null) itemObj.gameObject.SetActive(false);
            }

            var itemIdx = Convert.ToInt32(go.name);
            OnRemoveTimeAct(itemIdx);

            var time = data.Time - (long)TimeComponent.Now + saveTime;
            if (time > 0 && data.Status == Common.RewardStatus.Receivable)
            {
                void OnCheckTime()
                {
                    if (time <= 0)
                    {
                        giftList.Remove(giftList[index]);
                        OnUpdateList(true);
                        return;
                    }
                    timeTxt.text = TimeHelper.FormatGameTimeWithDays((int)time);
                    time--;
                }
                OnCheckTime();
                OnAddTimeAct(itemIdx, OnCheckTime);
            }
            else { timeTxt.text = ""; }

            iconBtn.onClick.RemoveAllListeners();
            iconBtn.onClick.AddListener(() =>
            {
                if (selectIdx == 0) { GameEntry.UI.OpenUIForm(EnumUIForm.UIUnionGiftRatioForm, (int)data.Type); }
                else { GameEntry.UI.OpenUIForm(EnumUIForm.UIItemBoxProForm, (itemid)itemId); }
            });

            getBtn.onClick.RemoveAllListeners();
            getBtn.onClick.AddListener(() =>
            {
                if (data.Status == Common.RewardStatus.Receivable)
                {
                    GameEntry.LogicData.UnionData.OnReqUnionGiftReceive(showType, data.Id, (rewardId) =>
                    {
                        OnUpdateItem(index, go);
                        OnUpdateList(true);

                        var btnTrans = getBtn.transform;
                        var form = GameEntry.UI.GetUIForm(EnumUIForm.UIResFlyForm) as UIResFlyForm;
                        Vector2 starPos = form.GetPosByTrans(btnTrans);
                        Vector2 endPos = form.GetPosByTrans(m_imgExp.gameObject.transform);
                        FlyResManager.UIFly((itemid)expId, 5, starPos, endPos);
                        FlyResManager.FlyResByItemId(rewardId, 5, btnTrans);
                    });
                }
            });
        }

        private void OnAddTimeAct(int index, Action act)
        {
            if (!timeActDic.TryAdd(index, act))
            {
                timeActDic[index] = act;
            }
            else
            {
                timeActList.Add(index);
            }
        }

        private void OnRemoveTimeAct(int index)
        {
            timeActDic.Remove(index);
            timeActList.Remove(index);
        }

        private void OnBtnExitClick()
        {
            Close();
        }

        private void OnBtnGiftClick()
        {
            m_btnTipMask.gameObject.SetActive(true);
            m_goTipLevel.SetActive(true);

            m_TableViewVLevel.InitTableViewByIndex(0);
        }

        private void OnBtnGoClick()
        {
            if (selectIdx == 0)
            {
                // 跳转到末日精英
            }
            else
            {
                var isFristCharge = false; // 是否首次充值
                if (isFristCharge) { }
                else
                {
                    GameEntry.UI.OpenUIForm(EnumUIForm.UIMallForm);
                    GameEntry.LogicData.UnionData.CloseAllUnionForm();
                    Close();
                }
            }
        }

        private void OnBtnOneKeyClick()
        {
            GameEntry.LogicData.UnionData.OnReqUnionGiftReceiveAll((PbGameconfig.gift_tag_type)(selectIdx + 1), (list) =>
            {
                var btnTrans = m_btnTipMask.transform;
                var form = GameEntry.UI.GetUIForm(EnumUIForm.UIResFlyForm) as UIResFlyForm;
                Vector2 starPos = form.GetPosByTrans(btnTrans);
                Vector2 endPos = form.GetPosByTrans(m_imgExp.gameObject.transform);

                for (int i = 0; i < list.Count; i++)
                {
                    var data = list[i];
                    FlyResManager.FlyResByItemId((itemid)data.Code, 5, btnTrans);
                }
                FlyResManager.UIFly((itemid)expId, 5, starPos, endPos);
                OnUpdateInfo();
            });
        }

        private void OnBtnTipMaskClick()
        {
            if (m_btnTipMask.gameObject.activeSelf) m_btnTipMask.gameObject.SetActive(false);
            if (m_goTipLevel.activeSelf) m_goTipLevel.SetActive(false);
        }

        private void OnBtnTipDescClick()
        {
            if (m_btnTipDesc.gameObject.activeSelf) m_btnTipDesc.gameObject.SetActive(false);
        }
    }
}

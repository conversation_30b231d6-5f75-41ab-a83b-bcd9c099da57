using GameFramework;
using GameFramework.Event;

namespace Game.Hotfix
{
    public class UavChangeLevelEventArgs : GameEventArgs
    {
        public static readonly int EventId = typeof(UavChangeLevelEventArgs).GetHashCode();
        public override void Clear()
        {
            
        }

        public override int Id
        {
            get
            {
                return EventId;
            }
        }
        
        public static UavChangeLevelEventArgs Create()
        {
            UavChangeLevelEventArgs uavChangeLevelEventArgs = ReferencePool.Acquire<UavChangeLevelEventArgs>();
            return uavChangeLevelEventArgs;
        }
    }
}
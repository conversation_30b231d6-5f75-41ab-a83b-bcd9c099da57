using UnityEngine;
using UnityEngine.UI;

namespace Game.Hotfix
{
    public partial class UIWorldMapTeamSelectForm : UGuiFormEx
    {
        [SerializeField] private UIButton m_btnInfo;
        [SerializeField] private UIButton m_btnConfirm;
        [SerializeField] private UIButton m_btnSetting;
        [SerializeField] private UIButton m_btnCreate;
        [SerializeField] private UIButton m_btnStaminaAdd;

        [SerializeField] private UIText m_txtName;
        [SerializeField] private UIText m_txtSoilderCnt;
        [SerializeField] private UIText m_txtFirst;
        [SerializeField] private UIText m_txtGatherAmountName;
        [SerializeField] private UIText m_txtGatherAmountValue;
        [SerializeField] private UIText m_txtGatherTimeName;
        [SerializeField] private UIText m_txtGatherTimeValue;
        [SerializeField] private UIText m_txtConfirmTime;
        [SerializeField] private UIText m_txtStaminaValue;
        [SerializeField] private UIText m_txtStaminaCost;

        [SerializeField] private Image m_imgGatherIcon;
        [SerializeField] private Image m_imgGatherTime;
        [SerializeField] private UIImage m_imgStaminaProgress;

        [SerializeField] private Slider m_SliderSoilder;

        [SerializeField] private RectTransform m_rectMenu;
        [SerializeField] private GameObject m_goArrMenu;
        [SerializeField] private GameObject m_goSoilder;
        [SerializeField] private Transform m_transHeros;
        [SerializeField] private GameObject m_goGather;
        [SerializeField] private GameObject m_goOperation;
        [SerializeField] private RectTransform m_rectCreate;
        [SerializeField] private GameObject m_goAnchorPos1;
        [SerializeField] private GameObject m_goAnchorPos2;
        [SerializeField] private GameObject m_goAnchorPos3;
        [SerializeField] private GameObject m_goAnchorPos4;
        [SerializeField] private GameObject m_goTeamList;
        [SerializeField] private GameObject m_goTeamItem;
        [SerializeField] private GameObject m_goHeroItem;

        void InitBind()
        {
            m_btnInfo.onClick.AddListener(OnBtnInfoClick);
            m_btnConfirm.onClick.AddListener(OnBtnConfirmClick);
            m_btnSetting.onClick.AddListener(OnBtnSettingClick);
            m_btnCreate.onClick.AddListener(OnBtnCreateClick);
            m_btnStaminaAdd.onClick.AddListener(OnBtnStaminaAddClick);
        }
    }
}

using Roledata;
using System.Collections.Generic;
using System;

namespace Game.Hotfix
{
    public class RoleData
    {
        // 使用 PascalCase 命名公共成员
        // 使用 camelCase 命名私有成员 (字段)

        private ulong m_roleID; // 角色 ID
        private uint m_ServerId; // 所在区服 id
        private string m_ServerName; // 所在区服名称
        private string m_Name; // 角色名称
        private bool m_IsCustomAvatar; // 是否是自定义头像
        private uint m_HeadSystemAvatar; // 角色头像图标
        private string m_HeadCustomAvatar; // 角色自定义头像链接地址
        private uint m_HeadBorder; // 角色头像框
        private ulong m_Power; // 总战力
        private long m_LastLoginAt; // 最后登录时间戳，秒
        private long m_LastLogoutAt; // 最后登出时间戳，秒
        private long m_CreateAt; // 角色创建时间戳，秒
        private int m_Lang; // 玩家当前语言 language_type.xlsx - id
        private uint m_Gender; // 性别，0 未知，1 男，2 女
        private uint m_KillSolderNum;//击败士兵数量的总和
        private int m_InnerCityGridId;//当前格子 id，默认 1, innercity_path.xlsx - id
        private int m_InnerCityRegionId;//已解锁大格子 id，默认 0, innercity_areaunlock.xlsx - id
        private int m_LoginContinueDays;//连续登录天数

        Dictionary<ulong, RoleBriefCacheData> m_roleBriefCache = new();
        class RoleBriefCacheData
        {
            public RoleBrief m_roleBrief; // 角色简要信息
            public ulong m_lastUpdateTime; // 最后更新时间戳，秒
        }
        private ulong m_roleBriefCacheTime = 5; // 角色简要信息缓存时间，秒

        public RoleData()
        {

        }

        public void Init(RoleInfo roleInfo)
        {
            if (roleInfo == null || roleInfo.Attr == null) return;

            RoleAttr roleAttr = roleInfo.Attr;

            m_roleID = roleInfo.Id;
            m_ServerId = roleAttr.ServerId;
            m_ServerName = roleAttr.ServerName;
            m_Name = roleAttr.Name;
            m_IsCustomAvatar = roleAttr.IsCustomAvatar;
            m_HeadSystemAvatar = roleAttr.HeadSystemAvatar;
            m_HeadCustomAvatar = roleAttr.HeadCustomAvatar;
            m_HeadBorder = roleAttr.HeadBorder;
            m_Power = roleAttr.Power;
            m_LastLoginAt = roleAttr.LastLoginAt;
            m_LastLogoutAt = roleAttr.LastLogoutAt;
            m_CreateAt = roleAttr.CreateAt;
            m_Lang = roleAttr.Lang;
            m_Gender = roleAttr.Gender;
            m_LoginContinueDays = roleAttr.LoginContinueDays;
        }

        // 使用属性 (Properties) 来访问和修改私有字段
        public ulong RoleID
        {
            get => m_roleID;
            set => m_roleID = value;
        }

        public uint ServerId
        {
            get => m_ServerId;
            set => m_ServerId = value;
        }

        public string ServerName
        {
            get => m_ServerName;
            set => m_ServerName = value;
        }

        public string Name
        {
            get => m_Name;
            set => m_Name = value;
        }

        public bool IsCustomAvatar
        {
            get => m_IsCustomAvatar;
            set => m_IsCustomAvatar = value;
        }

        public uint HeadSystemAvatar
        {
            get => m_HeadSystemAvatar;
            set => m_HeadSystemAvatar = value;
        }

        public string HeadCustomAvatar
        {
            get => m_HeadCustomAvatar;
            set => m_HeadCustomAvatar = value;
        }

        public uint HeadBorder
        {
            get => m_HeadBorder;
            set => m_HeadBorder = value;
        }

        public ulong Power
        {
            get => m_Power;
            set => m_Power = value;
        }

        public long LastLoginAt
        {
            get => m_LastLoginAt;
            set => m_LastLoginAt = value;
        }

        public long LastLogoutAt
        {
            get => m_LastLogoutAt;
            set => m_LastLogoutAt = value;
        }

        public long CreateAt
        {
            get => m_CreateAt;
            set => m_CreateAt = value;
        }

        public int Lang => m_Lang;
        public uint Gender => m_Gender;

        // 连续登录天数
        public int LoginContinueDays => m_LoginContinueDays;
        
        /// <summary>
        /// 指定角色进入游戏
        /// </summary>
        /// <param name="roleID">角色 ID</param>
        /// <param name="callback">回调</param>
        public void RequestRoleEnter(ulong roleID, Action<Role.RoleEnterResp> callback = null)
        {
            Role.RoleEnterReq req = new()
            {
                RoleId = roleID
            };

            GameEntry.LDLNet.Send(Protocol.MessageID.RoleEnter, req, (message) =>
            {
                Role.RoleEnterResp resp = message as Role.RoleEnterResp;
                callback?.Invoke(resp);
            });
        }

        /// <summary>
        /// 角色创建
        /// </summary>
        /// <param name="server_id">区服 ID</param>
        /// <param name="role_id">角色 ID</param>
        /// <param name="role_name">角色昵称</param>
        /// <param name="ip">IP 地址</param>
        /// <param name="callback">回调</param>
        public void RequestRoleCreate(uint server_id, ulong role_id, string role_name, string ip, Action<Role.RoleCreateResp> callback = null)
        {
            Role.RoleCreateReq req = new()
            {
                ServerId = server_id,
                RoleId = role_id,
                RoleName = role_name,
                Ip = ip
            };

            GameEntry.LDLNet.Send(Protocol.MessageID.RoleCreate, req, (message) =>
            {
                Role.RoleCreateResp resp = message as Role.RoleCreateResp;
                callback?.Invoke(resp);
            });
        }

        /// <summary>
        /// 查询本服角色信息(单个)
        /// </summary>
        /// <param name="roleId"></param>
        /// <param name="callback"></param>
        public void RequestRoleQueryLocalSingle(ulong roleId, Action<RoleBrief> callback = null)
        {
            List<ulong> roleIdList = new() { roleId };
            RequestRoleQueryLocal(roleIdList, (dic) =>
            {
                dic.TryGetValue(roleId, out RoleBrief roleBrief);
                callback?.Invoke(roleBrief);
            });
        }

        /// <summary>
        /// 查询本服角色信息
        /// </summary>
        /// <param name="roleIDList">角色 ID 列表</param>
        /// <param name="callback">回调</param>
        public void RequestRoleQueryLocal(List<ulong> roleIDList, Action<Dictionary<ulong, RoleBrief>> callback = null)
        {
            Dictionary<ulong, RoleBrief> dict = new();
            List<ulong> needRequestList = new();
            bool needRequest = false;
            foreach (var roleID in roleIDList)
            {
                if (m_roleBriefCache.ContainsKey(roleID))
                {
                    if (m_roleBriefCache[roleID].m_lastUpdateTime + m_roleBriefCacheTime < TimeComponent.Now)
                    {
                        needRequestList.Add(roleID);
                        needRequest = true;
                    }
                    else
                    {
                        dict.Add(roleID, m_roleBriefCache[roleID].m_roleBrief);
                    }
                }
                else
                {
                    needRequestList.Add(roleID);
                    needRequest = true;
                }
            }

            if (!needRequest)
            {
                ColorLog.Pink("角色信息缓存未过期，无需请求服务器");
                callback?.Invoke(dict);
                return;
            }

            Role.RoleQueryLocalReq req = new()
            {
                RoleIds = { needRequestList }
            };

            GameEntry.LDLNet.Send(Protocol.MessageID.RoleQueryLocal, req, (message) =>
            {
                if (message is Role.RoleQueryLocalResp resp && resp.Roles != null)
                {
                    foreach (var role in resp.Roles)
                    {
                        if (m_roleBriefCache.ContainsKey(role.Id))
                        {
                            RoleBriefCacheData cacheData = m_roleBriefCache[role.Id];
                            cacheData.m_roleBrief = role;
                            cacheData.m_lastUpdateTime = TimeComponent.Now;
                        }
                        else
                        {
                            m_roleBriefCache.Add(role.Id, new RoleBriefCacheData()
                            {
                                m_roleBrief = role,
                                m_lastUpdateTime = TimeComponent.Now,
                            });
                        }

                        if (needRequestList.Contains(role.Id))
                        {
                            dict.Add(role.Id, role);
                        }
                    }
                }

                callback?.Invoke(dict);
            });
        }

        /// <summary>
        /// 查询混服角色信息
        /// </summary>
        /// <param name="roleMetaList">角色元数据列表</param>
        /// <param name="callback">回调</param>
        public void RequestRoleQueryMulti(List<RoleMeta> roleMetaList, Action<Role.RoleQueryMultiResp> callback = null)
        {
            Role.RoleQueryMultiReq req = new()
            {
                List = { roleMetaList }
            };

            GameEntry.LDLNet.Send(Protocol.MessageID.RoleQueryMulti, req, (message) =>
            {
                Role.RoleQueryMultiResp resp = message as Role.RoleQueryMultiResp;
                callback?.Invoke(resp);
            });
        }
        
        
        /// <summary>
        /// 查询战力各个模块数据
        /// 1:英雄战力 2：无人机战力 3：士兵战力 4：建筑战力 5：科技战力
        /// </summary>
        /// <param name="callback">回调</param>
        public void RequestRoleQueryPowerBuild(Action<Role.RoleQueryPowerBuildResp> callback = null)
        {
            Role.RoleQueryPowerBuildReq req = new();
            GameEntry.LDLNet.Send(Protocol.MessageID.RoleQueryPowerBuild, req, (message) =>
            {
                var resp = (Role.RoleQueryPowerBuildResp)message;
                callback?.Invoke(resp);
            });
        }
    }
}
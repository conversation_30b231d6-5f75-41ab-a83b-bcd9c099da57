Using pre-set license
Built from '6000.0/china_unity/28f1' branch; Version is '6000.0.28f1c1 (a1337fc966e0) revision 10564479'; Using compiler version '193933523'; Build Type 'Release'
OS: 'Windows 11  (10.0.26100) 64bit Professional' Language: 'zh' Physical Memory: 32596 MB
BatchMode: 1, IsHumanControllingUs: 0, StartBugReporterOnCrash: 0, Is64bit: 1, IsPro: 1

COMMAND LINE ARGUMENTS:
D:\Program\Unity\Editor\6000.0.28f1c1\Editor\Unity.exe
-adb2
-batchMode
-noUpm
-name
AssetImportWorker3
-projectPath
D:/Project_LDL_Client/ldl-client/Client/Project_LDL
-logFile
Logs/AssetImportWorker3.log
-srvPort
1604
-job-worker-count
13
-background-job-worker-count
8
-asset-garbage-collectors
1
Successfully changed project path to: D:/Project_LDL_Client/ldl-client/Client/Project_LDL
D:/Project_LDL_Client/ldl-client/Client/Project_LDL
[UnityMemory] Configuration Parameters - Can be set up in boot.config
    "memorysetup-bucket-allocator-granularity=16"
    "memorysetup-bucket-allocator-bucket-count=8"
    "memorysetup-bucket-allocator-block-size=33554432"
    "memorysetup-bucket-allocator-block-count=8"
    "memorysetup-main-allocator-block-size=16777216"
    "memorysetup-thread-allocator-block-size=16777216"
    "memorysetup-gfx-main-allocator-block-size=16777216"
    "memorysetup-gfx-thread-allocator-block-size=16777216"
    "memorysetup-cache-allocator-block-size=4194304"
    "memorysetup-typetree-allocator-block-size=2097152"
    "memorysetup-profiler-bucket-allocator-granularity=16"
    "memorysetup-profiler-bucket-allocator-bucket-count=8"
    "memorysetup-profiler-bucket-allocator-block-size=33554432"
    "memorysetup-profiler-bucket-allocator-block-count=8"
    "memorysetup-profiler-allocator-block-size=16777216"
    "memorysetup-profiler-editor-allocator-block-size=1048576"
    "memorysetup-temp-allocator-size-main=16777216"
    "memorysetup-job-temp-allocator-block-size=2097152"
    "memorysetup-job-temp-allocator-block-size-background=1048576"
    "memorysetup-job-temp-allocator-reduction-small-platforms=262144"
    "memorysetup-allocator-temp-initial-block-size-main=262144"
    "memorysetup-allocator-temp-initial-block-size-worker=262144"
    "memorysetup-temp-allocator-size-background-worker=32768"
    "memorysetup-temp-allocator-size-job-worker=262144"
    "memorysetup-temp-allocator-size-preload-manager=33554432"
    "memorysetup-temp-allocator-size-nav-mesh-worker=65536"
    "memorysetup-temp-allocator-size-audio-worker=65536"
    "memorysetup-temp-allocator-size-cloud-worker=32768"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gfx=262144"
Player connection [47372]  Target information:

Player connection [47372]  * "[IP] ************* [Port] 0 [Flags] 2 [Guid] 4038520310 [EditorId] 4038520310 [Version] 1048832 [Id] WindowsEditor(7,DESKTOP-ORSLCSS) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [47372] Host joined multi-casting on [***********:54997]...
Player connection [47372] Host joined alternative multi-casting on [***********:34997]...
JobSystem: Creating JobQueue using job-worker-count value 13
[Physics::Module] Initialized fallback backend.
[Physics::Module] Id: 0xdecafbad
Library Redirect Path: Library/
[Physics::Module] Selected backend.
[Physics::Module] Name: PhysX
[Physics::Module] Id: 0xf2b8ea05
[Physics::Module] SDK Version: 4.1.2
[Physics::Module] Integration Version: 1.0.0
[Physics::Module] Threading Mode: Multi-Threaded
Refreshing native plugins compatible for Editor in 100.79 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Initialize engine version: 6000.0.28f1c1 (a1337fc966e0)
[Subsystems] Discovering subsystems at path D:/Program/Unity/Editor/6000.0.28f1c1/Editor/Data/Resources/UnitySubsystems
[Subsystems] Discovering subsystems at path D:/Project_LDL_Client/ldl-client/Client/Project_LDL/Assets
GfxDevice: creating device client; kGfxThreadingModeNonThreaded
Direct3D:
    Version:  Direct3D 11.0 [level 11.1]
    Renderer: NVIDIA GeForce RTX 4060 (ID=0x2808)
    Vendor:   NVIDIA
    VRAM:     7957 MB
    Driver:   32.0.15.6094
Initialize mono
Mono path[0] = 'D:/Program/Unity/Editor/6000.0.28f1c1/Editor/Data/Managed'
Mono path[1] = 'D:/Program/Unity/Editor/6000.0.28f1c1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32'
Mono config path = 'D:/Program/Unity/Editor/6000.0.28f1c1/Editor/Data/MonoBleedingEdge/etc'
Using monoOptions --debugger-agent=transport=dt_socket,embedding=1,server=y,suspend=n,address=127.0.0.1:56476
Begin MonoManager ReloadAssembly
Registering precompiled unity dll's ...
Register platform support module: D:/Program/Unity/Editor/6000.0.28f1c1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/UnityEditor.WindowsStandalone.Extensions.dll
Register platform support module: D:/Program/Unity/Editor/6000.0.28f1c1/Editor/Data/PlaybackEngines/AndroidPlayer/UnityEditor.Android.Extensions.dll
Registered in 0.001216 seconds.
- Loaded All Assemblies, in  0.615 seconds
Native extension for Android target not found
Android Extension - Scanning For ADB Devices 1703 ms
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  2.006 seconds
Domain Reload Profiling: 2621ms
	BeginReloadAssembly (100ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (0ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (0ms)
	RebuildCommonClasses (115ms)
	RebuildNativeTypeToScriptingClass (8ms)
	initialDomainReloadingComplete (31ms)
	LoadAllAssembliesAndSetupDomain (361ms)
		LoadAssemblies (99ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (359ms)
			TypeCache.Refresh (357ms)
				TypeCache.ScanAssembly (348ms)
			BuildScriptInfoCaches (0ms)
			ResolveRequiredComponents (0ms)
	FinalizeReload (2006ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1965ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (1765ms)
			SetLoadedEditorAssemblies (3ms)
			BeforeProcessingInitializeOnLoad (4ms)
			ProcessInitializeOnLoadAttributes (104ms)
			ProcessInitializeOnLoadMethodAttributes (62ms)
			AfterProcessingInitializeOnLoad (27ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (0ms)
========================================================================
Worker process is ready to serve import requests
Import Worker Mode flag is 0x00
Begin MonoManager ReloadAssembly
Duplicate assembly 'System.Runtime.CompilerServices.Unsafe.dll' with different versions detected, using 'Packages/com.bezi.sidekick/Plugins/System.Runtime.CompilerServices.Unsafe.dll, AssemblyName=System.Runtime.CompilerServices.Unsafe, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a' and ignoring 'Assets/Plugins/Protobuf/System.Runtime.CompilerServices.Unsafe.dll, AssemblyName=System.Runtime.CompilerServices.Unsafe, Version=4.0.4.1, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a'.Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  1.562 seconds
OnLevelWasLoaded was found on DOTweenComponent
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
Refreshing native plugins compatible for Editor in 0.59 ms, found 3 plugins.
OnLevelWasLoaded was found on DOTweenComponent
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Package Manager log level set to [2]
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Refreshing native plugins compatible for Editor in 1.69 ms, found 3 plugins.
SceneCameraResetButton: Initialized.
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:Log (object)
SceneCameraResetButton:.cctor () (at Assets/Editor/Tools/SceneCameraResetButton.cs:15)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: Assets/Editor/Tools/SceneCameraResetButton.cs Line: 15)

Launched and connected shader compiler UnityShaderCompiler.exe after 0.01 seconds
The project currently uses the compatibility mode where the Render Graph API is disabled. Support for this mode will be removed in future Unity versions. Migrate existing ScriptableRenderPasses to the new RenderGraph API. After the migration, disable the compatibility mode in Edit > Projects Settings > Graphics > Render Graph.
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogWarning (object)
UnityEngine.Rendering.Universal.WarnUsingNonRenderGraph:EmitConsoleWarning () (at ./Packages/com.unity.render-pipelines.universal/Runtime/Data/UniversalRenderPipelineAsset.cs:427)
System.Reflection.RuntimeMethodInfo:Invoke (object,System.Reflection.BindingFlags,System.Reflection.Binder,object[],System.Globalization.CultureInfo)
System.Reflection.MethodBase:Invoke (object,object[])
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadMethodAttributes ()

(Filename: ./Packages/com.unity.render-pipelines.universal/Runtime/Data/UniversalRenderPipelineAsset.cs Line: 427)

Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.662 seconds
Domain Reload Profiling: 3223ms
	BeginReloadAssembly (509ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (4ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (27ms)
	RebuildCommonClasses (30ms)
	RebuildNativeTypeToScriptingClass (9ms)
	initialDomainReloadingComplete (49ms)
	LoadAllAssembliesAndSetupDomain (964ms)
		LoadAssemblies (559ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (492ms)
			TypeCache.Refresh (384ms)
				TypeCache.ScanAssembly (357ms)
			BuildScriptInfoCaches (80ms)
			ResolveRequiredComponents (21ms)
	FinalizeReload (1663ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1433ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (12ms)
			SetLoadedEditorAssemblies (8ms)
			BeforeProcessingInitializeOnLoad (148ms)
			ProcessInitializeOnLoadAttributes (788ms)
			ProcessInitializeOnLoadMethodAttributes (474ms)
			AfterProcessingInitializeOnLoad (3ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (4ms)
Refreshing native plugins compatible for Editor in 1.41 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 504 Unused Serialized files (Serialized files now loaded: 0)
Unloading 8934 unused Assets / (8.5 MB). Loaded Objects now: 10060.
Memory consumption went from 317.7 MB to 309.1 MB.
Total: 36.886700 ms (FindLiveObjects: 1.547700 ms CreateObjectMapping: 1.288500 ms MarkObjects: 27.888400 ms  DeleteObjects: 6.159000 ms)

========================================================================
Received Import Request.
  Time since last request: 92230.138701 seconds.
  path: Assets/ResPackage/Sprite/ui_maoyi/maoyi_huoche_chexiang5.png
  artifactKey: Guid(d0f051eeb9f25c74c94f5da7261a16c2) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/ResPackage/Sprite/ui_maoyi/maoyi_huoche_chexiang5.png using Guid(d0f051eeb9f25c74c94f5da7261a16c2) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '64b0586ee9640b89b377d83ce32a1a76') in 0.0575824 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Prepare
OnLevelWasLoaded was found on DOTweenComponent
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
Refreshing native plugins compatible for Editor in 1.43 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 29 Unused Serialized files (Serialized files now loaded: 0)
Unloading 8921 unused Assets / (8.5 MB). Loaded Objects now: 10070.
Memory consumption went from 218.8 MB to 210.2 MB.
Total: 33.655000 ms (FindLiveObjects: 0.849200 ms CreateObjectMapping: 0.751800 ms MarkObjects: 26.257800 ms  DeleteObjects: 5.794700 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Duplicate assembly 'System.Runtime.CompilerServices.Unsafe.dll' with different versions detected, using 'Packages/com.bezi.sidekick/Plugins/System.Runtime.CompilerServices.Unsafe.dll, AssemblyName=System.Runtime.CompilerServices.Unsafe, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a' and ignoring 'Assets/Plugins/Protobuf/System.Runtime.CompilerServices.Unsafe.dll, AssemblyName=System.Runtime.CompilerServices.Unsafe, Version=4.0.4.1, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a'.Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.938 seconds
OnLevelWasLoaded was found on DOTweenComponent
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
Refreshing native plugins compatible for Editor in 0.66 ms, found 3 plugins.
OnLevelWasLoaded was found on DOTweenComponent
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
Native extension for Android target not found
Native extension for WindowsStandalone target not found
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Refreshing native plugins compatible for Editor in 0.83 ms, found 3 plugins.
SceneCameraResetButton: Initialized.
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:Log (object)
SceneCameraResetButton:.cctor () (at Assets/Editor/Tools/SceneCameraResetButton.cs:15)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: Assets/Editor/Tools/SceneCameraResetButton.cs Line: 15)

The project currently uses the compatibility mode where the Render Graph API is disabled. Support for this mode will be removed in future Unity versions. Migrate existing ScriptableRenderPasses to the new RenderGraph API. After the migration, disable the compatibility mode in Edit > Projects Settings > Graphics > Render Graph.
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogWarning (object)
UnityEngine.Rendering.Universal.WarnUsingNonRenderGraph:EmitConsoleWarning () (at ./Packages/com.unity.render-pipelines.universal/Runtime/Data/UniversalRenderPipelineAsset.cs:427)
System.Reflection.RuntimeMethodInfo:Invoke (object,System.Reflection.BindingFlags,System.Reflection.Binder,object[],System.Globalization.CultureInfo)
System.Reflection.MethodBase:Invoke (object,object[])
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadMethodAttributes ()

(Filename: ./Packages/com.unity.render-pipelines.universal/Runtime/Data/UniversalRenderPipelineAsset.cs Line: 427)

Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.326 seconds
Domain Reload Profiling: 2269ms
	BeginReloadAssembly (195ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (7ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (82ms)
	RebuildCommonClasses (26ms)
	RebuildNativeTypeToScriptingClass (8ms)
	initialDomainReloadingComplete (41ms)
	LoadAllAssembliesAndSetupDomain (672ms)
		LoadAssemblies (385ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (353ms)
			TypeCache.Refresh (41ms)
				TypeCache.ScanAssembly (27ms)
			BuildScriptInfoCaches (285ms)
			ResolveRequiredComponents (19ms)
	FinalizeReload (1327ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1066ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (9ms)
			SetLoadedEditorAssemblies (5ms)
			BeforeProcessingInitializeOnLoad (121ms)
			ProcessInitializeOnLoadAttributes (707ms)
			ProcessInitializeOnLoadMethodAttributes (221ms)
			AfterProcessingInitializeOnLoad (3ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (10ms)
Refreshing native plugins compatible for Editor in 1.54 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 38 Unused Serialized files (Serialized files now loaded: 0)
Unloading 8934 unused Assets / (7.6 MB). Loaded Objects now: 10083.
Memory consumption went from 234.1 MB to 226.4 MB.
Total: 30.387200 ms (FindLiveObjects: 0.914000 ms CreateObjectMapping: 0.755600 ms MarkObjects: 24.099800 ms  DeleteObjects: 4.616100 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
OnLevelWasLoaded was found on DOTweenComponent
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
Refreshing native plugins compatible for Editor in 1.65 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 29 Unused Serialized files (Serialized files now loaded: 0)
Unloading 8919 unused Assets / (6.6 MB). Loaded Objects now: 10081.
Memory consumption went from 232.7 MB to 226.2 MB.
Total: 26.392100 ms (FindLiveObjects: 0.836000 ms CreateObjectMapping: 0.633700 ms MarkObjects: 21.914500 ms  DeleteObjects: 3.006500 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Duplicate assembly 'System.Runtime.CompilerServices.Unsafe.dll' with different versions detected, using 'Packages/com.bezi.sidekick/Plugins/System.Runtime.CompilerServices.Unsafe.dll, AssemblyName=System.Runtime.CompilerServices.Unsafe, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a' and ignoring 'Assets/Plugins/Protobuf/System.Runtime.CompilerServices.Unsafe.dll, AssemblyName=System.Runtime.CompilerServices.Unsafe, Version=4.0.4.1, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a'.Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.846 seconds
OnLevelWasLoaded was found on DOTweenComponent
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
Refreshing native plugins compatible for Editor in 0.57 ms, found 3 plugins.
OnLevelWasLoaded was found on DOTweenComponent
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
Native extension for Android target not found
Native extension for WindowsStandalone target not found
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Refreshing native plugins compatible for Editor in 0.76 ms, found 3 plugins.
SceneCameraResetButton: Initialized.
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:Log (object)
SceneCameraResetButton:.cctor () (at Assets/Editor/Tools/SceneCameraResetButton.cs:15)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: Assets/Editor/Tools/SceneCameraResetButton.cs Line: 15)

The project currently uses the compatibility mode where the Render Graph API is disabled. Support for this mode will be removed in future Unity versions. Migrate existing ScriptableRenderPasses to the new RenderGraph API. After the migration, disable the compatibility mode in Edit > Projects Settings > Graphics > Render Graph.
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogWarning (object)
UnityEngine.Rendering.Universal.WarnUsingNonRenderGraph:EmitConsoleWarning () (at ./Packages/com.unity.render-pipelines.universal/Runtime/Data/UniversalRenderPipelineAsset.cs:427)
System.Reflection.RuntimeMethodInfo:Invoke (object,System.Reflection.BindingFlags,System.Reflection.Binder,object[],System.Globalization.CultureInfo)
System.Reflection.MethodBase:Invoke (object,object[])
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadMethodAttributes ()

(Filename: ./Packages/com.unity.render-pipelines.universal/Runtime/Data/UniversalRenderPipelineAsset.cs Line: 427)

Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.085 seconds
Domain Reload Profiling: 1935ms
	BeginReloadAssembly (175ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (4ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (65ms)
	RebuildCommonClasses (27ms)
	RebuildNativeTypeToScriptingClass (9ms)
	initialDomainReloadingComplete (41ms)
	LoadAllAssembliesAndSetupDomain (598ms)
		LoadAssemblies (379ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (292ms)
			TypeCache.Refresh (35ms)
				TypeCache.ScanAssembly (23ms)
			BuildScriptInfoCaches (236ms)
			ResolveRequiredComponents (15ms)
	FinalizeReload (1086ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (831ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (10ms)
			SetLoadedEditorAssemblies (5ms)
			BeforeProcessingInitializeOnLoad (99ms)
			ProcessInitializeOnLoadAttributes (523ms)
			ProcessInitializeOnLoadMethodAttributes (190ms)
			AfterProcessingInitializeOnLoad (5ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (10ms)
Refreshing native plugins compatible for Editor in 1.34 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 29 Unused Serialized files (Serialized files now loaded: 0)
Unloading 8932 unused Assets / (8.2 MB). Loaded Objects now: 10083.
Memory consumption went from 232.5 MB to 224.3 MB.
Total: 29.393000 ms (FindLiveObjects: 0.839200 ms CreateObjectMapping: 0.703400 ms MarkObjects: 23.569300 ms  DeleteObjects: 4.279700 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
OnLevelWasLoaded was found on DOTweenComponent
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
Refreshing native plugins compatible for Editor in 1.37 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 29 Unused Serialized files (Serialized files now loaded: 0)
Unloading 8921 unused Assets / (7.3 MB). Loaded Objects now: 10085.
Memory consumption went from 232.7 MB to 225.3 MB.
Total: 28.793000 ms (FindLiveObjects: 0.826300 ms CreateObjectMapping: 0.724200 ms MarkObjects: 23.912800 ms  DeleteObjects: 3.327700 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Duplicate assembly 'System.Runtime.CompilerServices.Unsafe.dll' with different versions detected, using 'Packages/com.bezi.sidekick/Plugins/System.Runtime.CompilerServices.Unsafe.dll, AssemblyName=System.Runtime.CompilerServices.Unsafe, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a' and ignoring 'Assets/Plugins/Protobuf/System.Runtime.CompilerServices.Unsafe.dll, AssemblyName=System.Runtime.CompilerServices.Unsafe, Version=4.0.4.1, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a'.Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.835 seconds
OnLevelWasLoaded was found on DOTweenComponent
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
Refreshing native plugins compatible for Editor in 0.64 ms, found 3 plugins.
OnLevelWasLoaded was found on DOTweenComponent
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
Native extension for Android target not found
Native extension for WindowsStandalone target not found
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Refreshing native plugins compatible for Editor in 0.87 ms, found 3 plugins.
SceneCameraResetButton: Initialized.
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:Log (object)
SceneCameraResetButton:.cctor () (at Assets/Editor/Tools/SceneCameraResetButton.cs:15)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: Assets/Editor/Tools/SceneCameraResetButton.cs Line: 15)

The project currently uses the compatibility mode where the Render Graph API is disabled. Support for this mode will be removed in future Unity versions. Migrate existing ScriptableRenderPasses to the new RenderGraph API. After the migration, disable the compatibility mode in Edit > Projects Settings > Graphics > Render Graph.
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogWarning (object)
UnityEngine.Rendering.Universal.WarnUsingNonRenderGraph:EmitConsoleWarning () (at ./Packages/com.unity.render-pipelines.universal/Runtime/Data/UniversalRenderPipelineAsset.cs:427)
System.Reflection.RuntimeMethodInfo:Invoke (object,System.Reflection.BindingFlags,System.Reflection.Binder,object[],System.Globalization.CultureInfo)
System.Reflection.MethodBase:Invoke (object,object[])
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadMethodAttributes ()

(Filename: ./Packages/com.unity.render-pipelines.universal/Runtime/Data/UniversalRenderPipelineAsset.cs Line: 427)

Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.961 seconds
Domain Reload Profiling: 1803ms
	BeginReloadAssembly (152ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (4ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (55ms)
	RebuildCommonClasses (23ms)
	RebuildNativeTypeToScriptingClass (8ms)
	initialDomainReloadingComplete (36ms)
	LoadAllAssembliesAndSetupDomain (624ms)
		LoadAssemblies (357ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (325ms)
			TypeCache.Refresh (38ms)
				TypeCache.ScanAssembly (26ms)
			BuildScriptInfoCaches (262ms)
			ResolveRequiredComponents (16ms)
	FinalizeReload (961ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (755ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (8ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (88ms)
			ProcessInitializeOnLoadAttributes (480ms)
			ProcessInitializeOnLoadMethodAttributes (172ms)
			AfterProcessingInitializeOnLoad (3ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (9ms)
Refreshing native plugins compatible for Editor in 1.42 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 29 Unused Serialized files (Serialized files now loaded: 0)
Unloading 8934 unused Assets / (7.6 MB). Loaded Objects now: 10087.
Memory consumption went from 232.5 MB to 224.9 MB.
Total: 29.146100 ms (FindLiveObjects: 0.823300 ms CreateObjectMapping: 0.619200 ms MarkObjects: 23.775300 ms  DeleteObjects: 3.927100 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
OnLevelWasLoaded was found on DOTweenComponent
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
Refreshing native plugins compatible for Editor in 1.53 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 29 Unused Serialized files (Serialized files now loaded: 0)
Unloading 8921 unused Assets / (6.8 MB). Loaded Objects now: 10087.
Memory consumption went from 232.6 MB to 225.8 MB.
Total: 29.232500 ms (FindLiveObjects: 0.788700 ms CreateObjectMapping: 0.767000 ms MarkObjects: 24.584700 ms  DeleteObjects: 3.090800 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Duplicate assembly 'System.Runtime.CompilerServices.Unsafe.dll' with different versions detected, using 'Packages/com.bezi.sidekick/Plugins/System.Runtime.CompilerServices.Unsafe.dll, AssemblyName=System.Runtime.CompilerServices.Unsafe, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a' and ignoring 'Assets/Plugins/Protobuf/System.Runtime.CompilerServices.Unsafe.dll, AssemblyName=System.Runtime.CompilerServices.Unsafe, Version=4.0.4.1, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a'.Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.808 seconds
OnLevelWasLoaded was found on DOTweenComponent
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
Refreshing native plugins compatible for Editor in 0.75 ms, found 3 plugins.
OnLevelWasLoaded was found on DOTweenComponent
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
Assertion failed on expression: 'pred(*previous, *i)'
Native extension for Android target not found
Native extension for WindowsStandalone target not found
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Refreshing native plugins compatible for Editor in 1.63 ms, found 3 plugins.
SceneCameraResetButton: Initialized.
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:Log (object)
SceneCameraResetButton:.cctor () (at Assets/Editor/Tools/SceneCameraResetButton.cs:15)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: Assets/Editor/Tools/SceneCameraResetButton.cs Line: 15)

The project currently uses the compatibility mode where the Render Graph API is disabled. Support for this mode will be removed in future Unity versions. Migrate existing ScriptableRenderPasses to the new RenderGraph API. After the migration, disable the compatibility mode in Edit > Projects Settings > Graphics > Render Graph.
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogWarning (object)
UnityEngine.Rendering.Universal.WarnUsingNonRenderGraph:EmitConsoleWarning () (at ./Packages/com.unity.render-pipelines.universal/Runtime/Data/UniversalRenderPipelineAsset.cs:427)
System.Reflection.RuntimeMethodInfo:Invoke (object,System.Reflection.BindingFlags,System.Reflection.Binder,object[],System.Globalization.CultureInfo)
System.Reflection.MethodBase:Invoke (object,object[])
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadMethodAttributes ()

(Filename: ./Packages/com.unity.render-pipelines.universal/Runtime/Data/UniversalRenderPipelineAsset.cs Line: 427)

Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.201 seconds
Domain Reload Profiling: 2014ms
	BeginReloadAssembly (149ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (4ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (52ms)
	RebuildCommonClasses (22ms)
	RebuildNativeTypeToScriptingClass (9ms)
	initialDomainReloadingComplete (37ms)
	LoadAllAssembliesAndSetupDomain (594ms)
		LoadAssemblies (350ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (306ms)
			TypeCache.Refresh (41ms)
				TypeCache.ScanAssembly (27ms)
			BuildScriptInfoCaches (241ms)
			ResolveRequiredComponents (18ms)
	FinalizeReload (1202ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (956ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (8ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (104ms)
			ProcessInitializeOnLoadAttributes (644ms)
			ProcessInitializeOnLoadMethodAttributes (192ms)
			AfterProcessingInitializeOnLoad (4ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (13ms)
Refreshing native plugins compatible for Editor in 1.45 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 29 Unused Serialized files (Serialized files now loaded: 0)
Unloading 8934 unused Assets / (8.6 MB). Loaded Objects now: 10089.
Memory consumption went from 232.6 MB to 224.0 MB.
Total: 33.037100 ms (FindLiveObjects: 0.961200 ms CreateObjectMapping: 0.855400 ms MarkObjects: 25.761800 ms  DeleteObjects: 5.456600 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
OnLevelWasLoaded was found on DOTweenComponent
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
Refreshing native plugins compatible for Editor in 1.61 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 29 Unused Serialized files (Serialized files now loaded: 0)
Unloading 8921 unused Assets / (8.3 MB). Loaded Objects now: 10089.
Memory consumption went from 232.6 MB to 224.4 MB.
Total: 33.083400 ms (FindLiveObjects: 1.002900 ms CreateObjectMapping: 0.906800 ms MarkObjects: 25.791200 ms  DeleteObjects: 5.380700 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Duplicate assembly 'System.Runtime.CompilerServices.Unsafe.dll' with different versions detected, using 'Packages/com.bezi.sidekick/Plugins/System.Runtime.CompilerServices.Unsafe.dll, AssemblyName=System.Runtime.CompilerServices.Unsafe, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a' and ignoring 'Assets/Plugins/Protobuf/System.Runtime.CompilerServices.Unsafe.dll, AssemblyName=System.Runtime.CompilerServices.Unsafe, Version=4.0.4.1, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a'.Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.800 seconds
OnLevelWasLoaded was found on DOTweenComponent
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
Refreshing native plugins compatible for Editor in 0.65 ms, found 3 plugins.
OnLevelWasLoaded was found on DOTweenComponent
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
Assertion failed on expression: 'pred(*previous, *i)'
Native extension for Android target not found
Native extension for WindowsStandalone target not found
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Refreshing native plugins compatible for Editor in 0.84 ms, found 3 plugins.
SceneCameraResetButton: Initialized.
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:Log (object)
SceneCameraResetButton:.cctor () (at Assets/Editor/Tools/SceneCameraResetButton.cs:15)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: Assets/Editor/Tools/SceneCameraResetButton.cs Line: 15)

The project currently uses the compatibility mode where the Render Graph API is disabled. Support for this mode will be removed in future Unity versions. Migrate existing ScriptableRenderPasses to the new RenderGraph API. After the migration, disable the compatibility mode in Edit > Projects Settings > Graphics > Render Graph.
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogWarning (object)
UnityEngine.Rendering.Universal.WarnUsingNonRenderGraph:EmitConsoleWarning () (at ./Packages/com.unity.render-pipelines.universal/Runtime/Data/UniversalRenderPipelineAsset.cs:427)
System.Reflection.RuntimeMethodInfo:Invoke (object,System.Reflection.BindingFlags,System.Reflection.Binder,object[],System.Globalization.CultureInfo)
System.Reflection.MethodBase:Invoke (object,object[])
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadMethodAttributes ()

(Filename: ./Packages/com.unity.render-pipelines.universal/Runtime/Data/UniversalRenderPipelineAsset.cs Line: 427)

Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.923 seconds
Domain Reload Profiling: 1728ms
	BeginReloadAssembly (143ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (4ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (50ms)
	RebuildCommonClasses (22ms)
	RebuildNativeTypeToScriptingClass (8ms)
	initialDomainReloadingComplete (36ms)
	LoadAllAssembliesAndSetupDomain (596ms)
		LoadAssemblies (355ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (299ms)
			TypeCache.Refresh (37ms)
				TypeCache.ScanAssembly (22ms)
			BuildScriptInfoCaches (240ms)
			ResolveRequiredComponents (14ms)
	FinalizeReload (923ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (720ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (8ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (98ms)
			ProcessInitializeOnLoadAttributes (454ms)
			ProcessInitializeOnLoadMethodAttributes (153ms)
			AfterProcessingInitializeOnLoad (3ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (8ms)
Refreshing native plugins compatible for Editor in 1.28 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 29 Unused Serialized files (Serialized files now loaded: 0)
Unloading 8934 unused Assets / (7.6 MB). Loaded Objects now: 10091.
Memory consumption went from 232.4 MB to 224.8 MB.
Total: 24.979900 ms (FindLiveObjects: 1.009800 ms CreateObjectMapping: 0.650300 ms MarkObjects: 20.092800 ms  DeleteObjects: 3.225700 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Duplicate assembly 'System.Runtime.CompilerServices.Unsafe.dll' with different versions detected, using 'Packages/com.bezi.sidekick/Plugins/System.Runtime.CompilerServices.Unsafe.dll, AssemblyName=System.Runtime.CompilerServices.Unsafe, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a' and ignoring 'Assets/Plugins/Protobuf/System.Runtime.CompilerServices.Unsafe.dll, AssemblyName=System.Runtime.CompilerServices.Unsafe, Version=4.0.4.1, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a'.Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.851 seconds
OnLevelWasLoaded was found on DOTweenComponent
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
Refreshing native plugins compatible for Editor in 0.78 ms, found 3 plugins.
OnLevelWasLoaded was found on DOTweenComponent
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
Assertion failed on expression: 'pred(*previous, *i)'
Native extension for Android target not found
Native extension for WindowsStandalone target not found
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Refreshing native plugins compatible for Editor in 0.80 ms, found 3 plugins.
SceneCameraResetButton: Initialized.
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:Log (object)
SceneCameraResetButton:.cctor () (at Assets/Editor/Tools/SceneCameraResetButton.cs:15)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: Assets/Editor/Tools/SceneCameraResetButton.cs Line: 15)

The project currently uses the compatibility mode where the Render Graph API is disabled. Support for this mode will be removed in future Unity versions. Migrate existing ScriptableRenderPasses to the new RenderGraph API. After the migration, disable the compatibility mode in Edit > Projects Settings > Graphics > Render Graph.
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogWarning (object)
UnityEngine.Rendering.Universal.WarnUsingNonRenderGraph:EmitConsoleWarning () (at ./Packages/com.unity.render-pipelines.universal/Runtime/Data/UniversalRenderPipelineAsset.cs:427)
System.Reflection.RuntimeMethodInfo:Invoke (object,System.Reflection.BindingFlags,System.Reflection.Binder,object[],System.Globalization.CultureInfo)
System.Reflection.MethodBase:Invoke (object,object[])
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadMethodAttributes ()

(Filename: ./Packages/com.unity.render-pipelines.universal/Runtime/Data/UniversalRenderPipelineAsset.cs Line: 427)

Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.921 seconds
Domain Reload Profiling: 1777ms
	BeginReloadAssembly (175ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (5ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (63ms)
	RebuildCommonClasses (29ms)
	RebuildNativeTypeToScriptingClass (8ms)
	initialDomainReloadingComplete (40ms)
	LoadAllAssembliesAndSetupDomain (603ms)
		LoadAssemblies (370ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (307ms)
			TypeCache.Refresh (11ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (272ms)
			ResolveRequiredComponents (15ms)
	FinalizeReload (922ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (712ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (7ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (87ms)
			ProcessInitializeOnLoadAttributes (472ms)
			ProcessInitializeOnLoadMethodAttributes (139ms)
			AfterProcessingInitializeOnLoad (3ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (8ms)
Refreshing native plugins compatible for Editor in 1.35 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 29 Unused Serialized files (Serialized files now loaded: 0)
Unloading 8934 unused Assets / (7.4 MB). Loaded Objects now: 10093.
Memory consumption went from 232.5 MB to 225.1 MB.
Total: 26.278600 ms (FindLiveObjects: 0.894600 ms CreateObjectMapping: 0.709000 ms MarkObjects: 21.262800 ms  DeleteObjects: 3.411000 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Duplicate assembly 'System.Runtime.CompilerServices.Unsafe.dll' with different versions detected, using 'Packages/com.bezi.sidekick/Plugins/System.Runtime.CompilerServices.Unsafe.dll, AssemblyName=System.Runtime.CompilerServices.Unsafe, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a' and ignoring 'Assets/Plugins/Protobuf/System.Runtime.CompilerServices.Unsafe.dll, AssemblyName=System.Runtime.CompilerServices.Unsafe, Version=4.0.4.1, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a'.Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.795 seconds
OnLevelWasLoaded was found on DOTweenComponent
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
Refreshing native plugins compatible for Editor in 0.83 ms, found 3 plugins.
OnLevelWasLoaded was found on DOTweenComponent
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
Assertion failed on expression: 'pred(*previous, *i)'
Native extension for Android target not found
Native extension for WindowsStandalone target not found
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Refreshing native plugins compatible for Editor in 0.88 ms, found 3 plugins.
SceneCameraResetButton: Initialized.
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:Log (object)
SceneCameraResetButton:.cctor () (at Assets/Editor/Tools/SceneCameraResetButton.cs:15)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: Assets/Editor/Tools/SceneCameraResetButton.cs Line: 15)

The project currently uses the compatibility mode where the Render Graph API is disabled. Support for this mode will be removed in future Unity versions. Migrate existing ScriptableRenderPasses to the new RenderGraph API. After the migration, disable the compatibility mode in Edit > Projects Settings > Graphics > Render Graph.
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogWarning (object)
UnityEngine.Rendering.Universal.WarnUsingNonRenderGraph:EmitConsoleWarning () (at ./Packages/com.unity.render-pipelines.universal/Runtime/Data/UniversalRenderPipelineAsset.cs:427)
System.Reflection.RuntimeMethodInfo:Invoke (object,System.Reflection.BindingFlags,System.Reflection.Binder,object[],System.Globalization.CultureInfo)
System.Reflection.MethodBase:Invoke (object,object[])
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadMethodAttributes ()

(Filename: ./Packages/com.unity.render-pipelines.universal/Runtime/Data/UniversalRenderPipelineAsset.cs Line: 427)

Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.972 seconds
Domain Reload Profiling: 1771ms
	BeginReloadAssembly (149ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (4ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (52ms)
	RebuildCommonClasses (24ms)
	RebuildNativeTypeToScriptingClass (8ms)
	initialDomainReloadingComplete (35ms)
	LoadAllAssembliesAndSetupDomain (582ms)
		LoadAssemblies (360ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (284ms)
			TypeCache.Refresh (9ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (256ms)
			ResolveRequiredComponents (13ms)
	FinalizeReload (973ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (764ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (7ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (101ms)
			ProcessInitializeOnLoadAttributes (506ms)
			ProcessInitializeOnLoadMethodAttributes (143ms)
			AfterProcessingInitializeOnLoad (3ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (8ms)
Refreshing native plugins compatible for Editor in 1.30 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 29 Unused Serialized files (Serialized files now loaded: 0)
Unloading 8934 unused Assets / (7.7 MB). Loaded Objects now: 10095.
Memory consumption went from 234.5 MB to 226.8 MB.
Total: 26.613100 ms (FindLiveObjects: 0.879600 ms CreateObjectMapping: 0.673800 ms MarkObjects: 20.875700 ms  DeleteObjects: 4.182400 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Duplicate assembly 'System.Runtime.CompilerServices.Unsafe.dll' with different versions detected, using 'Packages/com.bezi.sidekick/Plugins/System.Runtime.CompilerServices.Unsafe.dll, AssemblyName=System.Runtime.CompilerServices.Unsafe, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a' and ignoring 'Assets/Plugins/Protobuf/System.Runtime.CompilerServices.Unsafe.dll, AssemblyName=System.Runtime.CompilerServices.Unsafe, Version=4.0.4.1, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a'.Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.747 seconds
OnLevelWasLoaded was found on DOTweenComponent
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
Refreshing native plugins compatible for Editor in 0.71 ms, found 3 plugins.
OnLevelWasLoaded was found on DOTweenComponent
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
Assertion failed on expression: 'pred(*previous, *i)'
Native extension for Android target not found
Native extension for WindowsStandalone target not found
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Refreshing native plugins compatible for Editor in 0.84 ms, found 3 plugins.
SceneCameraResetButton: Initialized.
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:Log (object)
SceneCameraResetButton:.cctor () (at Assets/Editor/Tools/SceneCameraResetButton.cs:15)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: Assets/Editor/Tools/SceneCameraResetButton.cs Line: 15)

The project currently uses the compatibility mode where the Render Graph API is disabled. Support for this mode will be removed in future Unity versions. Migrate existing ScriptableRenderPasses to the new RenderGraph API. After the migration, disable the compatibility mode in Edit > Projects Settings > Graphics > Render Graph.
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogWarning (object)
UnityEngine.Rendering.Universal.WarnUsingNonRenderGraph:EmitConsoleWarning () (at ./Packages/com.unity.render-pipelines.universal/Runtime/Data/UniversalRenderPipelineAsset.cs:427)
System.Reflection.RuntimeMethodInfo:Invoke (object,System.Reflection.BindingFlags,System.Reflection.Binder,object[],System.Globalization.CultureInfo)
System.Reflection.MethodBase:Invoke (object,object[])
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadMethodAttributes ()

(Filename: ./Packages/com.unity.render-pipelines.universal/Runtime/Data/UniversalRenderPipelineAsset.cs Line: 427)

Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.918 seconds
Domain Reload Profiling: 1669ms
	BeginReloadAssembly (146ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (4ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (52ms)
	RebuildCommonClasses (22ms)
	RebuildNativeTypeToScriptingClass (8ms)
	initialDomainReloadingComplete (36ms)
	LoadAllAssembliesAndSetupDomain (538ms)
		LoadAssemblies (327ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (270ms)
			TypeCache.Refresh (9ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (241ms)
			ResolveRequiredComponents (14ms)
	FinalizeReload (918ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (719ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (8ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (92ms)
			ProcessInitializeOnLoadAttributes (451ms)
			ProcessInitializeOnLoadMethodAttributes (161ms)
			AfterProcessingInitializeOnLoad (3ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (9ms)
Refreshing native plugins compatible for Editor in 1.26 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 29 Unused Serialized files (Serialized files now loaded: 0)
Unloading 8934 unused Assets / (7.4 MB). Loaded Objects now: 10097.
Memory consumption went from 234.5 MB to 227.1 MB.
Total: 28.927700 ms (FindLiveObjects: 1.048700 ms CreateObjectMapping: 0.722900 ms MarkObjects: 23.111800 ms  DeleteObjects: 4.042900 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
OnLevelWasLoaded was found on DOTweenComponent
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
Refreshing native plugins compatible for Editor in 1.52 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 29 Unused Serialized files (Serialized files now loaded: 0)
Unloading 8921 unused Assets / (7.9 MB). Loaded Objects now: 10097.
Memory consumption went from 234.6 MB to 226.7 MB.
Total: 28.350600 ms (FindLiveObjects: 0.775300 ms CreateObjectMapping: 0.593900 ms MarkObjects: 23.378100 ms  DeleteObjects: 3.601300 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Duplicate assembly 'System.Runtime.CompilerServices.Unsafe.dll' with different versions detected, using 'Packages/com.bezi.sidekick/Plugins/System.Runtime.CompilerServices.Unsafe.dll, AssemblyName=System.Runtime.CompilerServices.Unsafe, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a' and ignoring 'Assets/Plugins/Protobuf/System.Runtime.CompilerServices.Unsafe.dll, AssemblyName=System.Runtime.CompilerServices.Unsafe, Version=4.0.4.1, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a'.Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.866 seconds
OnLevelWasLoaded was found on DOTweenComponent
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
Refreshing native plugins compatible for Editor in 0.94 ms, found 3 plugins.
OnLevelWasLoaded was found on DOTweenComponent
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
Assertion failed on expression: 'pred(*previous, *i)'
Native extension for Android target not found
Native extension for WindowsStandalone target not found
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Refreshing native plugins compatible for Editor in 0.81 ms, found 3 plugins.
SceneCameraResetButton: Initialized.
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:Log (object)
SceneCameraResetButton:.cctor () (at Assets/Editor/Tools/SceneCameraResetButton.cs:15)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: Assets/Editor/Tools/SceneCameraResetButton.cs Line: 15)

The project currently uses the compatibility mode where the Render Graph API is disabled. Support for this mode will be removed in future Unity versions. Migrate existing ScriptableRenderPasses to the new RenderGraph API. After the migration, disable the compatibility mode in Edit > Projects Settings > Graphics > Render Graph.
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogWarning (object)
UnityEngine.Rendering.Universal.WarnUsingNonRenderGraph:EmitConsoleWarning () (at ./Packages/com.unity.render-pipelines.universal/Runtime/Data/UniversalRenderPipelineAsset.cs:427)
System.Reflection.RuntimeMethodInfo:Invoke (object,System.Reflection.BindingFlags,System.Reflection.Binder,object[],System.Globalization.CultureInfo)
System.Reflection.MethodBase:Invoke (object,object[])
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadMethodAttributes ()

(Filename: ./Packages/com.unity.render-pipelines.universal/Runtime/Data/UniversalRenderPipelineAsset.cs Line: 427)

Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.897 seconds
Domain Reload Profiling: 1766ms
	BeginReloadAssembly (154ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (4ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (55ms)
	RebuildCommonClasses (23ms)
	RebuildNativeTypeToScriptingClass (9ms)
	initialDomainReloadingComplete (38ms)
	LoadAllAssembliesAndSetupDomain (646ms)
		LoadAssemblies (383ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (325ms)
			TypeCache.Refresh (42ms)
				TypeCache.ScanAssembly (27ms)
			BuildScriptInfoCaches (263ms)
			ResolveRequiredComponents (14ms)
	FinalizeReload (897ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (692ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (7ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (92ms)
			ProcessInitializeOnLoadAttributes (448ms)
			ProcessInitializeOnLoadMethodAttributes (138ms)
			AfterProcessingInitializeOnLoad (3ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (8ms)
Refreshing native plugins compatible for Editor in 1.31 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 29 Unused Serialized files (Serialized files now loaded: 0)
Unloading 8934 unused Assets / (8.2 MB). Loaded Objects now: 10099.
Memory consumption went from 234.5 MB to 226.4 MB.
Total: 31.060200 ms (FindLiveObjects: 0.813900 ms CreateObjectMapping: 0.626600 ms MarkObjects: 24.576300 ms  DeleteObjects: 5.041600 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Duplicate assembly 'System.Runtime.CompilerServices.Unsafe.dll' with different versions detected, using 'Packages/com.bezi.sidekick/Plugins/System.Runtime.CompilerServices.Unsafe.dll, AssemblyName=System.Runtime.CompilerServices.Unsafe, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a' and ignoring 'Assets/Plugins/Protobuf/System.Runtime.CompilerServices.Unsafe.dll, AssemblyName=System.Runtime.CompilerServices.Unsafe, Version=4.0.4.1, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a'.Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.784 seconds
OnLevelWasLoaded was found on DOTweenComponent
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
Refreshing native plugins compatible for Editor in 0.60 ms, found 3 plugins.
OnLevelWasLoaded was found on DOTweenComponent
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
Assertion failed on expression: 'pred(*previous, *i)'
Native extension for Android target not found
Native extension for WindowsStandalone target not found
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Refreshing native plugins compatible for Editor in 0.77 ms, found 3 plugins.
SceneCameraResetButton: Initialized.
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:Log (object)
SceneCameraResetButton:.cctor () (at Assets/Editor/Tools/SceneCameraResetButton.cs:15)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: Assets/Editor/Tools/SceneCameraResetButton.cs Line: 15)

The project currently uses the compatibility mode where the Render Graph API is disabled. Support for this mode will be removed in future Unity versions. Migrate existing ScriptableRenderPasses to the new RenderGraph API. After the migration, disable the compatibility mode in Edit > Projects Settings > Graphics > Render Graph.
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogWarning (object)
UnityEngine.Rendering.Universal.WarnUsingNonRenderGraph:EmitConsoleWarning () (at ./Packages/com.unity.render-pipelines.universal/Runtime/Data/UniversalRenderPipelineAsset.cs:427)
System.Reflection.RuntimeMethodInfo:Invoke (object,System.Reflection.BindingFlags,System.Reflection.Binder,object[],System.Globalization.CultureInfo)
System.Reflection.MethodBase:Invoke (object,object[])
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadMethodAttributes ()

(Filename: ./Packages/com.unity.render-pipelines.universal/Runtime/Data/UniversalRenderPipelineAsset.cs Line: 427)

Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.888 seconds
Domain Reload Profiling: 1677ms
	BeginReloadAssembly (152ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (4ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (52ms)
	RebuildCommonClasses (26ms)
	RebuildNativeTypeToScriptingClass (8ms)
	initialDomainReloadingComplete (41ms)
	LoadAllAssembliesAndSetupDomain (561ms)
		LoadAssemblies (349ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (277ms)
			TypeCache.Refresh (11ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (246ms)
			ResolveRequiredComponents (13ms)
	FinalizeReload (889ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (695ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (7ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (89ms)
			ProcessInitializeOnLoadAttributes (456ms)
			ProcessInitializeOnLoadMethodAttributes (136ms)
			AfterProcessingInitializeOnLoad (3ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (8ms)
Refreshing native plugins compatible for Editor in 1.11 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 29 Unused Serialized files (Serialized files now loaded: 0)
Unloading 8934 unused Assets / (7.3 MB). Loaded Objects now: 10101.
Memory consumption went from 238.2 MB to 230.9 MB.
Total: 26.801000 ms (FindLiveObjects: 0.747800 ms CreateObjectMapping: 0.637300 ms MarkObjects: 22.166300 ms  DeleteObjects: 3.248100 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Duplicate assembly 'System.Runtime.CompilerServices.Unsafe.dll' with different versions detected, using 'Packages/com.bezi.sidekick/Plugins/System.Runtime.CompilerServices.Unsafe.dll, AssemblyName=System.Runtime.CompilerServices.Unsafe, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a' and ignoring 'Assets/Plugins/Protobuf/System.Runtime.CompilerServices.Unsafe.dll, AssemblyName=System.Runtime.CompilerServices.Unsafe, Version=4.0.4.1, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a'.Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.786 seconds
OnLevelWasLoaded was found on DOTweenComponent
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
Refreshing native plugins compatible for Editor in 0.71 ms, found 3 plugins.
OnLevelWasLoaded was found on DOTweenComponent
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
Assertion failed on expression: 'pred(*previous, *i)'
Native extension for Android target not found
Native extension for WindowsStandalone target not found
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Refreshing native plugins compatible for Editor in 0.98 ms, found 3 plugins.
SceneCameraResetButton: Initialized.
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:Log (object)
SceneCameraResetButton:.cctor () (at Assets/Editor/Tools/SceneCameraResetButton.cs:15)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: Assets/Editor/Tools/SceneCameraResetButton.cs Line: 15)

The project currently uses the compatibility mode where the Render Graph API is disabled. Support for this mode will be removed in future Unity versions. Migrate existing ScriptableRenderPasses to the new RenderGraph API. After the migration, disable the compatibility mode in Edit > Projects Settings > Graphics > Render Graph.
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogWarning (object)
UnityEngine.Rendering.Universal.WarnUsingNonRenderGraph:EmitConsoleWarning () (at ./Packages/com.unity.render-pipelines.universal/Runtime/Data/UniversalRenderPipelineAsset.cs:427)
System.Reflection.RuntimeMethodInfo:Invoke (object,System.Reflection.BindingFlags,System.Reflection.Binder,object[],System.Globalization.CultureInfo)
System.Reflection.MethodBase:Invoke (object,object[])
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadMethodAttributes ()

(Filename: ./Packages/com.unity.render-pipelines.universal/Runtime/Data/UniversalRenderPipelineAsset.cs Line: 427)

Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.883 seconds
Domain Reload Profiling: 1673ms
	BeginReloadAssembly (144ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (4ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (49ms)
	RebuildCommonClasses (25ms)
	RebuildNativeTypeToScriptingClass (8ms)
	initialDomainReloadingComplete (37ms)
	LoadAllAssembliesAndSetupDomain (576ms)
		LoadAssemblies (354ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (281ms)
			TypeCache.Refresh (10ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (251ms)
			ResolveRequiredComponents (14ms)
	FinalizeReload (883ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (688ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (7ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (90ms)
			ProcessInitializeOnLoadAttributes (447ms)
			ProcessInitializeOnLoadMethodAttributes (136ms)
			AfterProcessingInitializeOnLoad (3ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (8ms)
Refreshing native plugins compatible for Editor in 1.25 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 29 Unused Serialized files (Serialized files now loaded: 0)
Unloading 8934 unused Assets / (7.6 MB). Loaded Objects now: 10103.
Memory consumption went from 238.3 MB to 230.7 MB.
Total: 28.283400 ms (FindLiveObjects: 0.852600 ms CreateObjectMapping: 0.703700 ms MarkObjects: 22.690000 ms  DeleteObjects: 4.035800 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
OnLevelWasLoaded was found on DOTweenComponent
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
Refreshing native plugins compatible for Editor in 1.50 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 29 Unused Serialized files (Serialized files now loaded: 0)
Unloading 8921 unused Assets / (8.6 MB). Loaded Objects now: 10103.
Memory consumption went from 238.5 MB to 229.9 MB.
Total: 28.916000 ms (FindLiveObjects: 0.892700 ms CreateObjectMapping: 0.673400 ms MarkObjects: 23.062100 ms  DeleteObjects: 4.286000 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Duplicate assembly 'System.Runtime.CompilerServices.Unsafe.dll' with different versions detected, using 'Packages/com.bezi.sidekick/Plugins/System.Runtime.CompilerServices.Unsafe.dll, AssemblyName=System.Runtime.CompilerServices.Unsafe, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a' and ignoring 'Assets/Plugins/Protobuf/System.Runtime.CompilerServices.Unsafe.dll, AssemblyName=System.Runtime.CompilerServices.Unsafe, Version=4.0.4.1, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a'.Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.858 seconds
OnLevelWasLoaded was found on DOTweenComponent
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
Refreshing native plugins compatible for Editor in 0.76 ms, found 3 plugins.
OnLevelWasLoaded was found on DOTweenComponent
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
Assertion failed on expression: 'pred(*previous, *i)'
Native extension for Android target not found
Native extension for WindowsStandalone target not found
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Refreshing native plugins compatible for Editor in 0.93 ms, found 3 plugins.
SceneCameraResetButton: Initialized.
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:Log (object)
SceneCameraResetButton:.cctor () (at Assets/Editor/Tools/SceneCameraResetButton.cs:15)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: Assets/Editor/Tools/SceneCameraResetButton.cs Line: 15)

The project currently uses the compatibility mode where the Render Graph API is disabled. Support for this mode will be removed in future Unity versions. Migrate existing ScriptableRenderPasses to the new RenderGraph API. After the migration, disable the compatibility mode in Edit > Projects Settings > Graphics > Render Graph.
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogWarning (object)
UnityEngine.Rendering.Universal.WarnUsingNonRenderGraph:EmitConsoleWarning () (at ./Packages/com.unity.render-pipelines.universal/Runtime/Data/UniversalRenderPipelineAsset.cs:427)
System.Reflection.RuntimeMethodInfo:Invoke (object,System.Reflection.BindingFlags,System.Reflection.Binder,object[],System.Globalization.CultureInfo)
System.Reflection.MethodBase:Invoke (object,object[])
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadMethodAttributes ()

(Filename: ./Packages/com.unity.render-pipelines.universal/Runtime/Data/UniversalRenderPipelineAsset.cs Line: 427)

Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.960 seconds
Domain Reload Profiling: 1822ms
	BeginReloadAssembly (164ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (5ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (60ms)
	RebuildCommonClasses (24ms)
	RebuildNativeTypeToScriptingClass (8ms)
	initialDomainReloadingComplete (37ms)
	LoadAllAssembliesAndSetupDomain (629ms)
		LoadAssemblies (365ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (330ms)
			TypeCache.Refresh (41ms)
				TypeCache.ScanAssembly (27ms)
			BuildScriptInfoCaches (269ms)
			ResolveRequiredComponents (14ms)
	FinalizeReload (961ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (757ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (8ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (102ms)
			ProcessInitializeOnLoadAttributes (473ms)
			ProcessInitializeOnLoadMethodAttributes (167ms)
			AfterProcessingInitializeOnLoad (3ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (8ms)
Refreshing native plugins compatible for Editor in 1.27 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 29 Unused Serialized files (Serialized files now loaded: 0)
Unloading 8934 unused Assets / (7.4 MB). Loaded Objects now: 10105.
Memory consumption went from 238.3 MB to 230.9 MB.
Total: 28.234900 ms (FindLiveObjects: 0.806700 ms CreateObjectMapping: 0.593000 ms MarkObjects: 22.982000 ms  DeleteObjects: 3.851500 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
OnLevelWasLoaded was found on DOTweenComponent
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
Refreshing native plugins compatible for Editor in 1.42 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 29 Unused Serialized files (Serialized files now loaded: 0)
Unloading 8921 unused Assets / (7.3 MB). Loaded Objects now: 10105.
Memory consumption went from 238.5 MB to 231.2 MB.
Total: 31.177900 ms (FindLiveObjects: 0.817500 ms CreateObjectMapping: 0.708700 ms MarkObjects: 25.827900 ms  DeleteObjects: 3.822600 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Duplicate assembly 'System.Runtime.CompilerServices.Unsafe.dll' with different versions detected, using 'Packages/com.bezi.sidekick/Plugins/System.Runtime.CompilerServices.Unsafe.dll, AssemblyName=System.Runtime.CompilerServices.Unsafe, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a' and ignoring 'Assets/Plugins/Protobuf/System.Runtime.CompilerServices.Unsafe.dll, AssemblyName=System.Runtime.CompilerServices.Unsafe, Version=4.0.4.1, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a'.Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.832 seconds
OnLevelWasLoaded was found on DOTweenComponent
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
Refreshing native plugins compatible for Editor in 0.69 ms, found 3 plugins.
OnLevelWasLoaded was found on DOTweenComponent
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
Assertion failed on expression: 'pred(*previous, *i)'
Native extension for Android target not found
Native extension for WindowsStandalone target not found
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Refreshing native plugins compatible for Editor in 1.00 ms, found 3 plugins.
SceneCameraResetButton: Initialized.
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:Log (object)
SceneCameraResetButton:.cctor () (at Assets/Editor/Tools/SceneCameraResetButton.cs:15)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: Assets/Editor/Tools/SceneCameraResetButton.cs Line: 15)

The project currently uses the compatibility mode where the Render Graph API is disabled. Support for this mode will be removed in future Unity versions. Migrate existing ScriptableRenderPasses to the new RenderGraph API. After the migration, disable the compatibility mode in Edit > Projects Settings > Graphics > Render Graph.
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogWarning (object)
UnityEngine.Rendering.Universal.WarnUsingNonRenderGraph:EmitConsoleWarning () (at ./Packages/com.unity.render-pipelines.universal/Runtime/Data/UniversalRenderPipelineAsset.cs:427)
System.Reflection.RuntimeMethodInfo:Invoke (object,System.Reflection.BindingFlags,System.Reflection.Binder,object[],System.Globalization.CultureInfo)
System.Reflection.MethodBase:Invoke (object,object[])
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadMethodAttributes ()

(Filename: ./Packages/com.unity.render-pipelines.universal/Runtime/Data/UniversalRenderPipelineAsset.cs Line: 427)

Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.957 seconds
Domain Reload Profiling: 1794ms
	BeginReloadAssembly (148ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (5ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (52ms)
	RebuildCommonClasses (22ms)
	RebuildNativeTypeToScriptingClass (8ms)
	initialDomainReloadingComplete (36ms)
	LoadAllAssembliesAndSetupDomain (622ms)
		LoadAssemblies (361ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (319ms)
			TypeCache.Refresh (49ms)
				TypeCache.ScanAssembly (33ms)
			BuildScriptInfoCaches (248ms)
			ResolveRequiredComponents (15ms)
	FinalizeReload (958ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (752ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (7ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (99ms)
			ProcessInitializeOnLoadAttributes (495ms)
			ProcessInitializeOnLoadMethodAttributes (144ms)
			AfterProcessingInitializeOnLoad (3ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (8ms)
Refreshing native plugins compatible for Editor in 1.30 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 29 Unused Serialized files (Serialized files now loaded: 0)
Unloading 8934 unused Assets / (7.6 MB). Loaded Objects now: 10107.
Memory consumption went from 240.3 MB to 232.7 MB.
Total: 29.510400 ms (FindLiveObjects: 1.016900 ms CreateObjectMapping: 0.694800 ms MarkObjects: 24.267700 ms  DeleteObjects: 3.529600 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
OnLevelWasLoaded was found on DOTweenComponent
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
Refreshing native plugins compatible for Editor in 1.29 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 29 Unused Serialized files (Serialized files now loaded: 0)
Unloading 8921 unused Assets / (6.7 MB). Loaded Objects now: 10107.
Memory consumption went from 240.4 MB to 233.7 MB.
Total: 23.648600 ms (FindLiveObjects: 1.104200 ms CreateObjectMapping: 0.570800 ms MarkObjects: 18.686400 ms  DeleteObjects: 3.285900 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Duplicate assembly 'System.Runtime.CompilerServices.Unsafe.dll' with different versions detected, using 'Packages/com.bezi.sidekick/Plugins/System.Runtime.CompilerServices.Unsafe.dll, AssemblyName=System.Runtime.CompilerServices.Unsafe, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a' and ignoring 'Assets/Plugins/Protobuf/System.Runtime.CompilerServices.Unsafe.dll, AssemblyName=System.Runtime.CompilerServices.Unsafe, Version=4.0.4.1, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a'.Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  3.984 seconds
OnLevelWasLoaded was found on DOTweenComponent
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
Refreshing native plugins compatible for Editor in 0.64 ms, found 3 plugins.
OnLevelWasLoaded was found on DOTweenComponent
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
Assertion failed on expression: 'pred(*previous, *i)'
Native extension for Android target not found
Native extension for WindowsStandalone target not found
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Refreshing native plugins compatible for Editor in 1.25 ms, found 3 plugins.
SceneCameraResetButton: Initialized.
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:Log (object)
SceneCameraResetButton:.cctor () (at Assets/Editor/Tools/SceneCameraResetButton.cs:15)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: Assets/Editor/Tools/SceneCameraResetButton.cs Line: 15)

The project currently uses the compatibility mode where the Render Graph API is disabled. Support for this mode will be removed in future Unity versions. Migrate existing ScriptableRenderPasses to the new RenderGraph API. After the migration, disable the compatibility mode in Edit > Projects Settings > Graphics > Render Graph.
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogWarning (object)
UnityEngine.Rendering.Universal.WarnUsingNonRenderGraph:EmitConsoleWarning () (at ./Packages/com.unity.render-pipelines.universal/Runtime/Data/UniversalRenderPipelineAsset.cs:427)
System.Reflection.RuntimeMethodInfo:Invoke (object,System.Reflection.BindingFlags,System.Reflection.Binder,object[],System.Globalization.CultureInfo)
System.Reflection.MethodBase:Invoke (object,object[])
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadMethodAttributes ()

(Filename: ./Packages/com.unity.render-pipelines.universal/Runtime/Data/UniversalRenderPipelineAsset.cs Line: 427)

Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.200 seconds
Domain Reload Profiling: 5189ms
	BeginReloadAssembly (386ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (5ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (57ms)
	RebuildCommonClasses (105ms)
	RebuildNativeTypeToScriptingClass (8ms)
	initialDomainReloadingComplete (37ms)
	LoadAllAssembliesAndSetupDomain (3453ms)
		LoadAssemblies (3028ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (714ms)
			TypeCache.Refresh (41ms)
				TypeCache.ScanAssembly (24ms)
			BuildScriptInfoCaches (649ms)
			ResolveRequiredComponents (16ms)
	FinalizeReload (1201ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (990ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (8ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (107ms)
			ProcessInitializeOnLoadAttributes (581ms)
			ProcessInitializeOnLoadMethodAttributes (286ms)
			AfterProcessingInitializeOnLoad (3ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (9ms)
Refreshing native plugins compatible for Editor in 1.41 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 29 Unused Serialized files (Serialized files now loaded: 0)
Unloading 8934 unused Assets / (7.7 MB). Loaded Objects now: 10109.
Memory consumption went from 240.3 MB to 232.6 MB.
Total: 29.510400 ms (FindLiveObjects: 0.862500 ms CreateObjectMapping: 0.667300 ms MarkObjects: 24.036600 ms  DeleteObjects: 3.942400 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
OnLevelWasLoaded was found on DOTweenComponent
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
Refreshing native plugins compatible for Editor in 1.38 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 29 Unused Serialized files (Serialized files now loaded: 0)
Unloading 8921 unused Assets / (8.2 MB). Loaded Objects now: 10109.
Memory consumption went from 240.4 MB to 232.2 MB.
Total: 33.327100 ms (FindLiveObjects: 1.175400 ms CreateObjectMapping: 0.915800 ms MarkObjects: 27.395400 ms  DeleteObjects: 3.838800 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Duplicate assembly 'System.Runtime.CompilerServices.Unsafe.dll' with different versions detected, using 'Packages/com.bezi.sidekick/Plugins/System.Runtime.CompilerServices.Unsafe.dll, AssemblyName=System.Runtime.CompilerServices.Unsafe, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a' and ignoring 'Assets/Plugins/Protobuf/System.Runtime.CompilerServices.Unsafe.dll, AssemblyName=System.Runtime.CompilerServices.Unsafe, Version=4.0.4.1, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a'.Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.962 seconds
OnLevelWasLoaded was found on DOTweenComponent
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
Refreshing native plugins compatible for Editor in 0.62 ms, found 3 plugins.
OnLevelWasLoaded was found on DOTweenComponent
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
Assertion failed on expression: 'pred(*previous, *i)'
Native extension for Android target not found
Native extension for WindowsStandalone target not found
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Refreshing native plugins compatible for Editor in 0.80 ms, found 3 plugins.
SceneCameraResetButton: Initialized.
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:Log (object)
SceneCameraResetButton:.cctor () (at Assets/Editor/Tools/SceneCameraResetButton.cs:15)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: Assets/Editor/Tools/SceneCameraResetButton.cs Line: 15)

The project currently uses the compatibility mode where the Render Graph API is disabled. Support for this mode will be removed in future Unity versions. Migrate existing ScriptableRenderPasses to the new RenderGraph API. After the migration, disable the compatibility mode in Edit > Projects Settings > Graphics > Render Graph.
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogWarning (object)
UnityEngine.Rendering.Universal.WarnUsingNonRenderGraph:EmitConsoleWarning () (at ./Packages/com.unity.render-pipelines.universal/Runtime/Data/UniversalRenderPipelineAsset.cs:427)
System.Reflection.RuntimeMethodInfo:Invoke (object,System.Reflection.BindingFlags,System.Reflection.Binder,object[],System.Globalization.CultureInfo)
System.Reflection.MethodBase:Invoke (object,object[])
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadMethodAttributes ()

(Filename: ./Packages/com.unity.render-pipelines.universal/Runtime/Data/UniversalRenderPipelineAsset.cs Line: 427)

Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.997 seconds
Domain Reload Profiling: 1964ms
	BeginReloadAssembly (166ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (5ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (63ms)
	RebuildCommonClasses (25ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (40ms)
	LoadAllAssembliesAndSetupDomain (724ms)
		LoadAssemblies (415ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (373ms)
			TypeCache.Refresh (48ms)
				TypeCache.ScanAssembly (31ms)
			BuildScriptInfoCaches (295ms)
			ResolveRequiredComponents (21ms)
	FinalizeReload (998ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (782ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (8ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (101ms)
			ProcessInitializeOnLoadAttributes (485ms)
			ProcessInitializeOnLoadMethodAttributes (181ms)
			AfterProcessingInitializeOnLoad (3ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (9ms)
Refreshing native plugins compatible for Editor in 1.69 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 29 Unused Serialized files (Serialized files now loaded: 0)
Unloading 8934 unused Assets / (7.7 MB). Loaded Objects now: 10111.
Memory consumption went from 240.1 MB to 232.4 MB.
Total: 29.583900 ms (FindLiveObjects: 0.912700 ms CreateObjectMapping: 0.730900 ms MarkObjects: 24.203000 ms  DeleteObjects: 3.735700 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Duplicate assembly 'System.Runtime.CompilerServices.Unsafe.dll' with different versions detected, using 'Packages/com.bezi.sidekick/Plugins/System.Runtime.CompilerServices.Unsafe.dll, AssemblyName=System.Runtime.CompilerServices.Unsafe, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a' and ignoring 'Assets/Plugins/Protobuf/System.Runtime.CompilerServices.Unsafe.dll, AssemblyName=System.Runtime.CompilerServices.Unsafe, Version=4.0.4.1, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a'.Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.809 seconds
OnLevelWasLoaded was found on DOTweenComponent
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
Refreshing native plugins compatible for Editor in 0.61 ms, found 3 plugins.
OnLevelWasLoaded was found on DOTweenComponent
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
Assertion failed on expression: 'pred(*previous, *i)'
Native extension for Android target not found
Native extension for WindowsStandalone target not found
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Refreshing native plugins compatible for Editor in 1.22 ms, found 3 plugins.
SceneCameraResetButton: Initialized.
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:Log (object)
SceneCameraResetButton:.cctor () (at Assets/Editor/Tools/SceneCameraResetButton.cs:15)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: Assets/Editor/Tools/SceneCameraResetButton.cs Line: 15)

The project currently uses the compatibility mode where the Render Graph API is disabled. Support for this mode will be removed in future Unity versions. Migrate existing ScriptableRenderPasses to the new RenderGraph API. After the migration, disable the compatibility mode in Edit > Projects Settings > Graphics > Render Graph.
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogWarning (object)
UnityEngine.Rendering.Universal.WarnUsingNonRenderGraph:EmitConsoleWarning () (at ./Packages/com.unity.render-pipelines.universal/Runtime/Data/UniversalRenderPipelineAsset.cs:427)
System.Reflection.RuntimeMethodInfo:Invoke (object,System.Reflection.BindingFlags,System.Reflection.Binder,object[],System.Globalization.CultureInfo)
System.Reflection.MethodBase:Invoke (object,object[])
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadMethodAttributes ()

(Filename: ./Packages/com.unity.render-pipelines.universal/Runtime/Data/UniversalRenderPipelineAsset.cs Line: 427)

Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.918 seconds
Domain Reload Profiling: 1731ms
	BeginReloadAssembly (156ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (4ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (58ms)
	RebuildCommonClasses (25ms)
	RebuildNativeTypeToScriptingClass (8ms)
	initialDomainReloadingComplete (37ms)
	LoadAllAssembliesAndSetupDomain (586ms)
		LoadAssemblies (357ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (292ms)
			TypeCache.Refresh (10ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (261ms)
			ResolveRequiredComponents (14ms)
	FinalizeReload (918ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (718ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (7ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (94ms)
			ProcessInitializeOnLoadAttributes (479ms)
			ProcessInitializeOnLoadMethodAttributes (131ms)
			AfterProcessingInitializeOnLoad (3ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (7ms)
Refreshing native plugins compatible for Editor in 1.27 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 29 Unused Serialized files (Serialized files now loaded: 0)
Unloading 8934 unused Assets / (7.6 MB). Loaded Objects now: 10113.
Memory consumption went from 240.3 MB to 232.7 MB.
Total: 29.712200 ms (FindLiveObjects: 0.882300 ms CreateObjectMapping: 0.729900 ms MarkObjects: 24.251300 ms  DeleteObjects: 3.847500 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
OnLevelWasLoaded was found on DOTweenComponent
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
Refreshing native plugins compatible for Editor in 1.31 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 29 Unused Serialized files (Serialized files now loaded: 0)
Unloading 8921 unused Assets / (8.1 MB). Loaded Objects now: 10113.
Memory consumption went from 240.5 MB to 232.3 MB.
Total: 31.854700 ms (FindLiveObjects: 0.936500 ms CreateObjectMapping: 0.691100 ms MarkObjects: 25.703600 ms  DeleteObjects: 4.522100 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Duplicate assembly 'System.Runtime.CompilerServices.Unsafe.dll' with different versions detected, using 'Packages/com.bezi.sidekick/Plugins/System.Runtime.CompilerServices.Unsafe.dll, AssemblyName=System.Runtime.CompilerServices.Unsafe, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a' and ignoring 'Assets/Plugins/Protobuf/System.Runtime.CompilerServices.Unsafe.dll, AssemblyName=System.Runtime.CompilerServices.Unsafe, Version=4.0.4.1, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a'.Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.840 seconds
OnLevelWasLoaded was found on DOTweenComponent
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
Refreshing native plugins compatible for Editor in 0.66 ms, found 3 plugins.
OnLevelWasLoaded was found on DOTweenComponent
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
Assertion failed on expression: 'pred(*previous, *i)'
Native extension for Android target not found
Native extension for WindowsStandalone target not found
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Refreshing native plugins compatible for Editor in 0.91 ms, found 3 plugins.
SceneCameraResetButton: Initialized.
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:Log (object)
SceneCameraResetButton:.cctor () (at Assets/Editor/Tools/SceneCameraResetButton.cs:15)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: Assets/Editor/Tools/SceneCameraResetButton.cs Line: 15)

The project currently uses the compatibility mode where the Render Graph API is disabled. Support for this mode will be removed in future Unity versions. Migrate existing ScriptableRenderPasses to the new RenderGraph API. After the migration, disable the compatibility mode in Edit > Projects Settings > Graphics > Render Graph.
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogWarning (object)
UnityEngine.Rendering.Universal.WarnUsingNonRenderGraph:EmitConsoleWarning () (at ./Packages/com.unity.render-pipelines.universal/Runtime/Data/UniversalRenderPipelineAsset.cs:427)
System.Reflection.RuntimeMethodInfo:Invoke (object,System.Reflection.BindingFlags,System.Reflection.Binder,object[],System.Globalization.CultureInfo)
System.Reflection.MethodBase:Invoke (object,object[])
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadMethodAttributes ()

(Filename: ./Packages/com.unity.render-pipelines.universal/Runtime/Data/UniversalRenderPipelineAsset.cs Line: 427)

Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.953 seconds
Domain Reload Profiling: 1796ms
	BeginReloadAssembly (142ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (4ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (52ms)
	RebuildCommonClasses (24ms)
	RebuildNativeTypeToScriptingClass (8ms)
	initialDomainReloadingComplete (38ms)
	LoadAllAssembliesAndSetupDomain (630ms)
		LoadAssemblies (359ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (327ms)
			TypeCache.Refresh (40ms)
				TypeCache.ScanAssembly (27ms)
			BuildScriptInfoCaches (265ms)
			ResolveRequiredComponents (15ms)
	FinalizeReload (953ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (752ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (8ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (105ms)
			ProcessInitializeOnLoadAttributes (479ms)
			ProcessInitializeOnLoadMethodAttributes (152ms)
			AfterProcessingInitializeOnLoad (3ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (8ms)
Refreshing native plugins compatible for Editor in 1.25 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 29 Unused Serialized files (Serialized files now loaded: 0)
Unloading 8934 unused Assets / (8.9 MB). Loaded Objects now: 10115.
Memory consumption went from 240.4 MB to 231.5 MB.
Total: 31.115300 ms (FindLiveObjects: 0.912000 ms CreateObjectMapping: 0.743300 ms MarkObjects: 25.350100 ms  DeleteObjects: 4.108700 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Duplicate assembly 'System.Runtime.CompilerServices.Unsafe.dll' with different versions detected, using 'Packages/com.bezi.sidekick/Plugins/System.Runtime.CompilerServices.Unsafe.dll, AssemblyName=System.Runtime.CompilerServices.Unsafe, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a' and ignoring 'Assets/Plugins/Protobuf/System.Runtime.CompilerServices.Unsafe.dll, AssemblyName=System.Runtime.CompilerServices.Unsafe, Version=4.0.4.1, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a'.Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.856 seconds
OnLevelWasLoaded was found on DOTweenComponent
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
Refreshing native plugins compatible for Editor in 0.89 ms, found 3 plugins.
OnLevelWasLoaded was found on DOTweenComponent
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
Assertion failed on expression: 'pred(*previous, *i)'
Native extension for Android target not found
Native extension for WindowsStandalone target not found
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Refreshing native plugins compatible for Editor in 0.75 ms, found 3 plugins.
SceneCameraResetButton: Initialized.
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:Log (object)
SceneCameraResetButton:.cctor () (at Assets/Editor/Tools/SceneCameraResetButton.cs:15)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: Assets/Editor/Tools/SceneCameraResetButton.cs Line: 15)

The project currently uses the compatibility mode where the Render Graph API is disabled. Support for this mode will be removed in future Unity versions. Migrate existing ScriptableRenderPasses to the new RenderGraph API. After the migration, disable the compatibility mode in Edit > Projects Settings > Graphics > Render Graph.
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogWarning (object)
UnityEngine.Rendering.Universal.WarnUsingNonRenderGraph:EmitConsoleWarning () (at ./Packages/com.unity.render-pipelines.universal/Runtime/Data/UniversalRenderPipelineAsset.cs:427)
System.Reflection.RuntimeMethodInfo:Invoke (object,System.Reflection.BindingFlags,System.Reflection.Binder,object[],System.Globalization.CultureInfo)
System.Reflection.MethodBase:Invoke (object,object[])
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadMethodAttributes ()

(Filename: ./Packages/com.unity.render-pipelines.universal/Runtime/Data/UniversalRenderPipelineAsset.cs Line: 427)

Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.912 seconds
Domain Reload Profiling: 1773ms
	BeginReloadAssembly (158ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (4ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (57ms)
	RebuildCommonClasses (26ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (41ms)
	LoadAllAssembliesAndSetupDomain (624ms)
		LoadAssemblies (377ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (310ms)
			TypeCache.Refresh (12ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (275ms)
			ResolveRequiredComponents (16ms)
	FinalizeReload (913ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (708ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (7ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (94ms)
			ProcessInitializeOnLoadAttributes (462ms)
			ProcessInitializeOnLoadMethodAttributes (138ms)
			AfterProcessingInitializeOnLoad (3ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (9ms)
Refreshing native plugins compatible for Editor in 1.17 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 29 Unused Serialized files (Serialized files now loaded: 0)
Unloading 8934 unused Assets / (8.5 MB). Loaded Objects now: 10117.
Memory consumption went from 240.3 MB to 231.8 MB.
Total: 31.952600 ms (FindLiveObjects: 0.868000 ms CreateObjectMapping: 0.703400 ms MarkObjects: 26.041700 ms  DeleteObjects: 4.337900 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Duplicate assembly 'System.Runtime.CompilerServices.Unsafe.dll' with different versions detected, using 'Packages/com.bezi.sidekick/Plugins/System.Runtime.CompilerServices.Unsafe.dll, AssemblyName=System.Runtime.CompilerServices.Unsafe, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a' and ignoring 'Assets/Plugins/Protobuf/System.Runtime.CompilerServices.Unsafe.dll, AssemblyName=System.Runtime.CompilerServices.Unsafe, Version=4.0.4.1, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a'.Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.865 seconds
OnLevelWasLoaded was found on DOTweenComponent
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
Refreshing native plugins compatible for Editor in 0.61 ms, found 3 plugins.
OnLevelWasLoaded was found on DOTweenComponent
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
Assertion failed on expression: 'pred(*previous, *i)'
Native extension for Android target not found
Native extension for WindowsStandalone target not found
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Refreshing native plugins compatible for Editor in 0.81 ms, found 3 plugins.
SceneCameraResetButton: Initialized.
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:Log (object)
SceneCameraResetButton:.cctor () (at Assets/Editor/Tools/SceneCameraResetButton.cs:15)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: Assets/Editor/Tools/SceneCameraResetButton.cs Line: 15)

The project currently uses the compatibility mode where the Render Graph API is disabled. Support for this mode will be removed in future Unity versions. Migrate existing ScriptableRenderPasses to the new RenderGraph API. After the migration, disable the compatibility mode in Edit > Projects Settings > Graphics > Render Graph.
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogWarning (object)
UnityEngine.Rendering.Universal.WarnUsingNonRenderGraph:EmitConsoleWarning () (at ./Packages/com.unity.render-pipelines.universal/Runtime/Data/UniversalRenderPipelineAsset.cs:427)
System.Reflection.RuntimeMethodInfo:Invoke (object,System.Reflection.BindingFlags,System.Reflection.Binder,object[],System.Globalization.CultureInfo)
System.Reflection.MethodBase:Invoke (object,object[])
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadMethodAttributes ()

(Filename: ./Packages/com.unity.render-pipelines.universal/Runtime/Data/UniversalRenderPipelineAsset.cs Line: 427)

Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.936 seconds
Domain Reload Profiling: 1805ms
	BeginReloadAssembly (156ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (4ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (56ms)
	RebuildCommonClasses (23ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (38ms)
	LoadAllAssembliesAndSetupDomain (642ms)
		LoadAssemblies (378ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (328ms)
			TypeCache.Refresh (13ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (292ms)
			ResolveRequiredComponents (15ms)
	FinalizeReload (936ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (737ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (8ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (100ms)
			ProcessInitializeOnLoadAttributes (466ms)
			ProcessInitializeOnLoadMethodAttributes (156ms)
			AfterProcessingInitializeOnLoad (3ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (8ms)
Refreshing native plugins compatible for Editor in 1.57 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 29 Unused Serialized files (Serialized files now loaded: 0)
Unloading 8934 unused Assets / (7.7 MB). Loaded Objects now: 10119.
Memory consumption went from 240.3 MB to 232.6 MB.
Total: 22.578500 ms (FindLiveObjects: 1.029200 ms CreateObjectMapping: 0.964400 ms MarkObjects: 17.057000 ms  DeleteObjects: 3.526500 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Duplicate assembly 'System.Runtime.CompilerServices.Unsafe.dll' with different versions detected, using 'Packages/com.bezi.sidekick/Plugins/System.Runtime.CompilerServices.Unsafe.dll, AssemblyName=System.Runtime.CompilerServices.Unsafe, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a' and ignoring 'Assets/Plugins/Protobuf/System.Runtime.CompilerServices.Unsafe.dll, AssemblyName=System.Runtime.CompilerServices.Unsafe, Version=4.0.4.1, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a'.Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.878 seconds
OnLevelWasLoaded was found on DOTweenComponent
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
Refreshing native plugins compatible for Editor in 0.67 ms, found 3 plugins.
OnLevelWasLoaded was found on DOTweenComponent
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
Assertion failed on expression: 'pred(*previous, *i)'
Native extension for Android target not found
Native extension for WindowsStandalone target not found
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Refreshing native plugins compatible for Editor in 0.74 ms, found 3 plugins.
SceneCameraResetButton: Initialized.
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:Log (object)
SceneCameraResetButton:.cctor () (at Assets/Editor/Tools/SceneCameraResetButton.cs:15)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: Assets/Editor/Tools/SceneCameraResetButton.cs Line: 15)

The project currently uses the compatibility mode where the Render Graph API is disabled. Support for this mode will be removed in future Unity versions. Migrate existing ScriptableRenderPasses to the new RenderGraph API. After the migration, disable the compatibility mode in Edit > Projects Settings > Graphics > Render Graph.
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogWarning (object)
UnityEngine.Rendering.Universal.WarnUsingNonRenderGraph:EmitConsoleWarning () (at ./Packages/com.unity.render-pipelines.universal/Runtime/Data/UniversalRenderPipelineAsset.cs:427)
System.Reflection.RuntimeMethodInfo:Invoke (object,System.Reflection.BindingFlags,System.Reflection.Binder,object[],System.Globalization.CultureInfo)
System.Reflection.MethodBase:Invoke (object,object[])
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadMethodAttributes ()

(Filename: ./Packages/com.unity.render-pipelines.universal/Runtime/Data/UniversalRenderPipelineAsset.cs Line: 427)

Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.942 seconds
Domain Reload Profiling: 1824ms
	BeginReloadAssembly (156ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (4ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (57ms)
	RebuildCommonClasses (24ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (39ms)
	LoadAllAssembliesAndSetupDomain (654ms)
		LoadAssemblies (385ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (332ms)
			TypeCache.Refresh (13ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (298ms)
			ResolveRequiredComponents (14ms)
	FinalizeReload (942ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (740ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (8ms)
			SetLoadedEditorAssemblies (5ms)
			BeforeProcessingInitializeOnLoad (111ms)
			ProcessInitializeOnLoadAttributes (474ms)
			ProcessInitializeOnLoadMethodAttributes (140ms)
			AfterProcessingInitializeOnLoad (3ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (8ms)
Refreshing native plugins compatible for Editor in 1.19 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 29 Unused Serialized files (Serialized files now loaded: 0)
Unloading 8934 unused Assets / (7.0 MB). Loaded Objects now: 10121.
Memory consumption went from 240.2 MB to 233.3 MB.
Total: 27.123600 ms (FindLiveObjects: 0.921300 ms CreateObjectMapping: 0.718600 ms MarkObjects: 21.929700 ms  DeleteObjects: 3.552300 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
OnLevelWasLoaded was found on DOTweenComponent
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
Refreshing native plugins compatible for Editor in 1.39 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 29 Unused Serialized files (Serialized files now loaded: 0)
Unloading 8921 unused Assets / (8.2 MB). Loaded Objects now: 10121.
Memory consumption went from 240.4 MB to 232.2 MB.
Total: 33.210700 ms (FindLiveObjects: 0.972100 ms CreateObjectMapping: 0.855800 ms MarkObjects: 27.254100 ms  DeleteObjects: 4.127000 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Duplicate assembly 'System.Runtime.CompilerServices.Unsafe.dll' with different versions detected, using 'Packages/com.bezi.sidekick/Plugins/System.Runtime.CompilerServices.Unsafe.dll, AssemblyName=System.Runtime.CompilerServices.Unsafe, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a' and ignoring 'Assets/Plugins/Protobuf/System.Runtime.CompilerServices.Unsafe.dll, AssemblyName=System.Runtime.CompilerServices.Unsafe, Version=4.0.4.1, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a'.Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.948 seconds
OnLevelWasLoaded was found on DOTweenComponent
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
Refreshing native plugins compatible for Editor in 0.72 ms, found 3 plugins.
OnLevelWasLoaded was found on DOTweenComponent
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
Assertion failed on expression: 'pred(*previous, *i)'
Native extension for Android target not found
Native extension for WindowsStandalone target not found
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Refreshing native plugins compatible for Editor in 0.79 ms, found 3 plugins.
SceneCameraResetButton: Initialized.
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:Log (object)
SceneCameraResetButton:.cctor () (at Assets/Editor/Tools/SceneCameraResetButton.cs:15)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: Assets/Editor/Tools/SceneCameraResetButton.cs Line: 15)

The project currently uses the compatibility mode where the Render Graph API is disabled. Support for this mode will be removed in future Unity versions. Migrate existing ScriptableRenderPasses to the new RenderGraph API. After the migration, disable the compatibility mode in Edit > Projects Settings > Graphics > Render Graph.
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogWarning (object)
UnityEngine.Rendering.Universal.WarnUsingNonRenderGraph:EmitConsoleWarning () (at ./Packages/com.unity.render-pipelines.universal/Runtime/Data/UniversalRenderPipelineAsset.cs:427)
System.Reflection.RuntimeMethodInfo:Invoke (object,System.Reflection.BindingFlags,System.Reflection.Binder,object[],System.Globalization.CultureInfo)
System.Reflection.MethodBase:Invoke (object,object[])
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadMethodAttributes ()

(Filename: ./Packages/com.unity.render-pipelines.universal/Runtime/Data/UniversalRenderPipelineAsset.cs Line: 427)

Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.957 seconds
Domain Reload Profiling: 1909ms
	BeginReloadAssembly (157ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (4ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (57ms)
	RebuildCommonClasses (25ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (41ms)
	LoadAllAssembliesAndSetupDomain (719ms)
		LoadAssemblies (417ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (363ms)
			TypeCache.Refresh (49ms)
				TypeCache.ScanAssembly (29ms)
			BuildScriptInfoCaches (291ms)
			ResolveRequiredComponents (17ms)
	FinalizeReload (957ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (721ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (7ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (92ms)
			ProcessInitializeOnLoadAttributes (476ms)
			ProcessInitializeOnLoadMethodAttributes (139ms)
			AfterProcessingInitializeOnLoad (3ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (9ms)
Refreshing native plugins compatible for Editor in 1.33 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 29 Unused Serialized files (Serialized files now loaded: 0)
Unloading 8934 unused Assets / (7.9 MB). Loaded Objects now: 10123.
Memory consumption went from 240.3 MB to 232.4 MB.
Total: 29.720100 ms (FindLiveObjects: 0.862900 ms CreateObjectMapping: 0.762100 ms MarkObjects: 23.767500 ms  DeleteObjects: 4.325900 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Duplicate assembly 'System.Runtime.CompilerServices.Unsafe.dll' with different versions detected, using 'Packages/com.bezi.sidekick/Plugins/System.Runtime.CompilerServices.Unsafe.dll, AssemblyName=System.Runtime.CompilerServices.Unsafe, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a' and ignoring 'Assets/Plugins/Protobuf/System.Runtime.CompilerServices.Unsafe.dll, AssemblyName=System.Runtime.CompilerServices.Unsafe, Version=4.0.4.1, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a'.Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.803 seconds
OnLevelWasLoaded was found on DOTweenComponent
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
Refreshing native plugins compatible for Editor in 0.79 ms, found 3 plugins.
OnLevelWasLoaded was found on DOTweenComponent
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
Assertion failed on expression: 'pred(*previous, *i)'
Native extension for Android target not found
Native extension for WindowsStandalone target not found
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Refreshing native plugins compatible for Editor in 0.80 ms, found 3 plugins.
SceneCameraResetButton: Initialized.
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:Log (object)
SceneCameraResetButton:.cctor () (at Assets/Editor/Tools/SceneCameraResetButton.cs:15)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: Assets/Editor/Tools/SceneCameraResetButton.cs Line: 15)

The project currently uses the compatibility mode where the Render Graph API is disabled. Support for this mode will be removed in future Unity versions. Migrate existing ScriptableRenderPasses to the new RenderGraph API. After the migration, disable the compatibility mode in Edit > Projects Settings > Graphics > Render Graph.
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogWarning (object)
UnityEngine.Rendering.Universal.WarnUsingNonRenderGraph:EmitConsoleWarning () (at ./Packages/com.unity.render-pipelines.universal/Runtime/Data/UniversalRenderPipelineAsset.cs:427)
System.Reflection.RuntimeMethodInfo:Invoke (object,System.Reflection.BindingFlags,System.Reflection.Binder,object[],System.Globalization.CultureInfo)
System.Reflection.MethodBase:Invoke (object,object[])
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadMethodAttributes ()

(Filename: ./Packages/com.unity.render-pipelines.universal/Runtime/Data/UniversalRenderPipelineAsset.cs Line: 427)

Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.892 seconds
Domain Reload Profiling: 1698ms
	BeginReloadAssembly (149ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (5ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (50ms)
	RebuildCommonClasses (24ms)
	RebuildNativeTypeToScriptingClass (9ms)
	initialDomainReloadingComplete (39ms)
	LoadAllAssembliesAndSetupDomain (585ms)
		LoadAssemblies (357ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (291ms)
			TypeCache.Refresh (10ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (262ms)
			ResolveRequiredComponents (13ms)
	FinalizeReload (892ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (698ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (7ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (92ms)
			ProcessInitializeOnLoadAttributes (451ms)
			ProcessInitializeOnLoadMethodAttributes (141ms)
			AfterProcessingInitializeOnLoad (3ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (8ms)
Refreshing native plugins compatible for Editor in 1.54 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 29 Unused Serialized files (Serialized files now loaded: 0)
Unloading 8934 unused Assets / (7.8 MB). Loaded Objects now: 10125.
Memory consumption went from 240.3 MB to 232.6 MB.
Total: 26.393700 ms (FindLiveObjects: 0.918600 ms CreateObjectMapping: 0.686400 ms MarkObjects: 21.430400 ms  DeleteObjects: 3.357300 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Duplicate assembly 'System.Runtime.CompilerServices.Unsafe.dll' with different versions detected, using 'Packages/com.bezi.sidekick/Plugins/System.Runtime.CompilerServices.Unsafe.dll, AssemblyName=System.Runtime.CompilerServices.Unsafe, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a' and ignoring 'Assets/Plugins/Protobuf/System.Runtime.CompilerServices.Unsafe.dll, AssemblyName=System.Runtime.CompilerServices.Unsafe, Version=4.0.4.1, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a'.Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.859 seconds
OnLevelWasLoaded was found on DOTweenComponent
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
Refreshing native plugins compatible for Editor in 0.73 ms, found 3 plugins.
OnLevelWasLoaded was found on DOTweenComponent
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
Assertion failed on expression: 'pred(*previous, *i)'
Native extension for Android target not found
Native extension for WindowsStandalone target not found
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Refreshing native plugins compatible for Editor in 0.93 ms, found 3 plugins.
SceneCameraResetButton: Initialized.
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:Log (object)
SceneCameraResetButton:.cctor () (at Assets/Editor/Tools/SceneCameraResetButton.cs:15)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: Assets/Editor/Tools/SceneCameraResetButton.cs Line: 15)

The project currently uses the compatibility mode where the Render Graph API is disabled. Support for this mode will be removed in future Unity versions. Migrate existing ScriptableRenderPasses to the new RenderGraph API. After the migration, disable the compatibility mode in Edit > Projects Settings > Graphics > Render Graph.
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogWarning (object)
UnityEngine.Rendering.Universal.WarnUsingNonRenderGraph:EmitConsoleWarning () (at ./Packages/com.unity.render-pipelines.universal/Runtime/Data/UniversalRenderPipelineAsset.cs:427)
System.Reflection.RuntimeMethodInfo:Invoke (object,System.Reflection.BindingFlags,System.Reflection.Binder,object[],System.Globalization.CultureInfo)
System.Reflection.MethodBase:Invoke (object,object[])
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadMethodAttributes ()

(Filename: ./Packages/com.unity.render-pipelines.universal/Runtime/Data/UniversalRenderPipelineAsset.cs Line: 427)

Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.944 seconds
Domain Reload Profiling: 1808ms
	BeginReloadAssembly (157ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (4ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (54ms)
	RebuildCommonClasses (24ms)
	RebuildNativeTypeToScriptingClass (8ms)
	initialDomainReloadingComplete (41ms)
	LoadAllAssembliesAndSetupDomain (632ms)
		LoadAssemblies (381ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (316ms)
			TypeCache.Refresh (11ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (285ms)
			ResolveRequiredComponents (14ms)
	FinalizeReload (945ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (734ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (8ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (99ms)
			ProcessInitializeOnLoadAttributes (479ms)
			ProcessInitializeOnLoadMethodAttributes (141ms)
			AfterProcessingInitializeOnLoad (3ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (8ms)
Refreshing native plugins compatible for Editor in 1.30 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 29 Unused Serialized files (Serialized files now loaded: 0)
Unloading 8934 unused Assets / (7.6 MB). Loaded Objects now: 10127.
Memory consumption went from 240.4 MB to 232.8 MB.
Total: 30.096900 ms (FindLiveObjects: 0.950900 ms CreateObjectMapping: 0.829000 ms MarkObjects: 24.883100 ms  DeleteObjects: 3.431700 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Duplicate assembly 'System.Runtime.CompilerServices.Unsafe.dll' with different versions detected, using 'Packages/com.bezi.sidekick/Plugins/System.Runtime.CompilerServices.Unsafe.dll, AssemblyName=System.Runtime.CompilerServices.Unsafe, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a' and ignoring 'Assets/Plugins/Protobuf/System.Runtime.CompilerServices.Unsafe.dll, AssemblyName=System.Runtime.CompilerServices.Unsafe, Version=4.0.4.1, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a'.Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.823 seconds
OnLevelWasLoaded was found on DOTweenComponent
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
Refreshing native plugins compatible for Editor in 0.80 ms, found 3 plugins.
OnLevelWasLoaded was found on DOTweenComponent
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
Assertion failed on expression: 'pred(*previous, *i)'
Native extension for Android target not found
Native extension for WindowsStandalone target not found
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Refreshing native plugins compatible for Editor in 0.79 ms, found 3 plugins.
SceneCameraResetButton: Initialized.
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:Log (object)
SceneCameraResetButton:.cctor () (at Assets/Editor/Tools/SceneCameraResetButton.cs:15)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: Assets/Editor/Tools/SceneCameraResetButton.cs Line: 15)

The project currently uses the compatibility mode where the Render Graph API is disabled. Support for this mode will be removed in future Unity versions. Migrate existing ScriptableRenderPasses to the new RenderGraph API. After the migration, disable the compatibility mode in Edit > Projects Settings > Graphics > Render Graph.
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogWarning (object)
UnityEngine.Rendering.Universal.WarnUsingNonRenderGraph:EmitConsoleWarning () (at ./Packages/com.unity.render-pipelines.universal/Runtime/Data/UniversalRenderPipelineAsset.cs:427)
System.Reflection.RuntimeMethodInfo:Invoke (object,System.Reflection.BindingFlags,System.Reflection.Binder,object[],System.Globalization.CultureInfo)
System.Reflection.MethodBase:Invoke (object,object[])
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadMethodAttributes ()

(Filename: ./Packages/com.unity.render-pipelines.universal/Runtime/Data/UniversalRenderPipelineAsset.cs Line: 427)

Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.901 seconds
Domain Reload Profiling: 1728ms
	BeginReloadAssembly (148ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (4ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (48ms)
	RebuildCommonClasses (23ms)
	RebuildNativeTypeToScriptingClass (9ms)
	initialDomainReloadingComplete (39ms)
	LoadAllAssembliesAndSetupDomain (607ms)
		LoadAssemblies (366ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (306ms)
			TypeCache.Refresh (11ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (273ms)
			ResolveRequiredComponents (15ms)
	FinalizeReload (902ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (704ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (7ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (91ms)
			ProcessInitializeOnLoadAttributes (461ms)
			ProcessInitializeOnLoadMethodAttributes (137ms)
			AfterProcessingInitializeOnLoad (3ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (8ms)
Refreshing native plugins compatible for Editor in 1.59 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 29 Unused Serialized files (Serialized files now loaded: 0)
Unloading 8934 unused Assets / (7.7 MB). Loaded Objects now: 10129.
Memory consumption went from 240.3 MB to 232.7 MB.
Total: 21.956000 ms (FindLiveObjects: 0.767600 ms CreateObjectMapping: 0.568100 ms MarkObjects: 17.667900 ms  DeleteObjects: 2.951100 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
OnLevelWasLoaded was found on DOTweenComponent
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
Refreshing native plugins compatible for Editor in 1.31 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 29 Unused Serialized files (Serialized files now loaded: 0)
Unloading 8921 unused Assets / (7.4 MB). Loaded Objects now: 10129.
Memory consumption went from 240.5 MB to 233.0 MB.
Total: 34.459200 ms (FindLiveObjects: 0.943800 ms CreateObjectMapping: 0.774800 ms MarkObjects: 28.887900 ms  DeleteObjects: 3.850700 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Duplicate assembly 'System.Runtime.CompilerServices.Unsafe.dll' with different versions detected, using 'Packages/com.bezi.sidekick/Plugins/System.Runtime.CompilerServices.Unsafe.dll, AssemblyName=System.Runtime.CompilerServices.Unsafe, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a' and ignoring 'Assets/Plugins/Protobuf/System.Runtime.CompilerServices.Unsafe.dll, AssemblyName=System.Runtime.CompilerServices.Unsafe, Version=4.0.4.1, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a'.Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.965 seconds
OnLevelWasLoaded was found on DOTweenComponent
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
Refreshing native plugins compatible for Editor in 0.85 ms, found 3 plugins.
OnLevelWasLoaded was found on DOTweenComponent
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
Assertion failed on expression: 'pred(*previous, *i)'
Native extension for Android target not found
Native extension for WindowsStandalone target not found
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Refreshing native plugins compatible for Editor in 0.77 ms, found 3 plugins.
SceneCameraResetButton: Initialized.
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:Log (object)
SceneCameraResetButton:.cctor () (at Assets/Editor/Tools/SceneCameraResetButton.cs:15)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: Assets/Editor/Tools/SceneCameraResetButton.cs Line: 15)

The project currently uses the compatibility mode where the Render Graph API is disabled. Support for this mode will be removed in future Unity versions. Migrate existing ScriptableRenderPasses to the new RenderGraph API. After the migration, disable the compatibility mode in Edit > Projects Settings > Graphics > Render Graph.
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogWarning (object)
UnityEngine.Rendering.Universal.WarnUsingNonRenderGraph:EmitConsoleWarning () (at ./Packages/com.unity.render-pipelines.universal/Runtime/Data/UniversalRenderPipelineAsset.cs:427)
System.Reflection.RuntimeMethodInfo:Invoke (object,System.Reflection.BindingFlags,System.Reflection.Binder,object[],System.Globalization.CultureInfo)
System.Reflection.MethodBase:Invoke (object,object[])
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadMethodAttributes ()

(Filename: ./Packages/com.unity.render-pipelines.universal/Runtime/Data/UniversalRenderPipelineAsset.cs Line: 427)

Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.930 seconds
Domain Reload Profiling: 1897ms
	BeginReloadAssembly (161ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (4ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (51ms)
	RebuildCommonClasses (27ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (43ms)
	LoadAllAssembliesAndSetupDomain (726ms)
		LoadAssemblies (401ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (397ms)
			TypeCache.Refresh (67ms)
				TypeCache.ScanAssembly (45ms)
			BuildScriptInfoCaches (311ms)
			ResolveRequiredComponents (14ms)
	FinalizeReload (930ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (737ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (7ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (90ms)
			ProcessInitializeOnLoadAttributes (485ms)
			ProcessInitializeOnLoadMethodAttributes (147ms)
			AfterProcessingInitializeOnLoad (3ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (7ms)
Refreshing native plugins compatible for Editor in 1.69 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 29 Unused Serialized files (Serialized files now loaded: 0)
Unloading 8934 unused Assets / (8.8 MB). Loaded Objects now: 10131.
Memory consumption went from 240.2 MB to 231.4 MB.
Total: 28.673900 ms (FindLiveObjects: 0.848700 ms CreateObjectMapping: 0.715100 ms MarkObjects: 22.625800 ms  DeleteObjects: 4.482600 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
OnLevelWasLoaded was found on DOTweenComponent
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
Refreshing native plugins compatible for Editor in 1.29 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 29 Unused Serialized files (Serialized files now loaded: 0)
Unloading 8921 unused Assets / (6.9 MB). Loaded Objects now: 10131.
Memory consumption went from 240.5 MB to 233.6 MB.
Total: 27.437800 ms (FindLiveObjects: 0.861200 ms CreateObjectMapping: 0.690000 ms MarkObjects: 22.316300 ms  DeleteObjects: 3.569200 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Duplicate assembly 'System.Runtime.CompilerServices.Unsafe.dll' with different versions detected, using 'Packages/com.bezi.sidekick/Plugins/System.Runtime.CompilerServices.Unsafe.dll, AssemblyName=System.Runtime.CompilerServices.Unsafe, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a' and ignoring 'Assets/Plugins/Protobuf/System.Runtime.CompilerServices.Unsafe.dll, AssemblyName=System.Runtime.CompilerServices.Unsafe, Version=4.0.4.1, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a'.Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.795 seconds
OnLevelWasLoaded was found on DOTweenComponent
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
Refreshing native plugins compatible for Editor in 0.90 ms, found 3 plugins.
OnLevelWasLoaded was found on DOTweenComponent
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
Assertion failed on expression: 'pred(*previous, *i)'
Native extension for Android target not found
Native extension for WindowsStandalone target not found
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Refreshing native plugins compatible for Editor in 0.78 ms, found 3 plugins.
SceneCameraResetButton: Initialized.
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:Log (object)
SceneCameraResetButton:.cctor () (at Assets/Editor/Tools/SceneCameraResetButton.cs:15)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: Assets/Editor/Tools/SceneCameraResetButton.cs Line: 15)

The project currently uses the compatibility mode where the Render Graph API is disabled. Support for this mode will be removed in future Unity versions. Migrate existing ScriptableRenderPasses to the new RenderGraph API. After the migration, disable the compatibility mode in Edit > Projects Settings > Graphics > Render Graph.
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogWarning (object)
UnityEngine.Rendering.Universal.WarnUsingNonRenderGraph:EmitConsoleWarning () (at ./Packages/com.unity.render-pipelines.universal/Runtime/Data/UniversalRenderPipelineAsset.cs:427)
System.Reflection.RuntimeMethodInfo:Invoke (object,System.Reflection.BindingFlags,System.Reflection.Binder,object[],System.Globalization.CultureInfo)
System.Reflection.MethodBase:Invoke (object,object[])
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadMethodAttributes ()

(Filename: ./Packages/com.unity.render-pipelines.universal/Runtime/Data/UniversalRenderPipelineAsset.cs Line: 427)

Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.885 seconds
Domain Reload Profiling: 1684ms
	BeginReloadAssembly (143ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (4ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (50ms)
	RebuildCommonClasses (23ms)
	RebuildNativeTypeToScriptingClass (8ms)
	initialDomainReloadingComplete (39ms)
	LoadAllAssembliesAndSetupDomain (585ms)
		LoadAssemblies (341ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (302ms)
			TypeCache.Refresh (36ms)
				TypeCache.ScanAssembly (24ms)
			BuildScriptInfoCaches (246ms)
			ResolveRequiredComponents (13ms)
	FinalizeReload (886ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (694ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (7ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (87ms)
			ProcessInitializeOnLoadAttributes (451ms)
			ProcessInitializeOnLoadMethodAttributes (142ms)
			AfterProcessingInitializeOnLoad (3ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (8ms)
Refreshing native plugins compatible for Editor in 1.62 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 29 Unused Serialized files (Serialized files now loaded: 0)
Unloading 8934 unused Assets / (7.8 MB). Loaded Objects now: 10133.
Memory consumption went from 240.5 MB to 232.7 MB.
Total: 26.672300 ms (FindLiveObjects: 0.851100 ms CreateObjectMapping: 0.722700 ms MarkObjects: 21.357900 ms  DeleteObjects: 3.739100 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
OnLevelWasLoaded was found on DOTweenComponent
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
Refreshing native plugins compatible for Editor in 1.46 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 29 Unused Serialized files (Serialized files now loaded: 0)
Unloading 8921 unused Assets / (7.3 MB). Loaded Objects now: 10133.
Memory consumption went from 240.6 MB to 233.3 MB.
Total: 34.139200 ms (FindLiveObjects: 0.971700 ms CreateObjectMapping: 0.719700 ms MarkObjects: 28.103300 ms  DeleteObjects: 4.342500 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Duplicate assembly 'System.Runtime.CompilerServices.Unsafe.dll' with different versions detected, using 'Packages/com.bezi.sidekick/Plugins/System.Runtime.CompilerServices.Unsafe.dll, AssemblyName=System.Runtime.CompilerServices.Unsafe, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a' and ignoring 'Assets/Plugins/Protobuf/System.Runtime.CompilerServices.Unsafe.dll, AssemblyName=System.Runtime.CompilerServices.Unsafe, Version=4.0.4.1, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a'.Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.828 seconds
OnLevelWasLoaded was found on DOTweenComponent
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
Refreshing native plugins compatible for Editor in 0.68 ms, found 3 plugins.
OnLevelWasLoaded was found on DOTweenComponent
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
Assertion failed on expression: 'pred(*previous, *i)'
Native extension for Android target not found
Native extension for WindowsStandalone target not found
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Refreshing native plugins compatible for Editor in 0.72 ms, found 3 plugins.
SceneCameraResetButton: Initialized.
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:Log (object)
SceneCameraResetButton:.cctor () (at Assets/Editor/Tools/SceneCameraResetButton.cs:15)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: Assets/Editor/Tools/SceneCameraResetButton.cs Line: 15)

The project currently uses the compatibility mode where the Render Graph API is disabled. Support for this mode will be removed in future Unity versions. Migrate existing ScriptableRenderPasses to the new RenderGraph API. After the migration, disable the compatibility mode in Edit > Projects Settings > Graphics > Render Graph.
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogWarning (object)
UnityEngine.Rendering.Universal.WarnUsingNonRenderGraph:EmitConsoleWarning () (at ./Packages/com.unity.render-pipelines.universal/Runtime/Data/UniversalRenderPipelineAsset.cs:427)
System.Reflection.RuntimeMethodInfo:Invoke (object,System.Reflection.BindingFlags,System.Reflection.Binder,object[],System.Globalization.CultureInfo)
System.Reflection.MethodBase:Invoke (object,object[])
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadMethodAttributes ()

(Filename: ./Packages/com.unity.render-pipelines.universal/Runtime/Data/UniversalRenderPipelineAsset.cs Line: 427)

Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.967 seconds
Domain Reload Profiling: 1800ms
	BeginReloadAssembly (168ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (5ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (67ms)
	RebuildCommonClasses (23ms)
	RebuildNativeTypeToScriptingClass (9ms)
	initialDomainReloadingComplete (37ms)
	LoadAllAssembliesAndSetupDomain (595ms)
		LoadAssemblies (344ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (313ms)
			TypeCache.Refresh (39ms)
				TypeCache.ScanAssembly (26ms)
			BuildScriptInfoCaches (248ms)
			ResolveRequiredComponents (20ms)
	FinalizeReload (967ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (772ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (7ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (99ms)
			ProcessInitializeOnLoadAttributes (476ms)
			ProcessInitializeOnLoadMethodAttributes (182ms)
			AfterProcessingInitializeOnLoad (3ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (8ms)
Refreshing native plugins compatible for Editor in 1.25 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 29 Unused Serialized files (Serialized files now loaded: 0)
Unloading 8934 unused Assets / (6.4 MB). Loaded Objects now: 10135.
Memory consumption went from 240.5 MB to 234.1 MB.
Total: 27.414600 ms (FindLiveObjects: 0.869500 ms CreateObjectMapping: 0.832500 ms MarkObjects: 22.789900 ms  DeleteObjects: 2.921300 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Duplicate assembly 'System.Runtime.CompilerServices.Unsafe.dll' with different versions detected, using 'Packages/com.bezi.sidekick/Plugins/System.Runtime.CompilerServices.Unsafe.dll, AssemblyName=System.Runtime.CompilerServices.Unsafe, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a' and ignoring 'Assets/Plugins/Protobuf/System.Runtime.CompilerServices.Unsafe.dll, AssemblyName=System.Runtime.CompilerServices.Unsafe, Version=4.0.4.1, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a'.Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.807 seconds
OnLevelWasLoaded was found on DOTweenComponent
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
Refreshing native plugins compatible for Editor in 0.61 ms, found 3 plugins.
OnLevelWasLoaded was found on DOTweenComponent
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
Assertion failed on expression: 'pred(*previous, *i)'
Native extension for Android target not found
Native extension for WindowsStandalone target not found
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Refreshing native plugins compatible for Editor in 0.72 ms, found 3 plugins.
SceneCameraResetButton: Initialized.
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:Log (object)
SceneCameraResetButton:.cctor () (at Assets/Editor/Tools/SceneCameraResetButton.cs:15)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: Assets/Editor/Tools/SceneCameraResetButton.cs Line: 15)

The project currently uses the compatibility mode where the Render Graph API is disabled. Support for this mode will be removed in future Unity versions. Migrate existing ScriptableRenderPasses to the new RenderGraph API. After the migration, disable the compatibility mode in Edit > Projects Settings > Graphics > Render Graph.
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogWarning (object)
UnityEngine.Rendering.Universal.WarnUsingNonRenderGraph:EmitConsoleWarning () (at ./Packages/com.unity.render-pipelines.universal/Runtime/Data/UniversalRenderPipelineAsset.cs:427)
System.Reflection.RuntimeMethodInfo:Invoke (object,System.Reflection.BindingFlags,System.Reflection.Binder,object[],System.Globalization.CultureInfo)
System.Reflection.MethodBase:Invoke (object,object[])
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadMethodAttributes ()

(Filename: ./Packages/com.unity.render-pipelines.universal/Runtime/Data/UniversalRenderPipelineAsset.cs Line: 427)

Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.899 seconds
Domain Reload Profiling: 1709ms
	BeginReloadAssembly (146ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (4ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (51ms)
	RebuildCommonClasses (23ms)
	RebuildNativeTypeToScriptingClass (11ms)
	initialDomainReloadingComplete (36ms)
	LoadAllAssembliesAndSetupDomain (594ms)
		LoadAssemblies (357ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (295ms)
			TypeCache.Refresh (12ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (264ms)
			ResolveRequiredComponents (14ms)
	FinalizeReload (899ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (708ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (7ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (93ms)
			ProcessInitializeOnLoadAttributes (461ms)
			ProcessInitializeOnLoadMethodAttributes (140ms)
			AfterProcessingInitializeOnLoad (3ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (7ms)
Refreshing native plugins compatible for Editor in 1.29 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 29 Unused Serialized files (Serialized files now loaded: 0)
Unloading 8934 unused Assets / (7.2 MB). Loaded Objects now: 10137.
Memory consumption went from 240.4 MB to 233.2 MB.
Total: 29.533600 ms (FindLiveObjects: 0.816700 ms CreateObjectMapping: 0.853300 ms MarkObjects: 24.225100 ms  DeleteObjects: 3.637100 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
OnLevelWasLoaded was found on DOTweenComponent
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
Refreshing native plugins compatible for Editor in 1.52 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 29 Unused Serialized files (Serialized files now loaded: 0)
Unloading 8921 unused Assets / (7.6 MB). Loaded Objects now: 10137.
Memory consumption went from 240.6 MB to 233.0 MB.
Total: 26.961700 ms (FindLiveObjects: 0.861700 ms CreateObjectMapping: 0.669800 ms MarkObjects: 21.783400 ms  DeleteObjects: 3.645000 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Duplicate assembly 'System.Runtime.CompilerServices.Unsafe.dll' with different versions detected, using 'Packages/com.bezi.sidekick/Plugins/System.Runtime.CompilerServices.Unsafe.dll, AssemblyName=System.Runtime.CompilerServices.Unsafe, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a' and ignoring 'Assets/Plugins/Protobuf/System.Runtime.CompilerServices.Unsafe.dll, AssemblyName=System.Runtime.CompilerServices.Unsafe, Version=4.0.4.1, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a'.Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.833 seconds
OnLevelWasLoaded was found on DOTweenComponent
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
Refreshing native plugins compatible for Editor in 0.61 ms, found 3 plugins.
OnLevelWasLoaded was found on DOTweenComponent
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
Assertion failed on expression: 'pred(*previous, *i)'
Native extension for Android target not found
Native extension for WindowsStandalone target not found
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Refreshing native plugins compatible for Editor in 0.77 ms, found 3 plugins.
SceneCameraResetButton: Initialized.
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:Log (object)
SceneCameraResetButton:.cctor () (at Assets/Editor/Tools/SceneCameraResetButton.cs:15)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: Assets/Editor/Tools/SceneCameraResetButton.cs Line: 15)

The project currently uses the compatibility mode where the Render Graph API is disabled. Support for this mode will be removed in future Unity versions. Migrate existing ScriptableRenderPasses to the new RenderGraph API. After the migration, disable the compatibility mode in Edit > Projects Settings > Graphics > Render Graph.
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogWarning (object)
UnityEngine.Rendering.Universal.WarnUsingNonRenderGraph:EmitConsoleWarning () (at ./Packages/com.unity.render-pipelines.universal/Runtime/Data/UniversalRenderPipelineAsset.cs:427)
System.Reflection.RuntimeMethodInfo:Invoke (object,System.Reflection.BindingFlags,System.Reflection.Binder,object[],System.Globalization.CultureInfo)
System.Reflection.MethodBase:Invoke (object,object[])
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadMethodAttributes ()

(Filename: ./Packages/com.unity.render-pipelines.universal/Runtime/Data/UniversalRenderPipelineAsset.cs Line: 427)

Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.913 seconds
Domain Reload Profiling: 1750ms
	BeginReloadAssembly (142ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (3ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (45ms)
	RebuildCommonClasses (25ms)
	RebuildNativeTypeToScriptingClass (9ms)
	initialDomainReloadingComplete (37ms)
	LoadAllAssembliesAndSetupDomain (623ms)
		LoadAssemblies (374ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (307ms)
			TypeCache.Refresh (48ms)
				TypeCache.ScanAssembly (27ms)
			BuildScriptInfoCaches (240ms)
			ResolveRequiredComponents (14ms)
	FinalizeReload (913ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (719ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (8ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (94ms)
			ProcessInitializeOnLoadAttributes (474ms)
			ProcessInitializeOnLoadMethodAttributes (136ms)
			AfterProcessingInitializeOnLoad (3ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (8ms)
Refreshing native plugins compatible for Editor in 1.24 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 29 Unused Serialized files (Serialized files now loaded: 0)
Unloading 8934 unused Assets / (6.7 MB). Loaded Objects now: 10139.
Memory consumption went from 240.5 MB to 233.8 MB.
Total: 28.953400 ms (FindLiveObjects: 0.839100 ms CreateObjectMapping: 0.539800 ms MarkObjects: 24.217300 ms  DeleteObjects: 3.355800 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
OnLevelWasLoaded was found on DOTweenComponent
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
Refreshing native plugins compatible for Editor in 1.54 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 29 Unused Serialized files (Serialized files now loaded: 0)
Unloading 8921 unused Assets / (6.3 MB). Loaded Objects now: 10139.
Memory consumption went from 240.6 MB to 234.3 MB.
Total: 28.997100 ms (FindLiveObjects: 1.003500 ms CreateObjectMapping: 0.924600 ms MarkObjects: 24.011000 ms  DeleteObjects: 3.056800 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Duplicate assembly 'System.Runtime.CompilerServices.Unsafe.dll' with different versions detected, using 'Packages/com.bezi.sidekick/Plugins/System.Runtime.CompilerServices.Unsafe.dll, AssemblyName=System.Runtime.CompilerServices.Unsafe, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a' and ignoring 'Assets/Plugins/Protobuf/System.Runtime.CompilerServices.Unsafe.dll, AssemblyName=System.Runtime.CompilerServices.Unsafe, Version=4.0.4.1, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a'.Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.813 seconds
OnLevelWasLoaded was found on DOTweenComponent
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
Refreshing native plugins compatible for Editor in 0.59 ms, found 3 plugins.
OnLevelWasLoaded was found on DOTweenComponent
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
Assertion failed on expression: 'pred(*previous, *i)'
Native extension for Android target not found
Native extension for WindowsStandalone target not found
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Refreshing native plugins compatible for Editor in 0.69 ms, found 3 plugins.
SceneCameraResetButton: Initialized.
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:Log (object)
SceneCameraResetButton:.cctor () (at Assets/Editor/Tools/SceneCameraResetButton.cs:15)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: Assets/Editor/Tools/SceneCameraResetButton.cs Line: 15)

The project currently uses the compatibility mode where the Render Graph API is disabled. Support for this mode will be removed in future Unity versions. Migrate existing ScriptableRenderPasses to the new RenderGraph API. After the migration, disable the compatibility mode in Edit > Projects Settings > Graphics > Render Graph.
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogWarning (object)
UnityEngine.Rendering.Universal.WarnUsingNonRenderGraph:EmitConsoleWarning () (at ./Packages/com.unity.render-pipelines.universal/Runtime/Data/UniversalRenderPipelineAsset.cs:427)
System.Reflection.RuntimeMethodInfo:Invoke (object,System.Reflection.BindingFlags,System.Reflection.Binder,object[],System.Globalization.CultureInfo)
System.Reflection.MethodBase:Invoke (object,object[])
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadMethodAttributes ()

(Filename: ./Packages/com.unity.render-pipelines.universal/Runtime/Data/UniversalRenderPipelineAsset.cs Line: 427)

Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.872 seconds
Domain Reload Profiling: 1688ms
	BeginReloadAssembly (157ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (4ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (55ms)
	RebuildCommonClasses (25ms)
	RebuildNativeTypeToScriptingClass (9ms)
	initialDomainReloadingComplete (38ms)
	LoadAllAssembliesAndSetupDomain (588ms)
		LoadAssemblies (370ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (285ms)
			TypeCache.Refresh (36ms)
				TypeCache.ScanAssembly (23ms)
			BuildScriptInfoCaches (229ms)
			ResolveRequiredComponents (13ms)
	FinalizeReload (872ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (680ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (7ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (87ms)
			ProcessInitializeOnLoadAttributes (442ms)
			ProcessInitializeOnLoadMethodAttributes (137ms)
			AfterProcessingInitializeOnLoad (3ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (8ms)
Refreshing native plugins compatible for Editor in 1.34 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 29 Unused Serialized files (Serialized files now loaded: 0)
Unloading 8934 unused Assets / (6.3 MB). Loaded Objects now: 10141.
Memory consumption went from 240.3 MB to 234.0 MB.
Total: 23.584400 ms (FindLiveObjects: 0.825900 ms CreateObjectMapping: 0.751200 ms MarkObjects: 19.098600 ms  DeleteObjects: 2.907400 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
OnLevelWasLoaded was found on DOTweenComponent
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
Refreshing native plugins compatible for Editor in 1.55 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 29 Unused Serialized files (Serialized files now loaded: 0)
Unloading 8921 unused Assets / (8.0 MB). Loaded Objects now: 10141.
Memory consumption went from 240.4 MB to 232.4 MB.
Total: 30.261400 ms (FindLiveObjects: 0.897700 ms CreateObjectMapping: 0.672700 ms MarkObjects: 24.984800 ms  DeleteObjects: 3.704700 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Duplicate assembly 'System.Runtime.CompilerServices.Unsafe.dll' with different versions detected, using 'Packages/com.bezi.sidekick/Plugins/System.Runtime.CompilerServices.Unsafe.dll, AssemblyName=System.Runtime.CompilerServices.Unsafe, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a' and ignoring 'Assets/Plugins/Protobuf/System.Runtime.CompilerServices.Unsafe.dll, AssemblyName=System.Runtime.CompilerServices.Unsafe, Version=4.0.4.1, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a'.Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.817 seconds
OnLevelWasLoaded was found on DOTweenComponent
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
Refreshing native plugins compatible for Editor in 0.72 ms, found 3 plugins.
OnLevelWasLoaded was found on DOTweenComponent
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
Assertion failed on expression: 'pred(*previous, *i)'
Native extension for Android target not found
Native extension for WindowsStandalone target not found
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Refreshing native plugins compatible for Editor in 0.76 ms, found 3 plugins.
SceneCameraResetButton: Initialized.
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:Log (object)
SceneCameraResetButton:.cctor () (at Assets/Editor/Tools/SceneCameraResetButton.cs:15)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: Assets/Editor/Tools/SceneCameraResetButton.cs Line: 15)

The project currently uses the compatibility mode where the Render Graph API is disabled. Support for this mode will be removed in future Unity versions. Migrate existing ScriptableRenderPasses to the new RenderGraph API. After the migration, disable the compatibility mode in Edit > Projects Settings > Graphics > Render Graph.
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogWarning (object)
UnityEngine.Rendering.Universal.WarnUsingNonRenderGraph:EmitConsoleWarning () (at ./Packages/com.unity.render-pipelines.universal/Runtime/Data/UniversalRenderPipelineAsset.cs:427)
System.Reflection.RuntimeMethodInfo:Invoke (object,System.Reflection.BindingFlags,System.Reflection.Binder,object[],System.Globalization.CultureInfo)
System.Reflection.MethodBase:Invoke (object,object[])
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadMethodAttributes ()

(Filename: ./Packages/com.unity.render-pipelines.universal/Runtime/Data/UniversalRenderPipelineAsset.cs Line: 427)

Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.917 seconds
Domain Reload Profiling: 1739ms
	BeginReloadAssembly (149ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (4ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (52ms)
	RebuildCommonClasses (25ms)
	RebuildNativeTypeToScriptingClass (8ms)
	initialDomainReloadingComplete (38ms)
	LoadAllAssembliesAndSetupDomain (601ms)
		LoadAssemblies (352ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (309ms)
			TypeCache.Refresh (40ms)
				TypeCache.ScanAssembly (25ms)
			BuildScriptInfoCaches (249ms)
			ResolveRequiredComponents (14ms)
	FinalizeReload (917ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (720ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (7ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (91ms)
			ProcessInitializeOnLoadAttributes (481ms)
			ProcessInitializeOnLoadMethodAttributes (134ms)
			AfterProcessingInitializeOnLoad (3ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (7ms)
Refreshing native plugins compatible for Editor in 1.28 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 29 Unused Serialized files (Serialized files now loaded: 0)
Unloading 8934 unused Assets / (8.0 MB). Loaded Objects now: 10143.
Memory consumption went from 240.5 MB to 232.5 MB.
Total: 25.921400 ms (FindLiveObjects: 0.807400 ms CreateObjectMapping: 0.879500 ms MarkObjects: 20.236000 ms  DeleteObjects: 3.997000 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
OnLevelWasLoaded was found on DOTweenComponent
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
Refreshing native plugins compatible for Editor in 1.57 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 29 Unused Serialized files (Serialized files now loaded: 0)
Unloading 8921 unused Assets / (7.8 MB). Loaded Objects now: 10143.
Memory consumption went from 240.6 MB to 232.8 MB.
Total: 33.690400 ms (FindLiveObjects: 0.842500 ms CreateObjectMapping: 0.669700 ms MarkObjects: 28.145700 ms  DeleteObjects: 4.031000 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Duplicate assembly 'System.Runtime.CompilerServices.Unsafe.dll' with different versions detected, using 'Packages/com.bezi.sidekick/Plugins/System.Runtime.CompilerServices.Unsafe.dll, AssemblyName=System.Runtime.CompilerServices.Unsafe, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a' and ignoring 'Assets/Plugins/Protobuf/System.Runtime.CompilerServices.Unsafe.dll, AssemblyName=System.Runtime.CompilerServices.Unsafe, Version=4.0.4.1, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a'.Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.871 seconds
OnLevelWasLoaded was found on DOTweenComponent
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
Refreshing native plugins compatible for Editor in 0.85 ms, found 3 plugins.
OnLevelWasLoaded was found on DOTweenComponent
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
Assertion failed on expression: 'pred(*previous, *i)'
Native extension for Android target not found
Native extension for WindowsStandalone target not found
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Refreshing native plugins compatible for Editor in 0.84 ms, found 3 plugins.
SceneCameraResetButton: Initialized.
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:Log (object)
SceneCameraResetButton:.cctor () (at Assets/Editor/Tools/SceneCameraResetButton.cs:15)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: Assets/Editor/Tools/SceneCameraResetButton.cs Line: 15)

The project currently uses the compatibility mode where the Render Graph API is disabled. Support for this mode will be removed in future Unity versions. Migrate existing ScriptableRenderPasses to the new RenderGraph API. After the migration, disable the compatibility mode in Edit > Projects Settings > Graphics > Render Graph.
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogWarning (object)
UnityEngine.Rendering.Universal.WarnUsingNonRenderGraph:EmitConsoleWarning () (at ./Packages/com.unity.render-pipelines.universal/Runtime/Data/UniversalRenderPipelineAsset.cs:427)
System.Reflection.RuntimeMethodInfo:Invoke (object,System.Reflection.BindingFlags,System.Reflection.Binder,object[],System.Globalization.CultureInfo)
System.Reflection.MethodBase:Invoke (object,object[])
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadMethodAttributes ()

(Filename: ./Packages/com.unity.render-pipelines.universal/Runtime/Data/UniversalRenderPipelineAsset.cs Line: 427)

Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.965 seconds
Domain Reload Profiling: 1840ms
	BeginReloadAssembly (152ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (4ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (53ms)
	RebuildCommonClasses (24ms)
	RebuildNativeTypeToScriptingClass (9ms)
	initialDomainReloadingComplete (40ms)
	LoadAllAssembliesAndSetupDomain (650ms)
		LoadAssemblies (365ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (348ms)
			TypeCache.Refresh (40ms)
				TypeCache.ScanAssembly (25ms)
			BuildScriptInfoCaches (283ms)
			ResolveRequiredComponents (17ms)
	FinalizeReload (965ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (766ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (9ms)
			SetLoadedEditorAssemblies (5ms)
			BeforeProcessingInitializeOnLoad (93ms)
			ProcessInitializeOnLoadAttributes (502ms)
			ProcessInitializeOnLoadMethodAttributes (155ms)
			AfterProcessingInitializeOnLoad (3ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (10ms)
Refreshing native plugins compatible for Editor in 1.82 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 29 Unused Serialized files (Serialized files now loaded: 0)
Unloading 8934 unused Assets / (7.6 MB). Loaded Objects now: 10145.
Memory consumption went from 240.5 MB to 232.8 MB.
Total: 29.690400 ms (FindLiveObjects: 0.882800 ms CreateObjectMapping: 0.751400 ms MarkObjects: 23.914800 ms  DeleteObjects: 4.139900 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Duplicate assembly 'System.Runtime.CompilerServices.Unsafe.dll' with different versions detected, using 'Packages/com.bezi.sidekick/Plugins/System.Runtime.CompilerServices.Unsafe.dll, AssemblyName=System.Runtime.CompilerServices.Unsafe, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a' and ignoring 'Assets/Plugins/Protobuf/System.Runtime.CompilerServices.Unsafe.dll, AssemblyName=System.Runtime.CompilerServices.Unsafe, Version=4.0.4.1, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a'.Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.783 seconds
OnLevelWasLoaded was found on DOTweenComponent
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
Refreshing native plugins compatible for Editor in 0.60 ms, found 3 plugins.
OnLevelWasLoaded was found on DOTweenComponent
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
Assertion failed on expression: 'pred(*previous, *i)'
Native extension for Android target not found
Native extension for WindowsStandalone target not found
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Refreshing native plugins compatible for Editor in 0.77 ms, found 3 plugins.
SceneCameraResetButton: Initialized.
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:Log (object)
SceneCameraResetButton:.cctor () (at Assets/Editor/Tools/SceneCameraResetButton.cs:15)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: Assets/Editor/Tools/SceneCameraResetButton.cs Line: 15)

The project currently uses the compatibility mode where the Render Graph API is disabled. Support for this mode will be removed in future Unity versions. Migrate existing ScriptableRenderPasses to the new RenderGraph API. After the migration, disable the compatibility mode in Edit > Projects Settings > Graphics > Render Graph.
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogWarning (object)
UnityEngine.Rendering.Universal.WarnUsingNonRenderGraph:EmitConsoleWarning () (at ./Packages/com.unity.render-pipelines.universal/Runtime/Data/UniversalRenderPipelineAsset.cs:427)
System.Reflection.RuntimeMethodInfo:Invoke (object,System.Reflection.BindingFlags,System.Reflection.Binder,object[],System.Globalization.CultureInfo)
System.Reflection.MethodBase:Invoke (object,object[])
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadMethodAttributes ()

(Filename: ./Packages/com.unity.render-pipelines.universal/Runtime/Data/UniversalRenderPipelineAsset.cs Line: 427)

Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.904 seconds
Domain Reload Profiling: 1689ms
	BeginReloadAssembly (143ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (4ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (51ms)
	RebuildCommonClasses (24ms)
	RebuildNativeTypeToScriptingClass (9ms)
	initialDomainReloadingComplete (39ms)
	LoadAllAssembliesAndSetupDomain (571ms)
		LoadAssemblies (360ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (268ms)
			TypeCache.Refresh (9ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (238ms)
			ResolveRequiredComponents (14ms)
	FinalizeReload (904ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (711ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (7ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (91ms)
			ProcessInitializeOnLoadAttributes (459ms)
			ProcessInitializeOnLoadMethodAttributes (147ms)
			AfterProcessingInitializeOnLoad (3ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (8ms)
Refreshing native plugins compatible for Editor in 1.31 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 29 Unused Serialized files (Serialized files now loaded: 0)
Unloading 8934 unused Assets / (7.1 MB). Loaded Objects now: 10147.
Memory consumption went from 240.5 MB to 233.4 MB.
Total: 29.043700 ms (FindLiveObjects: 1.111600 ms CreateObjectMapping: 0.781000 ms MarkObjects: 24.175800 ms  DeleteObjects: 2.973900 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Duplicate assembly 'System.Runtime.CompilerServices.Unsafe.dll' with different versions detected, using 'Packages/com.bezi.sidekick/Plugins/System.Runtime.CompilerServices.Unsafe.dll, AssemblyName=System.Runtime.CompilerServices.Unsafe, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a' and ignoring 'Assets/Plugins/Protobuf/System.Runtime.CompilerServices.Unsafe.dll, AssemblyName=System.Runtime.CompilerServices.Unsafe, Version=4.0.4.1, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a'.Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.803 seconds
OnLevelWasLoaded was found on DOTweenComponent
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
Refreshing native plugins compatible for Editor in 0.71 ms, found 3 plugins.
OnLevelWasLoaded was found on DOTweenComponent
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
Assertion failed on expression: 'pred(*previous, *i)'
Native extension for Android target not found
Native extension for WindowsStandalone target not found
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Refreshing native plugins compatible for Editor in 0.78 ms, found 3 plugins.
SceneCameraResetButton: Initialized.
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:Log (object)
SceneCameraResetButton:.cctor () (at Assets/Editor/Tools/SceneCameraResetButton.cs:15)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: Assets/Editor/Tools/SceneCameraResetButton.cs Line: 15)

The project currently uses the compatibility mode where the Render Graph API is disabled. Support for this mode will be removed in future Unity versions. Migrate existing ScriptableRenderPasses to the new RenderGraph API. After the migration, disable the compatibility mode in Edit > Projects Settings > Graphics > Render Graph.
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogWarning (object)
UnityEngine.Rendering.Universal.WarnUsingNonRenderGraph:EmitConsoleWarning () (at ./Packages/com.unity.render-pipelines.universal/Runtime/Data/UniversalRenderPipelineAsset.cs:427)
System.Reflection.RuntimeMethodInfo:Invoke (object,System.Reflection.BindingFlags,System.Reflection.Binder,object[],System.Globalization.CultureInfo)
System.Reflection.MethodBase:Invoke (object,object[])
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadMethodAttributes ()

(Filename: ./Packages/com.unity.render-pipelines.universal/Runtime/Data/UniversalRenderPipelineAsset.cs Line: 427)

Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.914 seconds
Domain Reload Profiling: 1721ms
	BeginReloadAssembly (156ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (4ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (57ms)
	RebuildCommonClasses (25ms)
	RebuildNativeTypeToScriptingClass (9ms)
	initialDomainReloadingComplete (38ms)
	LoadAllAssembliesAndSetupDomain (578ms)
		LoadAssemblies (359ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (281ms)
			TypeCache.Refresh (10ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (249ms)
			ResolveRequiredComponents (14ms)
	FinalizeReload (914ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (720ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (7ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (92ms)
			ProcessInitializeOnLoadAttributes (468ms)
			ProcessInitializeOnLoadMethodAttributes (146ms)
			AfterProcessingInitializeOnLoad (3ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (8ms)
Refreshing native plugins compatible for Editor in 1.36 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 29 Unused Serialized files (Serialized files now loaded: 0)
Unloading 8934 unused Assets / (6.8 MB). Loaded Objects now: 10149.
Memory consumption went from 240.5 MB to 233.6 MB.
Total: 27.053500 ms (FindLiveObjects: 1.123200 ms CreateObjectMapping: 0.950300 ms MarkObjects: 21.778800 ms  DeleteObjects: 3.199100 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
OnLevelWasLoaded was found on DOTweenComponent
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
Refreshing native plugins compatible for Editor in 1.29 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 29 Unused Serialized files (Serialized files now loaded: 0)
Unloading 8921 unused Assets / (8.8 MB). Loaded Objects now: 10149.
Memory consumption went from 240.6 MB to 231.8 MB.
Total: 33.013500 ms (FindLiveObjects: 0.911400 ms CreateObjectMapping: 0.685100 ms MarkObjects: 25.053800 ms  DeleteObjects: 6.361400 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Duplicate assembly 'System.Runtime.CompilerServices.Unsafe.dll' with different versions detected, using 'Packages/com.bezi.sidekick/Plugins/System.Runtime.CompilerServices.Unsafe.dll, AssemblyName=System.Runtime.CompilerServices.Unsafe, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a' and ignoring 'Assets/Plugins/Protobuf/System.Runtime.CompilerServices.Unsafe.dll, AssemblyName=System.Runtime.CompilerServices.Unsafe, Version=4.0.4.1, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a'.Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.843 seconds
OnLevelWasLoaded was found on DOTweenComponent
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
Refreshing native plugins compatible for Editor in 0.92 ms, found 3 plugins.
OnLevelWasLoaded was found on DOTweenComponent
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
Assertion failed on expression: 'pred(*previous, *i)'
Native extension for Android target not found
Native extension for WindowsStandalone target not found
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Refreshing native plugins compatible for Editor in 1.65 ms, found 3 plugins.
SceneCameraResetButton: Initialized.
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:Log (object)
SceneCameraResetButton:.cctor () (at Assets/Editor/Tools/SceneCameraResetButton.cs:15)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: Assets/Editor/Tools/SceneCameraResetButton.cs Line: 15)

The project currently uses the compatibility mode where the Render Graph API is disabled. Support for this mode will be removed in future Unity versions. Migrate existing ScriptableRenderPasses to the new RenderGraph API. After the migration, disable the compatibility mode in Edit > Projects Settings > Graphics > Render Graph.
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogWarning (object)
UnityEngine.Rendering.Universal.WarnUsingNonRenderGraph:EmitConsoleWarning () (at ./Packages/com.unity.render-pipelines.universal/Runtime/Data/UniversalRenderPipelineAsset.cs:427)
System.Reflection.RuntimeMethodInfo:Invoke (object,System.Reflection.BindingFlags,System.Reflection.Binder,object[],System.Globalization.CultureInfo)
System.Reflection.MethodBase:Invoke (object,object[])
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadMethodAttributes ()

(Filename: ./Packages/com.unity.render-pipelines.universal/Runtime/Data/UniversalRenderPipelineAsset.cs Line: 427)

Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.926 seconds
Domain Reload Profiling: 1773ms
	BeginReloadAssembly (154ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (4ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (50ms)
	RebuildCommonClasses (24ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (39ms)
	LoadAllAssembliesAndSetupDomain (619ms)
		LoadAssemblies (373ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (312ms)
			TypeCache.Refresh (42ms)
				TypeCache.ScanAssembly (28ms)
			BuildScriptInfoCaches (249ms)
			ResolveRequiredComponents (15ms)
	FinalizeReload (926ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (729ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (8ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (91ms)
			ProcessInitializeOnLoadAttributes (485ms)
			ProcessInitializeOnLoadMethodAttributes (137ms)
			AfterProcessingInitializeOnLoad (3ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (8ms)
Refreshing native plugins compatible for Editor in 1.25 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 29 Unused Serialized files (Serialized files now loaded: 0)
Unloading 8934 unused Assets / (8.1 MB). Loaded Objects now: 10151.
Memory consumption went from 240.4 MB to 232.3 MB.
Total: 32.078500 ms (FindLiveObjects: 1.053900 ms CreateObjectMapping: 1.098400 ms MarkObjects: 25.297900 ms  DeleteObjects: 4.626600 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
OnLevelWasLoaded was found on DOTweenComponent
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
Refreshing native plugins compatible for Editor in 1.40 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 29 Unused Serialized files (Serialized files now loaded: 0)
Unloading 8921 unused Assets / (7.6 MB). Loaded Objects now: 10151.
Memory consumption went from 240.6 MB to 232.9 MB.
Total: 31.959100 ms (FindLiveObjects: 0.798700 ms CreateObjectMapping: 0.645300 ms MarkObjects: 25.916000 ms  DeleteObjects: 4.597200 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Duplicate assembly 'System.Runtime.CompilerServices.Unsafe.dll' with different versions detected, using 'Packages/com.bezi.sidekick/Plugins/System.Runtime.CompilerServices.Unsafe.dll, AssemblyName=System.Runtime.CompilerServices.Unsafe, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a' and ignoring 'Assets/Plugins/Protobuf/System.Runtime.CompilerServices.Unsafe.dll, AssemblyName=System.Runtime.CompilerServices.Unsafe, Version=4.0.4.1, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a'.Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.824 seconds
OnLevelWasLoaded was found on DOTweenComponent
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
Refreshing native plugins compatible for Editor in 0.82 ms, found 3 plugins.
OnLevelWasLoaded was found on DOTweenComponent
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
Assertion failed on expression: 'pred(*previous, *i)'
Native extension for Android target not found
Native extension for WindowsStandalone target not found
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Refreshing native plugins compatible for Editor in 0.96 ms, found 3 plugins.
SceneCameraResetButton: Initialized.
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:Log (object)
SceneCameraResetButton:.cctor () (at Assets/Editor/Tools/SceneCameraResetButton.cs:15)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: Assets/Editor/Tools/SceneCameraResetButton.cs Line: 15)

The project currently uses the compatibility mode where the Render Graph API is disabled. Support for this mode will be removed in future Unity versions. Migrate existing ScriptableRenderPasses to the new RenderGraph API. After the migration, disable the compatibility mode in Edit > Projects Settings > Graphics > Render Graph.
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogWarning (object)
UnityEngine.Rendering.Universal.WarnUsingNonRenderGraph:EmitConsoleWarning () (at ./Packages/com.unity.render-pipelines.universal/Runtime/Data/UniversalRenderPipelineAsset.cs:427)
System.Reflection.RuntimeMethodInfo:Invoke (object,System.Reflection.BindingFlags,System.Reflection.Binder,object[],System.Globalization.CultureInfo)
System.Reflection.MethodBase:Invoke (object,object[])
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadMethodAttributes ()

(Filename: ./Packages/com.unity.render-pipelines.universal/Runtime/Data/UniversalRenderPipelineAsset.cs Line: 427)

Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.914 seconds
Domain Reload Profiling: 1741ms
	BeginReloadAssembly (151ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (4ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (51ms)
	RebuildCommonClasses (24ms)
	RebuildNativeTypeToScriptingClass (8ms)
	initialDomainReloadingComplete (41ms)
	LoadAllAssembliesAndSetupDomain (602ms)
		LoadAssemblies (368ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (297ms)
			TypeCache.Refresh (35ms)
				TypeCache.ScanAssembly (22ms)
			BuildScriptInfoCaches (241ms)
			ResolveRequiredComponents (15ms)
	FinalizeReload (914ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (716ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (8ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (98ms)
			ProcessInitializeOnLoadAttributes (466ms)
			ProcessInitializeOnLoadMethodAttributes (138ms)
			AfterProcessingInitializeOnLoad (3ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (8ms)
Refreshing native plugins compatible for Editor in 1.48 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 29 Unused Serialized files (Serialized files now loaded: 0)
Unloading 8934 unused Assets / (7.2 MB). Loaded Objects now: 10153.
Memory consumption went from 240.5 MB to 233.3 MB.
Total: 25.770400 ms (FindLiveObjects: 0.871000 ms CreateObjectMapping: 0.778300 ms MarkObjects: 20.109900 ms  DeleteObjects: 4.009300 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Duplicate assembly 'System.Runtime.CompilerServices.Unsafe.dll' with different versions detected, using 'Packages/com.bezi.sidekick/Plugins/System.Runtime.CompilerServices.Unsafe.dll, AssemblyName=System.Runtime.CompilerServices.Unsafe, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a' and ignoring 'Assets/Plugins/Protobuf/System.Runtime.CompilerServices.Unsafe.dll, AssemblyName=System.Runtime.CompilerServices.Unsafe, Version=4.0.4.1, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a'.Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.808 seconds
OnLevelWasLoaded was found on DOTweenComponent
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
Refreshing native plugins compatible for Editor in 0.84 ms, found 3 plugins.
OnLevelWasLoaded was found on DOTweenComponent
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
Assertion failed on expression: 'pred(*previous, *i)'
Native extension for Android target not found
Native extension for WindowsStandalone target not found
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Refreshing native plugins compatible for Editor in 0.78 ms, found 3 plugins.
SceneCameraResetButton: Initialized.
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:Log (object)
SceneCameraResetButton:.cctor () (at Assets/Editor/Tools/SceneCameraResetButton.cs:15)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: Assets/Editor/Tools/SceneCameraResetButton.cs Line: 15)

The project currently uses the compatibility mode where the Render Graph API is disabled. Support for this mode will be removed in future Unity versions. Migrate existing ScriptableRenderPasses to the new RenderGraph API. After the migration, disable the compatibility mode in Edit > Projects Settings > Graphics > Render Graph.
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogWarning (object)
UnityEngine.Rendering.Universal.WarnUsingNonRenderGraph:EmitConsoleWarning () (at ./Packages/com.unity.render-pipelines.universal/Runtime/Data/UniversalRenderPipelineAsset.cs:427)
System.Reflection.RuntimeMethodInfo:Invoke (object,System.Reflection.BindingFlags,System.Reflection.Binder,object[],System.Globalization.CultureInfo)
System.Reflection.MethodBase:Invoke (object,object[])
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadMethodAttributes ()

(Filename: ./Packages/com.unity.render-pipelines.universal/Runtime/Data/UniversalRenderPipelineAsset.cs Line: 427)

Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.928 seconds
Domain Reload Profiling: 1740ms
	BeginReloadAssembly (161ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (4ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (55ms)
	RebuildCommonClasses (27ms)
	RebuildNativeTypeToScriptingClass (8ms)
	initialDomainReloadingComplete (44ms)
	LoadAllAssembliesAndSetupDomain (571ms)
		LoadAssemblies (364ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (273ms)
			TypeCache.Refresh (10ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (244ms)
			ResolveRequiredComponents (14ms)
	FinalizeReload (929ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (734ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (7ms)
			SetLoadedEditorAssemblies (5ms)
			BeforeProcessingInitializeOnLoad (94ms)
			ProcessInitializeOnLoadAttributes (475ms)
			ProcessInitializeOnLoadMethodAttributes (149ms)
			AfterProcessingInitializeOnLoad (3ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (8ms)
Refreshing native plugins compatible for Editor in 1.42 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 29 Unused Serialized files (Serialized files now loaded: 0)
Unloading 8934 unused Assets / (7.8 MB). Loaded Objects now: 10155.
Memory consumption went from 240.6 MB to 232.8 MB.
Total: 24.273300 ms (FindLiveObjects: 0.826800 ms CreateObjectMapping: 0.847200 ms MarkObjects: 18.670700 ms  DeleteObjects: 3.927200 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
OnLevelWasLoaded was found on DOTweenComponent
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
Refreshing native plugins compatible for Editor in 1.21 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 29 Unused Serialized files (Serialized files now loaded: 0)
Unloading 8921 unused Assets / (8.3 MB). Loaded Objects now: 10155.
Memory consumption went from 240.6 MB to 232.4 MB.
Total: 31.538900 ms (FindLiveObjects: 1.245300 ms CreateObjectMapping: 0.795800 ms MarkObjects: 24.454100 ms  DeleteObjects: 5.042100 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Duplicate assembly 'System.Runtime.CompilerServices.Unsafe.dll' with different versions detected, using 'Packages/com.bezi.sidekick/Plugins/System.Runtime.CompilerServices.Unsafe.dll, AssemblyName=System.Runtime.CompilerServices.Unsafe, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a' and ignoring 'Assets/Plugins/Protobuf/System.Runtime.CompilerServices.Unsafe.dll, AssemblyName=System.Runtime.CompilerServices.Unsafe, Version=4.0.4.1, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a'.Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.862 seconds
OnLevelWasLoaded was found on DOTweenComponent
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
Refreshing native plugins compatible for Editor in 0.76 ms, found 3 plugins.
OnLevelWasLoaded was found on DOTweenComponent
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
Assertion failed on expression: 'pred(*previous, *i)'
Native extension for Android target not found
Native extension for WindowsStandalone target not found
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Refreshing native plugins compatible for Editor in 0.81 ms, found 3 plugins.
SceneCameraResetButton: Initialized.
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:Log (object)
SceneCameraResetButton:.cctor () (at Assets/Editor/Tools/SceneCameraResetButton.cs:15)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: Assets/Editor/Tools/SceneCameraResetButton.cs Line: 15)

The project currently uses the compatibility mode where the Render Graph API is disabled. Support for this mode will be removed in future Unity versions. Migrate existing ScriptableRenderPasses to the new RenderGraph API. After the migration, disable the compatibility mode in Edit > Projects Settings > Graphics > Render Graph.
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogWarning (object)
UnityEngine.Rendering.Universal.WarnUsingNonRenderGraph:EmitConsoleWarning () (at ./Packages/com.unity.render-pipelines.universal/Runtime/Data/UniversalRenderPipelineAsset.cs:427)
System.Reflection.RuntimeMethodInfo:Invoke (object,System.Reflection.BindingFlags,System.Reflection.Binder,object[],System.Globalization.CultureInfo)
System.Reflection.MethodBase:Invoke (object,object[])
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadMethodAttributes ()

(Filename: ./Packages/com.unity.render-pipelines.universal/Runtime/Data/UniversalRenderPipelineAsset.cs Line: 427)

Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.043 seconds
Domain Reload Profiling: 1908ms
	BeginReloadAssembly (169ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (5ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (60ms)
	RebuildCommonClasses (33ms)
	RebuildNativeTypeToScriptingClass (8ms)
	initialDomainReloadingComplete (46ms)
	LoadAllAssembliesAndSetupDomain (608ms)
		LoadAssemblies (377ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (302ms)
			TypeCache.Refresh (40ms)
				TypeCache.ScanAssembly (25ms)
			BuildScriptInfoCaches (240ms)
			ResolveRequiredComponents (15ms)
	FinalizeReload (1043ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (834ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (8ms)
			SetLoadedEditorAssemblies (5ms)
			BeforeProcessingInitializeOnLoad (102ms)
			ProcessInitializeOnLoadAttributes (553ms)
			ProcessInitializeOnLoadMethodAttributes (159ms)
			AfterProcessingInitializeOnLoad (6ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (13ms)
Refreshing native plugins compatible for Editor in 1.77 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 29 Unused Serialized files (Serialized files now loaded: 0)
Unloading 8934 unused Assets / (8.6 MB). Loaded Objects now: 10157.
Memory consumption went from 240.5 MB to 231.9 MB.
Total: 32.461200 ms (FindLiveObjects: 0.834500 ms CreateObjectMapping: 0.901300 ms MarkObjects: 26.043600 ms  DeleteObjects: 4.680600 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Duplicate assembly 'System.Runtime.CompilerServices.Unsafe.dll' with different versions detected, using 'Packages/com.bezi.sidekick/Plugins/System.Runtime.CompilerServices.Unsafe.dll, AssemblyName=System.Runtime.CompilerServices.Unsafe, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a' and ignoring 'Assets/Plugins/Protobuf/System.Runtime.CompilerServices.Unsafe.dll, AssemblyName=System.Runtime.CompilerServices.Unsafe, Version=4.0.4.1, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a'.Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.834 seconds
OnLevelWasLoaded was found on DOTweenComponent
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
Refreshing native plugins compatible for Editor in 0.72 ms, found 3 plugins.
OnLevelWasLoaded was found on DOTweenComponent
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
Assertion failed on expression: 'pred(*previous, *i)'
Native extension for Android target not found
Native extension for WindowsStandalone target not found
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Refreshing native plugins compatible for Editor in 0.86 ms, found 3 plugins.
SceneCameraResetButton: Initialized.
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:Log (object)
SceneCameraResetButton:.cctor () (at Assets/Editor/Tools/SceneCameraResetButton.cs:15)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: Assets/Editor/Tools/SceneCameraResetButton.cs Line: 15)

The project currently uses the compatibility mode where the Render Graph API is disabled. Support for this mode will be removed in future Unity versions. Migrate existing ScriptableRenderPasses to the new RenderGraph API. After the migration, disable the compatibility mode in Edit > Projects Settings > Graphics > Render Graph.
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogWarning (object)
UnityEngine.Rendering.Universal.WarnUsingNonRenderGraph:EmitConsoleWarning () (at ./Packages/com.unity.render-pipelines.universal/Runtime/Data/UniversalRenderPipelineAsset.cs:427)
System.Reflection.RuntimeMethodInfo:Invoke (object,System.Reflection.BindingFlags,System.Reflection.Binder,object[],System.Globalization.CultureInfo)
System.Reflection.MethodBase:Invoke (object,object[])
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadMethodAttributes ()

(Filename: ./Packages/com.unity.render-pipelines.universal/Runtime/Data/UniversalRenderPipelineAsset.cs Line: 427)

Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.907 seconds
Domain Reload Profiling: 1746ms
	BeginReloadAssembly (165ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (6ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (58ms)
	RebuildCommonClasses (25ms)
	RebuildNativeTypeToScriptingClass (9ms)
	initialDomainReloadingComplete (40ms)
	LoadAllAssembliesAndSetupDomain (598ms)
		LoadAssemblies (381ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (284ms)
			TypeCache.Refresh (11ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (254ms)
			ResolveRequiredComponents (13ms)
	FinalizeReload (908ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (708ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (7ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (92ms)
			ProcessInitializeOnLoadAttributes (469ms)
			ProcessInitializeOnLoadMethodAttributes (135ms)
			AfterProcessingInitializeOnLoad (3ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (8ms)
Refreshing native plugins compatible for Editor in 1.22 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 29 Unused Serialized files (Serialized files now loaded: 0)
Unloading 8934 unused Assets / (8.1 MB). Loaded Objects now: 10159.
Memory consumption went from 240.5 MB to 232.5 MB.
Total: 23.535900 ms (FindLiveObjects: 1.277000 ms CreateObjectMapping: 0.678300 ms MarkObjects: 17.460800 ms  DeleteObjects: 4.118500 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Duplicate assembly 'System.Runtime.CompilerServices.Unsafe.dll' with different versions detected, using 'Packages/com.bezi.sidekick/Plugins/System.Runtime.CompilerServices.Unsafe.dll, AssemblyName=System.Runtime.CompilerServices.Unsafe, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a' and ignoring 'Assets/Plugins/Protobuf/System.Runtime.CompilerServices.Unsafe.dll, AssemblyName=System.Runtime.CompilerServices.Unsafe, Version=4.0.4.1, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a'.Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.824 seconds
OnLevelWasLoaded was found on DOTweenComponent
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
Refreshing native plugins compatible for Editor in 0.68 ms, found 3 plugins.
OnLevelWasLoaded was found on DOTweenComponent
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
Assertion failed on expression: 'pred(*previous, *i)'
Native extension for Android target not found
Native extension for WindowsStandalone target not found
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Refreshing native plugins compatible for Editor in 0.86 ms, found 3 plugins.
SceneCameraResetButton: Initialized.
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:Log (object)
SceneCameraResetButton:.cctor () (at Assets/Editor/Tools/SceneCameraResetButton.cs:15)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: Assets/Editor/Tools/SceneCameraResetButton.cs Line: 15)

The project currently uses the compatibility mode where the Render Graph API is disabled. Support for this mode will be removed in future Unity versions. Migrate existing ScriptableRenderPasses to the new RenderGraph API. After the migration, disable the compatibility mode in Edit > Projects Settings > Graphics > Render Graph.
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogWarning (object)
UnityEngine.Rendering.Universal.WarnUsingNonRenderGraph:EmitConsoleWarning () (at ./Packages/com.unity.render-pipelines.universal/Runtime/Data/UniversalRenderPipelineAsset.cs:427)
System.Reflection.RuntimeMethodInfo:Invoke (object,System.Reflection.BindingFlags,System.Reflection.Binder,object[],System.Globalization.CultureInfo)
System.Reflection.MethodBase:Invoke (object,object[])
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadMethodAttributes ()

(Filename: ./Packages/com.unity.render-pipelines.universal/Runtime/Data/UniversalRenderPipelineAsset.cs Line: 427)

Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.916 seconds
Domain Reload Profiling: 1745ms
	BeginReloadAssembly (161ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (4ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (55ms)
	RebuildCommonClasses (29ms)
	RebuildNativeTypeToScriptingClass (8ms)
	initialDomainReloadingComplete (37ms)
	LoadAllAssembliesAndSetupDomain (592ms)
		LoadAssemblies (373ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (286ms)
			TypeCache.Refresh (13ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (250ms)
			ResolveRequiredComponents (16ms)
	FinalizeReload (917ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (712ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (9ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (98ms)
			ProcessInitializeOnLoadAttributes (460ms)
			ProcessInitializeOnLoadMethodAttributes (137ms)
			AfterProcessingInitializeOnLoad (3ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (8ms)
Refreshing native plugins compatible for Editor in 1.25 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 29 Unused Serialized files (Serialized files now loaded: 0)
Unloading 8934 unused Assets / (8.5 MB). Loaded Objects now: 10161.
Memory consumption went from 240.4 MB to 231.9 MB.
Total: 28.660200 ms (FindLiveObjects: 0.857200 ms CreateObjectMapping: 0.761900 ms MarkObjects: 22.316900 ms  DeleteObjects: 4.721900 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
OnLevelWasLoaded was found on DOTweenComponent
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
Refreshing native plugins compatible for Editor in 1.56 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 29 Unused Serialized files (Serialized files now loaded: 0)
Unloading 8921 unused Assets / (8.3 MB). Loaded Objects now: 10161.
Memory consumption went from 240.6 MB to 232.2 MB.
Total: 29.837000 ms (FindLiveObjects: 0.975500 ms CreateObjectMapping: 0.724000 ms MarkObjects: 23.684500 ms  DeleteObjects: 4.451100 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Duplicate assembly 'System.Runtime.CompilerServices.Unsafe.dll' with different versions detected, using 'Packages/com.bezi.sidekick/Plugins/System.Runtime.CompilerServices.Unsafe.dll, AssemblyName=System.Runtime.CompilerServices.Unsafe, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a' and ignoring 'Assets/Plugins/Protobuf/System.Runtime.CompilerServices.Unsafe.dll, AssemblyName=System.Runtime.CompilerServices.Unsafe, Version=4.0.4.1, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a'.Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.847 seconds
OnLevelWasLoaded was found on DOTweenComponent
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
Refreshing native plugins compatible for Editor in 0.92 ms, found 3 plugins.
OnLevelWasLoaded was found on DOTweenComponent
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
Assertion failed on expression: 'pred(*previous, *i)'
Native extension for Android target not found
Native extension for WindowsStandalone target not found
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Refreshing native plugins compatible for Editor in 0.84 ms, found 3 plugins.
SceneCameraResetButton: Initialized.
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:Log (object)
SceneCameraResetButton:.cctor () (at Assets/Editor/Tools/SceneCameraResetButton.cs:15)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: Assets/Editor/Tools/SceneCameraResetButton.cs Line: 15)

The project currently uses the compatibility mode where the Render Graph API is disabled. Support for this mode will be removed in future Unity versions. Migrate existing ScriptableRenderPasses to the new RenderGraph API. After the migration, disable the compatibility mode in Edit > Projects Settings > Graphics > Render Graph.
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogWarning (object)
UnityEngine.Rendering.Universal.WarnUsingNonRenderGraph:EmitConsoleWarning () (at ./Packages/com.unity.render-pipelines.universal/Runtime/Data/UniversalRenderPipelineAsset.cs:427)
System.Reflection.RuntimeMethodInfo:Invoke (object,System.Reflection.BindingFlags,System.Reflection.Binder,object[],System.Globalization.CultureInfo)
System.Reflection.MethodBase:Invoke (object,object[])
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadMethodAttributes ()

(Filename: ./Packages/com.unity.render-pipelines.universal/Runtime/Data/UniversalRenderPipelineAsset.cs Line: 427)

Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.931 seconds
Domain Reload Profiling: 1781ms
	BeginReloadAssembly (162ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (4ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (54ms)
	RebuildCommonClasses (30ms)
	RebuildNativeTypeToScriptingClass (12ms)
	initialDomainReloadingComplete (46ms)
	LoadAllAssembliesAndSetupDomain (600ms)
		LoadAssemblies (381ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (289ms)
			TypeCache.Refresh (34ms)
				TypeCache.ScanAssembly (23ms)
			BuildScriptInfoCaches (234ms)
			ResolveRequiredComponents (14ms)
	FinalizeReload (931ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (729ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (7ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (99ms)
			ProcessInitializeOnLoadAttributes (478ms)
			ProcessInitializeOnLoadMethodAttributes (138ms)
			AfterProcessingInitializeOnLoad (3ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (9ms)
Refreshing native plugins compatible for Editor in 1.39 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 29 Unused Serialized files (Serialized files now loaded: 0)
Unloading 8934 unused Assets / (7.8 MB). Loaded Objects now: 10163.
Memory consumption went from 240.6 MB to 232.8 MB.
Total: 27.757400 ms (FindLiveObjects: 0.845600 ms CreateObjectMapping: 0.736300 ms MarkObjects: 22.187000 ms  DeleteObjects: 3.986900 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
OnLevelWasLoaded was found on DOTweenComponent
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
Refreshing native plugins compatible for Editor in 1.36 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 29 Unused Serialized files (Serialized files now loaded: 0)
Unloading 8921 unused Assets / (7.3 MB). Loaded Objects now: 10163.
Memory consumption went from 240.7 MB to 233.4 MB.
Total: 30.531200 ms (FindLiveObjects: 1.043300 ms CreateObjectMapping: 0.804100 ms MarkObjects: 25.359100 ms  DeleteObjects: 3.323400 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Duplicate assembly 'System.Runtime.CompilerServices.Unsafe.dll' with different versions detected, using 'Packages/com.bezi.sidekick/Plugins/System.Runtime.CompilerServices.Unsafe.dll, AssemblyName=System.Runtime.CompilerServices.Unsafe, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a' and ignoring 'Assets/Plugins/Protobuf/System.Runtime.CompilerServices.Unsafe.dll, AssemblyName=System.Runtime.CompilerServices.Unsafe, Version=4.0.4.1, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a'.Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.952 seconds
OnLevelWasLoaded was found on DOTweenComponent
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
Refreshing native plugins compatible for Editor in 0.71 ms, found 3 plugins.
OnLevelWasLoaded was found on DOTweenComponent
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
Assertion failed on expression: 'pred(*previous, *i)'
Native extension for Android target not found
Native extension for WindowsStandalone target not found
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Refreshing native plugins compatible for Editor in 0.76 ms, found 3 plugins.
SceneCameraResetButton: Initialized.
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:Log (object)
SceneCameraResetButton:.cctor () (at Assets/Editor/Tools/SceneCameraResetButton.cs:15)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: Assets/Editor/Tools/SceneCameraResetButton.cs Line: 15)

The project currently uses the compatibility mode where the Render Graph API is disabled. Support for this mode will be removed in future Unity versions. Migrate existing ScriptableRenderPasses to the new RenderGraph API. After the migration, disable the compatibility mode in Edit > Projects Settings > Graphics > Render Graph.
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogWarning (object)
UnityEngine.Rendering.Universal.WarnUsingNonRenderGraph:EmitConsoleWarning () (at ./Packages/com.unity.render-pipelines.universal/Runtime/Data/UniversalRenderPipelineAsset.cs:427)
System.Reflection.RuntimeMethodInfo:Invoke (object,System.Reflection.BindingFlags,System.Reflection.Binder,object[],System.Globalization.CultureInfo)
System.Reflection.MethodBase:Invoke (object,object[])
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadMethodAttributes ()

(Filename: ./Packages/com.unity.render-pipelines.universal/Runtime/Data/UniversalRenderPipelineAsset.cs Line: 427)

Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.019 seconds
Domain Reload Profiling: 1977ms
	BeginReloadAssembly (167ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (4ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (60ms)
	RebuildCommonClasses (27ms)
	RebuildNativeTypeToScriptingClass (11ms)
	initialDomainReloadingComplete (44ms)
	LoadAllAssembliesAndSetupDomain (709ms)
		LoadAssemblies (419ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (357ms)
			TypeCache.Refresh (39ms)
				TypeCache.ScanAssembly (26ms)
			BuildScriptInfoCaches (296ms)
			ResolveRequiredComponents (15ms)
	FinalizeReload (1020ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (801ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (8ms)
			SetLoadedEditorAssemblies (5ms)
			BeforeProcessingInitializeOnLoad (101ms)
			ProcessInitializeOnLoadAttributes (508ms)
			ProcessInitializeOnLoadMethodAttributes (173ms)
			AfterProcessingInitializeOnLoad (6ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (11ms)
Refreshing native plugins compatible for Editor in 1.24 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 29 Unused Serialized files (Serialized files now loaded: 0)
Unloading 8934 unused Assets / (7.7 MB). Loaded Objects now: 10165.
Memory consumption went from 240.5 MB to 232.8 MB.
Total: 29.508200 ms (FindLiveObjects: 0.813000 ms CreateObjectMapping: 0.603400 ms MarkObjects: 24.078600 ms  DeleteObjects: 4.011900 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Duplicate assembly 'System.Runtime.CompilerServices.Unsafe.dll' with different versions detected, using 'Packages/com.bezi.sidekick/Plugins/System.Runtime.CompilerServices.Unsafe.dll, AssemblyName=System.Runtime.CompilerServices.Unsafe, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a' and ignoring 'Assets/Plugins/Protobuf/System.Runtime.CompilerServices.Unsafe.dll, AssemblyName=System.Runtime.CompilerServices.Unsafe, Version=4.0.4.1, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a'.Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.773 seconds
OnLevelWasLoaded was found on DOTweenComponent
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
Refreshing native plugins compatible for Editor in 0.80 ms, found 3 plugins.
OnLevelWasLoaded was found on DOTweenComponent
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
Assertion failed on expression: 'pred(*previous, *i)'
Native extension for Android target not found
Native extension for WindowsStandalone target not found
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Refreshing native plugins compatible for Editor in 0.72 ms, found 3 plugins.
SceneCameraResetButton: Initialized.
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:Log (object)
SceneCameraResetButton:.cctor () (at Assets/Editor/Tools/SceneCameraResetButton.cs:15)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: Assets/Editor/Tools/SceneCameraResetButton.cs Line: 15)

The project currently uses the compatibility mode where the Render Graph API is disabled. Support for this mode will be removed in future Unity versions. Migrate existing ScriptableRenderPasses to the new RenderGraph API. After the migration, disable the compatibility mode in Edit > Projects Settings > Graphics > Render Graph.
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogWarning (object)
UnityEngine.Rendering.Universal.WarnUsingNonRenderGraph:EmitConsoleWarning () (at ./Packages/com.unity.render-pipelines.universal/Runtime/Data/UniversalRenderPipelineAsset.cs:427)
System.Reflection.RuntimeMethodInfo:Invoke (object,System.Reflection.BindingFlags,System.Reflection.Binder,object[],System.Globalization.CultureInfo)
System.Reflection.MethodBase:Invoke (object,object[])
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadMethodAttributes ()

(Filename: ./Packages/com.unity.render-pipelines.universal/Runtime/Data/UniversalRenderPipelineAsset.cs Line: 427)

Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.927 seconds
Domain Reload Profiling: 1704ms
	BeginReloadAssembly (149ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (4ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (53ms)
	RebuildCommonClasses (23ms)
	RebuildNativeTypeToScriptingClass (9ms)
	initialDomainReloadingComplete (37ms)
	LoadAllAssembliesAndSetupDomain (559ms)
		LoadAssemblies (336ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (282ms)
			TypeCache.Refresh (10ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (251ms)
			ResolveRequiredComponents (14ms)
	FinalizeReload (927ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (729ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (7ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (98ms)
			ProcessInitializeOnLoadAttributes (475ms)
			ProcessInitializeOnLoadMethodAttributes (142ms)
			AfterProcessingInitializeOnLoad (3ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (8ms)
Refreshing native plugins compatible for Editor in 1.25 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 29 Unused Serialized files (Serialized files now loaded: 0)
Unloading 8934 unused Assets / (7.4 MB). Loaded Objects now: 10167.
Memory consumption went from 240.6 MB to 233.2 MB.
Total: 22.374200 ms (FindLiveObjects: 1.309600 ms CreateObjectMapping: 0.443800 ms MarkObjects: 17.162600 ms  DeleteObjects: 3.456200 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
OnLevelWasLoaded was found on DOTweenComponent
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
Refreshing native plugins compatible for Editor in 1.36 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 29 Unused Serialized files (Serialized files now loaded: 0)
Unloading 8921 unused Assets / (7.3 MB). Loaded Objects now: 10167.
Memory consumption went from 240.7 MB to 233.4 MB.
Total: 30.776700 ms (FindLiveObjects: 1.035100 ms CreateObjectMapping: 0.673900 ms MarkObjects: 25.172500 ms  DeleteObjects: 3.894000 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Duplicate assembly 'System.Runtime.CompilerServices.Unsafe.dll' with different versions detected, using 'Packages/com.bezi.sidekick/Plugins/System.Runtime.CompilerServices.Unsafe.dll, AssemblyName=System.Runtime.CompilerServices.Unsafe, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a' and ignoring 'Assets/Plugins/Protobuf/System.Runtime.CompilerServices.Unsafe.dll, AssemblyName=System.Runtime.CompilerServices.Unsafe, Version=4.0.4.1, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a'.Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.840 seconds
OnLevelWasLoaded was found on DOTweenComponent
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
Refreshing native plugins compatible for Editor in 0.68 ms, found 3 plugins.
OnLevelWasLoaded was found on DOTweenComponent
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
Assertion failed on expression: 'pred(*previous, *i)'
Native extension for Android target not found
Native extension for WindowsStandalone target not found
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Refreshing native plugins compatible for Editor in 0.78 ms, found 3 plugins.
SceneCameraResetButton: Initialized.
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:Log (object)
SceneCameraResetButton:.cctor () (at Assets/Editor/Tools/SceneCameraResetButton.cs:15)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: Assets/Editor/Tools/SceneCameraResetButton.cs Line: 15)

The project currently uses the compatibility mode where the Render Graph API is disabled. Support for this mode will be removed in future Unity versions. Migrate existing ScriptableRenderPasses to the new RenderGraph API. After the migration, disable the compatibility mode in Edit > Projects Settings > Graphics > Render Graph.
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogWarning (object)
UnityEngine.Rendering.Universal.WarnUsingNonRenderGraph:EmitConsoleWarning () (at ./Packages/com.unity.render-pipelines.universal/Runtime/Data/UniversalRenderPipelineAsset.cs:427)
System.Reflection.RuntimeMethodInfo:Invoke (object,System.Reflection.BindingFlags,System.Reflection.Binder,object[],System.Globalization.CultureInfo)
System.Reflection.MethodBase:Invoke (object,object[])
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadMethodAttributes ()

(Filename: ./Packages/com.unity.render-pipelines.universal/Runtime/Data/UniversalRenderPipelineAsset.cs Line: 427)

Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.915 seconds
Domain Reload Profiling: 1760ms
	BeginReloadAssembly (158ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (4ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (52ms)
	RebuildCommonClasses (25ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (40ms)
	LoadAllAssembliesAndSetupDomain (611ms)
		LoadAssemblies (367ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (313ms)
			TypeCache.Refresh (36ms)
				TypeCache.ScanAssembly (24ms)
			BuildScriptInfoCaches (256ms)
			ResolveRequiredComponents (15ms)
	FinalizeReload (916ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (709ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (7ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (95ms)
			ProcessInitializeOnLoadAttributes (472ms)
			ProcessInitializeOnLoadMethodAttributes (127ms)
			AfterProcessingInitializeOnLoad (3ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (8ms)
Refreshing native plugins compatible for Editor in 1.18 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 29 Unused Serialized files (Serialized files now loaded: 0)
Unloading 8934 unused Assets / (8.5 MB). Loaded Objects now: 10169.
Memory consumption went from 242.5 MB to 234.0 MB.
Total: 29.850200 ms (FindLiveObjects: 0.899300 ms CreateObjectMapping: 0.964900 ms MarkObjects: 23.498100 ms  DeleteObjects: 4.486800 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Duplicate assembly 'System.Runtime.CompilerServices.Unsafe.dll' with different versions detected, using 'Packages/com.bezi.sidekick/Plugins/System.Runtime.CompilerServices.Unsafe.dll, AssemblyName=System.Runtime.CompilerServices.Unsafe, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a' and ignoring 'Assets/Plugins/Protobuf/System.Runtime.CompilerServices.Unsafe.dll, AssemblyName=System.Runtime.CompilerServices.Unsafe, Version=4.0.4.1, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a'.Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.842 seconds
OnLevelWasLoaded was found on DOTweenComponent
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
Refreshing native plugins compatible for Editor in 0.61 ms, found 3 plugins.
OnLevelWasLoaded was found on DOTweenComponent
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
Assertion failed on expression: 'pred(*previous, *i)'
Native extension for Android target not found
Native extension for WindowsStandalone target not found
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Refreshing native plugins compatible for Editor in 0.80 ms, found 3 plugins.
SceneCameraResetButton: Initialized.
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:Log (object)
SceneCameraResetButton:.cctor () (at Assets/Editor/Tools/SceneCameraResetButton.cs:15)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: Assets/Editor/Tools/SceneCameraResetButton.cs Line: 15)

The project currently uses the compatibility mode where the Render Graph API is disabled. Support for this mode will be removed in future Unity versions. Migrate existing ScriptableRenderPasses to the new RenderGraph API. After the migration, disable the compatibility mode in Edit > Projects Settings > Graphics > Render Graph.
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogWarning (object)
UnityEngine.Rendering.Universal.WarnUsingNonRenderGraph:EmitConsoleWarning () (at ./Packages/com.unity.render-pipelines.universal/Runtime/Data/UniversalRenderPipelineAsset.cs:427)
System.Reflection.RuntimeMethodInfo:Invoke (object,System.Reflection.BindingFlags,System.Reflection.Binder,object[],System.Globalization.CultureInfo)
System.Reflection.MethodBase:Invoke (object,object[])
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadMethodAttributes ()

(Filename: ./Packages/com.unity.render-pipelines.universal/Runtime/Data/UniversalRenderPipelineAsset.cs Line: 427)

Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.902 seconds
Domain Reload Profiling: 1749ms
	BeginReloadAssembly (157ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (4ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (58ms)
	RebuildCommonClasses (26ms)
	RebuildNativeTypeToScriptingClass (9ms)
	initialDomainReloadingComplete (39ms)
	LoadAllAssembliesAndSetupDomain (615ms)
		LoadAssemblies (370ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (309ms)
			TypeCache.Refresh (12ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (274ms)
			ResolveRequiredComponents (15ms)
	FinalizeReload (902ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (707ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (7ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (99ms)
			ProcessInitializeOnLoadAttributes (462ms)
			ProcessInitializeOnLoadMethodAttributes (132ms)
			AfterProcessingInitializeOnLoad (3ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (8ms)
Refreshing native plugins compatible for Editor in 1.20 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 29 Unused Serialized files (Serialized files now loaded: 0)
Unloading 8934 unused Assets / (7.6 MB). Loaded Objects now: 10171.
Memory consumption went from 242.4 MB to 234.8 MB.
Total: 27.324500 ms (FindLiveObjects: 0.897700 ms CreateObjectMapping: 0.875700 ms MarkObjects: 22.266600 ms  DeleteObjects: 3.283100 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Duplicate assembly 'System.Runtime.CompilerServices.Unsafe.dll' with different versions detected, using 'Packages/com.bezi.sidekick/Plugins/System.Runtime.CompilerServices.Unsafe.dll, AssemblyName=System.Runtime.CompilerServices.Unsafe, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a' and ignoring 'Assets/Plugins/Protobuf/System.Runtime.CompilerServices.Unsafe.dll, AssemblyName=System.Runtime.CompilerServices.Unsafe, Version=4.0.4.1, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a'.Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.813 seconds
OnLevelWasLoaded was found on DOTweenComponent
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
Refreshing native plugins compatible for Editor in 0.58 ms, found 3 plugins.
OnLevelWasLoaded was found on DOTweenComponent
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
Assertion failed on expression: 'pred(*previous, *i)'
Native extension for Android target not found
Native extension for WindowsStandalone target not found
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Refreshing native plugins compatible for Editor in 1.00 ms, found 3 plugins.
SceneCameraResetButton: Initialized.
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:Log (object)
SceneCameraResetButton:.cctor () (at Assets/Editor/Tools/SceneCameraResetButton.cs:15)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: Assets/Editor/Tools/SceneCameraResetButton.cs Line: 15)

The project currently uses the compatibility mode where the Render Graph API is disabled. Support for this mode will be removed in future Unity versions. Migrate existing ScriptableRenderPasses to the new RenderGraph API. After the migration, disable the compatibility mode in Edit > Projects Settings > Graphics > Render Graph.
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogWarning (object)
UnityEngine.Rendering.Universal.WarnUsingNonRenderGraph:EmitConsoleWarning () (at ./Packages/com.unity.render-pipelines.universal/Runtime/Data/UniversalRenderPipelineAsset.cs:427)
System.Reflection.RuntimeMethodInfo:Invoke (object,System.Reflection.BindingFlags,System.Reflection.Binder,object[],System.Globalization.CultureInfo)
System.Reflection.MethodBase:Invoke (object,object[])
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadMethodAttributes ()

(Filename: ./Packages/com.unity.render-pipelines.universal/Runtime/Data/UniversalRenderPipelineAsset.cs Line: 427)

Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.903 seconds
Domain Reload Profiling: 1721ms
	BeginReloadAssembly (162ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (4ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (59ms)
	RebuildCommonClasses (32ms)
	RebuildNativeTypeToScriptingClass (8ms)
	initialDomainReloadingComplete (35ms)
	LoadAllAssembliesAndSetupDomain (581ms)
		LoadAssemblies (359ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (285ms)
			TypeCache.Refresh (10ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (250ms)
			ResolveRequiredComponents (18ms)
	FinalizeReload (903ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (706ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (7ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (93ms)
			ProcessInitializeOnLoadAttributes (470ms)
			ProcessInitializeOnLoadMethodAttributes (129ms)
			AfterProcessingInitializeOnLoad (3ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (8ms)
Refreshing native plugins compatible for Editor in 1.19 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 29 Unused Serialized files (Serialized files now loaded: 0)
Unloading 8934 unused Assets / (8.0 MB). Loaded Objects now: 10173.
Memory consumption went from 242.5 MB to 234.5 MB.
Total: 25.991400 ms (FindLiveObjects: 0.800800 ms CreateObjectMapping: 0.898500 ms MarkObjects: 20.931100 ms  DeleteObjects: 3.358900 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
OnLevelWasLoaded was found on DOTweenComponent
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
Refreshing native plugins compatible for Editor in 1.46 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 29 Unused Serialized files (Serialized files now loaded: 0)
Unloading 8921 unused Assets / (8.9 MB). Loaded Objects now: 10173.
Memory consumption went from 242.7 MB to 233.8 MB.
Total: 33.902400 ms (FindLiveObjects: 0.964900 ms CreateObjectMapping: 0.689800 ms MarkObjects: 27.092600 ms  DeleteObjects: 5.152700 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Duplicate assembly 'System.Runtime.CompilerServices.Unsafe.dll' with different versions detected, using 'Packages/com.bezi.sidekick/Plugins/System.Runtime.CompilerServices.Unsafe.dll, AssemblyName=System.Runtime.CompilerServices.Unsafe, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a' and ignoring 'Assets/Plugins/Protobuf/System.Runtime.CompilerServices.Unsafe.dll, AssemblyName=System.Runtime.CompilerServices.Unsafe, Version=4.0.4.1, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a'.Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.941 seconds
OnLevelWasLoaded was found on DOTweenComponent
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
Refreshing native plugins compatible for Editor in 0.99 ms, found 3 plugins.
OnLevelWasLoaded was found on DOTweenComponent
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
Assertion failed on expression: 'pred(*previous, *i)'
Native extension for Android target not found
Native extension for WindowsStandalone target not found
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Refreshing native plugins compatible for Editor in 0.81 ms, found 3 plugins.
SceneCameraResetButton: Initialized.
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:Log (object)
SceneCameraResetButton:.cctor () (at Assets/Editor/Tools/SceneCameraResetButton.cs:15)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: Assets/Editor/Tools/SceneCameraResetButton.cs Line: 15)

The project currently uses the compatibility mode where the Render Graph API is disabled. Support for this mode will be removed in future Unity versions. Migrate existing ScriptableRenderPasses to the new RenderGraph API. After the migration, disable the compatibility mode in Edit > Projects Settings > Graphics > Render Graph.
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogWarning (object)
UnityEngine.Rendering.Universal.WarnUsingNonRenderGraph:EmitConsoleWarning () (at ./Packages/com.unity.render-pipelines.universal/Runtime/Data/UniversalRenderPipelineAsset.cs:427)
System.Reflection.RuntimeMethodInfo:Invoke (object,System.Reflection.BindingFlags,System.Reflection.Binder,object[],System.Globalization.CultureInfo)
System.Reflection.MethodBase:Invoke (object,object[])
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadMethodAttributes ()

(Filename: ./Packages/com.unity.render-pipelines.universal/Runtime/Data/UniversalRenderPipelineAsset.cs Line: 427)

Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.888 seconds
Domain Reload Profiling: 1833ms
	BeginReloadAssembly (177ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (6ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (54ms)
	RebuildCommonClasses (28ms)
	RebuildNativeTypeToScriptingClass (9ms)
	initialDomainReloadingComplete (45ms)
	LoadAllAssembliesAndSetupDomain (686ms)
		LoadAssemblies (441ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (324ms)
			TypeCache.Refresh (39ms)
				TypeCache.ScanAssembly (24ms)
			BuildScriptInfoCaches (265ms)
			ResolveRequiredComponents (14ms)
	FinalizeReload (889ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (692ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (8ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (92ms)
			ProcessInitializeOnLoadAttributes (453ms)
			ProcessInitializeOnLoadMethodAttributes (130ms)
			AfterProcessingInitializeOnLoad (3ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (7ms)
Refreshing native plugins compatible for Editor in 1.58 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 29 Unused Serialized files (Serialized files now loaded: 0)
Unloading 8934 unused Assets / (7.2 MB). Loaded Objects now: 10175.
Memory consumption went from 242.5 MB to 235.3 MB.
Total: 27.498900 ms (FindLiveObjects: 0.894000 ms CreateObjectMapping: 0.803000 ms MarkObjects: 22.264400 ms  DeleteObjects: 3.536100 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Duplicate assembly 'System.Runtime.CompilerServices.Unsafe.dll' with different versions detected, using 'Packages/com.bezi.sidekick/Plugins/System.Runtime.CompilerServices.Unsafe.dll, AssemblyName=System.Runtime.CompilerServices.Unsafe, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a' and ignoring 'Assets/Plugins/Protobuf/System.Runtime.CompilerServices.Unsafe.dll, AssemblyName=System.Runtime.CompilerServices.Unsafe, Version=4.0.4.1, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a'.Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.773 seconds
OnLevelWasLoaded was found on DOTweenComponent
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
Refreshing native plugins compatible for Editor in 0.63 ms, found 3 plugins.
OnLevelWasLoaded was found on DOTweenComponent
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
Assertion failed on expression: 'pred(*previous, *i)'
Native extension for Android target not found
Native extension for WindowsStandalone target not found
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Refreshing native plugins compatible for Editor in 0.79 ms, found 3 plugins.
SceneCameraResetButton: Initialized.
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:Log (object)
SceneCameraResetButton:.cctor () (at Assets/Editor/Tools/SceneCameraResetButton.cs:15)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: Assets/Editor/Tools/SceneCameraResetButton.cs Line: 15)

The project currently uses the compatibility mode where the Render Graph API is disabled. Support for this mode will be removed in future Unity versions. Migrate existing ScriptableRenderPasses to the new RenderGraph API. After the migration, disable the compatibility mode in Edit > Projects Settings > Graphics > Render Graph.
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogWarning (object)
UnityEngine.Rendering.Universal.WarnUsingNonRenderGraph:EmitConsoleWarning () (at ./Packages/com.unity.render-pipelines.universal/Runtime/Data/UniversalRenderPipelineAsset.cs:427)
System.Reflection.RuntimeMethodInfo:Invoke (object,System.Reflection.BindingFlags,System.Reflection.Binder,object[],System.Globalization.CultureInfo)
System.Reflection.MethodBase:Invoke (object,object[])
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadMethodAttributes ()

(Filename: ./Packages/com.unity.render-pipelines.universal/Runtime/Data/UniversalRenderPipelineAsset.cs Line: 427)

Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.905 seconds
Domain Reload Profiling: 1682ms
	BeginReloadAssembly (145ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (4ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (50ms)
	RebuildCommonClasses (22ms)
	RebuildNativeTypeToScriptingClass (8ms)
	initialDomainReloadingComplete (36ms)
	LoadAllAssembliesAndSetupDomain (565ms)
		LoadAssemblies (342ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (282ms)
			TypeCache.Refresh (10ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (252ms)
			ResolveRequiredComponents (14ms)
	FinalizeReload (905ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (708ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (7ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (99ms)
			ProcessInitializeOnLoadAttributes (470ms)
			ProcessInitializeOnLoadMethodAttributes (125ms)
			AfterProcessingInitializeOnLoad (3ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (8ms)
Refreshing native plugins compatible for Editor in 1.12 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 29 Unused Serialized files (Serialized files now loaded: 0)
Unloading 8934 unused Assets / (7.5 MB). Loaded Objects now: 10177.
Memory consumption went from 242.5 MB to 235.0 MB.
Total: 26.622200 ms (FindLiveObjects: 1.277200 ms CreateObjectMapping: 0.584900 ms MarkObjects: 20.511600 ms  DeleteObjects: 4.246900 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Duplicate assembly 'System.Runtime.CompilerServices.Unsafe.dll' with different versions detected, using 'Packages/com.bezi.sidekick/Plugins/System.Runtime.CompilerServices.Unsafe.dll, AssemblyName=System.Runtime.CompilerServices.Unsafe, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a' and ignoring 'Assets/Plugins/Protobuf/System.Runtime.CompilerServices.Unsafe.dll, AssemblyName=System.Runtime.CompilerServices.Unsafe, Version=4.0.4.1, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a'.Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.788 seconds
OnLevelWasLoaded was found on DOTweenComponent
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
Refreshing native plugins compatible for Editor in 0.74 ms, found 3 plugins.
OnLevelWasLoaded was found on DOTweenComponent
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
Assertion failed on expression: 'pred(*previous, *i)'
Native extension for Android target not found
Native extension for WindowsStandalone target not found
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Refreshing native plugins compatible for Editor in 1.19 ms, found 3 plugins.
SceneCameraResetButton: Initialized.
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:Log (object)
SceneCameraResetButton:.cctor () (at Assets/Editor/Tools/SceneCameraResetButton.cs:15)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: Assets/Editor/Tools/SceneCameraResetButton.cs Line: 15)

The project currently uses the compatibility mode where the Render Graph API is disabled. Support for this mode will be removed in future Unity versions. Migrate existing ScriptableRenderPasses to the new RenderGraph API. After the migration, disable the compatibility mode in Edit > Projects Settings > Graphics > Render Graph.
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogWarning (object)
UnityEngine.Rendering.Universal.WarnUsingNonRenderGraph:EmitConsoleWarning () (at ./Packages/com.unity.render-pipelines.universal/Runtime/Data/UniversalRenderPipelineAsset.cs:427)
System.Reflection.RuntimeMethodInfo:Invoke (object,System.Reflection.BindingFlags,System.Reflection.Binder,object[],System.Globalization.CultureInfo)
System.Reflection.MethodBase:Invoke (object,object[])
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadMethodAttributes ()

(Filename: ./Packages/com.unity.render-pipelines.universal/Runtime/Data/UniversalRenderPipelineAsset.cs Line: 427)

Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.886 seconds
Domain Reload Profiling: 1678ms
	BeginReloadAssembly (151ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (9ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (49ms)
	RebuildCommonClasses (24ms)
	RebuildNativeTypeToScriptingClass (8ms)
	initialDomainReloadingComplete (36ms)
	LoadAllAssembliesAndSetupDomain (573ms)
		LoadAssemblies (358ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (276ms)
			TypeCache.Refresh (10ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (246ms)
			ResolveRequiredComponents (14ms)
	FinalizeReload (886ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (687ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (7ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (94ms)
			ProcessInitializeOnLoadAttributes (453ms)
			ProcessInitializeOnLoadMethodAttributes (126ms)
			AfterProcessingInitializeOnLoad (3ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (8ms)
Refreshing native plugins compatible for Editor in 1.14 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 29 Unused Serialized files (Serialized files now loaded: 0)
Unloading 8934 unused Assets / (7.2 MB). Loaded Objects now: 10179.
Memory consumption went from 242.6 MB to 235.4 MB.
Total: 27.434100 ms (FindLiveObjects: 0.755300 ms CreateObjectMapping: 0.693900 ms MarkObjects: 22.504800 ms  DeleteObjects: 3.478900 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
OnLevelWasLoaded was found on DOTweenComponent
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
Refreshing native plugins compatible for Editor in 1.39 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 29 Unused Serialized files (Serialized files now loaded: 0)
Unloading 8921 unused Assets / (8.1 MB). Loaded Objects now: 10179.
Memory consumption went from 242.7 MB to 234.5 MB.
Total: 31.057200 ms (FindLiveObjects: 0.923500 ms CreateObjectMapping: 0.680400 ms MarkObjects: 25.410400 ms  DeleteObjects: 4.041700 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Duplicate assembly 'System.Runtime.CompilerServices.Unsafe.dll' with different versions detected, using 'Packages/com.bezi.sidekick/Plugins/System.Runtime.CompilerServices.Unsafe.dll, AssemblyName=System.Runtime.CompilerServices.Unsafe, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a' and ignoring 'Assets/Plugins/Protobuf/System.Runtime.CompilerServices.Unsafe.dll, AssemblyName=System.Runtime.CompilerServices.Unsafe, Version=4.0.4.1, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a'.Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.784 seconds
OnLevelWasLoaded was found on DOTweenComponent
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
Refreshing native plugins compatible for Editor in 0.75 ms, found 3 plugins.
OnLevelWasLoaded was found on DOTweenComponent
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
Assertion failed on expression: 'pred(*previous, *i)'
Native extension for Android target not found
Native extension for WindowsStandalone target not found
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Refreshing native plugins compatible for Editor in 0.83 ms, found 3 plugins.
SceneCameraResetButton: Initialized.
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:Log (object)
SceneCameraResetButton:.cctor () (at Assets/Editor/Tools/SceneCameraResetButton.cs:15)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: Assets/Editor/Tools/SceneCameraResetButton.cs Line: 15)

The project currently uses the compatibility mode where the Render Graph API is disabled. Support for this mode will be removed in future Unity versions. Migrate existing ScriptableRenderPasses to the new RenderGraph API. After the migration, disable the compatibility mode in Edit > Projects Settings > Graphics > Render Graph.
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogWarning (object)
UnityEngine.Rendering.Universal.WarnUsingNonRenderGraph:EmitConsoleWarning () (at ./Packages/com.unity.render-pipelines.universal/Runtime/Data/UniversalRenderPipelineAsset.cs:427)
System.Reflection.RuntimeMethodInfo:Invoke (object,System.Reflection.BindingFlags,System.Reflection.Binder,object[],System.Globalization.CultureInfo)
System.Reflection.MethodBase:Invoke (object,object[])
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadMethodAttributes ()

(Filename: ./Packages/com.unity.render-pipelines.universal/Runtime/Data/UniversalRenderPipelineAsset.cs Line: 427)

Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.881 seconds
Domain Reload Profiling: 1669ms
	BeginReloadAssembly (146ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (4ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (51ms)
	RebuildCommonClasses (23ms)
	RebuildNativeTypeToScriptingClass (9ms)
	initialDomainReloadingComplete (36ms)
	LoadAllAssembliesAndSetupDomain (575ms)
		LoadAssemblies (348ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (286ms)
			TypeCache.Refresh (35ms)
				TypeCache.ScanAssembly (23ms)
			BuildScriptInfoCaches (230ms)
			ResolveRequiredComponents (14ms)
	FinalizeReload (881ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (690ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (7ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (97ms)
			ProcessInitializeOnLoadAttributes (448ms)
			ProcessInitializeOnLoadMethodAttributes (131ms)
			AfterProcessingInitializeOnLoad (3ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (8ms)
Refreshing native plugins compatible for Editor in 1.30 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 29 Unused Serialized files (Serialized files now loaded: 0)
Unloading 8934 unused Assets / (7.2 MB). Loaded Objects now: 10181.
Memory consumption went from 242.4 MB to 235.2 MB.
Total: 28.409300 ms (FindLiveObjects: 0.830300 ms CreateObjectMapping: 0.776800 ms MarkObjects: 23.310300 ms  DeleteObjects: 3.490600 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Duplicate assembly 'System.Runtime.CompilerServices.Unsafe.dll' with different versions detected, using 'Packages/com.bezi.sidekick/Plugins/System.Runtime.CompilerServices.Unsafe.dll, AssemblyName=System.Runtime.CompilerServices.Unsafe, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a' and ignoring 'Assets/Plugins/Protobuf/System.Runtime.CompilerServices.Unsafe.dll, AssemblyName=System.Runtime.CompilerServices.Unsafe, Version=4.0.4.1, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a'.Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.763 seconds
OnLevelWasLoaded was found on DOTweenComponent
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
Refreshing native plugins compatible for Editor in 0.75 ms, found 3 plugins.
OnLevelWasLoaded was found on DOTweenComponent
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
Assertion failed on expression: 'pred(*previous, *i)'
Native extension for Android target not found
Native extension for WindowsStandalone target not found
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Refreshing native plugins compatible for Editor in 0.73 ms, found 3 plugins.
SceneCameraResetButton: Initialized.
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:Log (object)
SceneCameraResetButton:.cctor () (at Assets/Editor/Tools/SceneCameraResetButton.cs:15)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: Assets/Editor/Tools/SceneCameraResetButton.cs Line: 15)

The project currently uses the compatibility mode where the Render Graph API is disabled. Support for this mode will be removed in future Unity versions. Migrate existing ScriptableRenderPasses to the new RenderGraph API. After the migration, disable the compatibility mode in Edit > Projects Settings > Graphics > Render Graph.
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogWarning (object)
UnityEngine.Rendering.Universal.WarnUsingNonRenderGraph:EmitConsoleWarning () (at ./Packages/com.unity.render-pipelines.universal/Runtime/Data/UniversalRenderPipelineAsset.cs:427)
System.Reflection.RuntimeMethodInfo:Invoke (object,System.Reflection.BindingFlags,System.Reflection.Binder,object[],System.Globalization.CultureInfo)
System.Reflection.MethodBase:Invoke (object,object[])
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadMethodAttributes ()

(Filename: ./Packages/com.unity.render-pipelines.universal/Runtime/Data/UniversalRenderPipelineAsset.cs Line: 427)

Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.881 seconds
Domain Reload Profiling: 1648ms
	BeginReloadAssembly (149ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (4ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (52ms)
	RebuildCommonClasses (22ms)
	RebuildNativeTypeToScriptingClass (8ms)
	initialDomainReloadingComplete (35ms)
	LoadAllAssembliesAndSetupDomain (552ms)
		LoadAssemblies (339ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (274ms)
			TypeCache.Refresh (11ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (243ms)
			ResolveRequiredComponents (14ms)
	FinalizeReload (882ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (688ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (8ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (92ms)
			ProcessInitializeOnLoadAttributes (454ms)
			ProcessInitializeOnLoadMethodAttributes (128ms)
			AfterProcessingInitializeOnLoad (3ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (7ms)
Refreshing native plugins compatible for Editor in 1.51 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 29 Unused Serialized files (Serialized files now loaded: 0)
Unloading 8934 unused Assets / (8.2 MB). Loaded Objects now: 10183.
Memory consumption went from 242.6 MB to 234.4 MB.
Total: 29.810900 ms (FindLiveObjects: 0.898700 ms CreateObjectMapping: 0.783200 ms MarkObjects: 24.158800 ms  DeleteObjects: 3.968700 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
OnLevelWasLoaded was found on DOTweenComponent
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
Refreshing native plugins compatible for Editor in 1.59 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 29 Unused Serialized files (Serialized files now loaded: 0)
Unloading 8921 unused Assets / (7.8 MB). Loaded Objects now: 10183.
Memory consumption went from 242.7 MB to 234.9 MB.
Total: 31.712200 ms (FindLiveObjects: 0.900100 ms CreateObjectMapping: 0.640400 ms MarkObjects: 26.320100 ms  DeleteObjects: 3.849700 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Duplicate assembly 'System.Runtime.CompilerServices.Unsafe.dll' with different versions detected, using 'Packages/com.bezi.sidekick/Plugins/System.Runtime.CompilerServices.Unsafe.dll, AssemblyName=System.Runtime.CompilerServices.Unsafe, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a' and ignoring 'Assets/Plugins/Protobuf/System.Runtime.CompilerServices.Unsafe.dll, AssemblyName=System.Runtime.CompilerServices.Unsafe, Version=4.0.4.1, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a'.Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.879 seconds
OnLevelWasLoaded was found on DOTweenComponent
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
Refreshing native plugins compatible for Editor in 1.40 ms, found 3 plugins.
OnLevelWasLoaded was found on DOTweenComponent
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
Assertion failed on expression: 'pred(*previous, *i)'
Native extension for Android target not found
Native extension for WindowsStandalone target not found
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Refreshing native plugins compatible for Editor in 0.75 ms, found 3 plugins.
SceneCameraResetButton: Initialized.
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:Log (object)
SceneCameraResetButton:.cctor () (at Assets/Editor/Tools/SceneCameraResetButton.cs:15)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: Assets/Editor/Tools/SceneCameraResetButton.cs Line: 15)

The project currently uses the compatibility mode where the Render Graph API is disabled. Support for this mode will be removed in future Unity versions. Migrate existing ScriptableRenderPasses to the new RenderGraph API. After the migration, disable the compatibility mode in Edit > Projects Settings > Graphics > Render Graph.
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogWarning (object)
UnityEngine.Rendering.Universal.WarnUsingNonRenderGraph:EmitConsoleWarning () (at ./Packages/com.unity.render-pipelines.universal/Runtime/Data/UniversalRenderPipelineAsset.cs:427)
System.Reflection.RuntimeMethodInfo:Invoke (object,System.Reflection.BindingFlags,System.Reflection.Binder,object[],System.Globalization.CultureInfo)
System.Reflection.MethodBase:Invoke (object,object[])
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadMethodAttributes ()

(Filename: ./Packages/com.unity.render-pipelines.universal/Runtime/Data/UniversalRenderPipelineAsset.cs Line: 427)

Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.967 seconds
Domain Reload Profiling: 1850ms
	BeginReloadAssembly (157ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (5ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (53ms)
	RebuildCommonClasses (27ms)
	RebuildNativeTypeToScriptingClass (9ms)
	initialDomainReloadingComplete (45ms)
	LoadAllAssembliesAndSetupDomain (644ms)
		LoadAssemblies (381ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (330ms)
			TypeCache.Refresh (39ms)
				TypeCache.ScanAssembly (26ms)
			BuildScriptInfoCaches (267ms)
			ResolveRequiredComponents (16ms)
	FinalizeReload (967ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (739ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (8ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (105ms)
			ProcessInitializeOnLoadAttributes (489ms)
			ProcessInitializeOnLoadMethodAttributes (131ms)
			AfterProcessingInitializeOnLoad (3ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (8ms)
Refreshing native plugins compatible for Editor in 1.34 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 29 Unused Serialized files (Serialized files now loaded: 0)
Unloading 8934 unused Assets / (8.6 MB). Loaded Objects now: 10185.
Memory consumption went from 242.6 MB to 234.0 MB.
Total: 28.326400 ms (FindLiveObjects: 0.880300 ms CreateObjectMapping: 0.642600 ms MarkObjects: 21.489700 ms  DeleteObjects: 5.311900 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
OnLevelWasLoaded was found on DOTweenComponent
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
Refreshing native plugins compatible for Editor in 1.31 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 29 Unused Serialized files (Serialized files now loaded: 0)
Unloading 8921 unused Assets / (6.7 MB). Loaded Objects now: 10185.
Memory consumption went from 242.7 MB to 236.0 MB.
Total: 26.895100 ms (FindLiveObjects: 0.850400 ms CreateObjectMapping: 0.651000 ms MarkObjects: 22.141900 ms  DeleteObjects: 3.250000 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Duplicate assembly 'System.Runtime.CompilerServices.Unsafe.dll' with different versions detected, using 'Packages/com.bezi.sidekick/Plugins/System.Runtime.CompilerServices.Unsafe.dll, AssemblyName=System.Runtime.CompilerServices.Unsafe, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a' and ignoring 'Assets/Plugins/Protobuf/System.Runtime.CompilerServices.Unsafe.dll, AssemblyName=System.Runtime.CompilerServices.Unsafe, Version=4.0.4.1, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a'.Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.909 seconds
OnLevelWasLoaded was found on DOTweenComponent
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
Refreshing native plugins compatible for Editor in 0.61 ms, found 3 plugins.
OnLevelWasLoaded was found on DOTweenComponent
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
Assertion failed on expression: 'pred(*previous, *i)'
Native extension for Android target not found
Native extension for WindowsStandalone target not found
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Refreshing native plugins compatible for Editor in 0.83 ms, found 3 plugins.
SceneCameraResetButton: Initialized.
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:Log (object)
SceneCameraResetButton:.cctor () (at Assets/Editor/Tools/SceneCameraResetButton.cs:15)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: Assets/Editor/Tools/SceneCameraResetButton.cs Line: 15)

The project currently uses the compatibility mode where the Render Graph API is disabled. Support for this mode will be removed in future Unity versions. Migrate existing ScriptableRenderPasses to the new RenderGraph API. After the migration, disable the compatibility mode in Edit > Projects Settings > Graphics > Render Graph.
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogWarning (object)
UnityEngine.Rendering.Universal.WarnUsingNonRenderGraph:EmitConsoleWarning () (at ./Packages/com.unity.render-pipelines.universal/Runtime/Data/UniversalRenderPipelineAsset.cs:427)
System.Reflection.RuntimeMethodInfo:Invoke (object,System.Reflection.BindingFlags,System.Reflection.Binder,object[],System.Globalization.CultureInfo)
System.Reflection.MethodBase:Invoke (object,object[])
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadMethodAttributes ()

(Filename: ./Packages/com.unity.render-pipelines.universal/Runtime/Data/UniversalRenderPipelineAsset.cs Line: 427)

Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.937 seconds
Domain Reload Profiling: 1849ms
	BeginReloadAssembly (220ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (8ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (75ms)
	RebuildCommonClasses (31ms)
	RebuildNativeTypeToScriptingClass (11ms)
	initialDomainReloadingComplete (53ms)
	LoadAllAssembliesAndSetupDomain (597ms)
		LoadAssemblies (398ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (284ms)
			TypeCache.Refresh (34ms)
				TypeCache.ScanAssembly (22ms)
			BuildScriptInfoCaches (231ms)
			ResolveRequiredComponents (13ms)
	FinalizeReload (937ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (740ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (7ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (97ms)
			ProcessInitializeOnLoadAttributes (461ms)
			ProcessInitializeOnLoadMethodAttributes (167ms)
			AfterProcessingInitializeOnLoad (3ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (9ms)
Refreshing native plugins compatible for Editor in 1.39 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 29 Unused Serialized files (Serialized files now loaded: 0)
Unloading 8934 unused Assets / (8.2 MB). Loaded Objects now: 10187.
Memory consumption went from 242.6 MB to 234.4 MB.
Total: 31.932700 ms (FindLiveObjects: 0.923700 ms CreateObjectMapping: 0.751800 ms MarkObjects: 25.290600 ms  DeleteObjects: 4.965100 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
OnLevelWasLoaded was found on DOTweenComponent
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
Refreshing native plugins compatible for Editor in 1.33 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 29 Unused Serialized files (Serialized files now loaded: 0)
Unloading 8921 unused Assets / (6.4 MB). Loaded Objects now: 10187.
Memory consumption went from 242.7 MB to 236.3 MB.
Total: 27.082200 ms (FindLiveObjects: 0.983200 ms CreateObjectMapping: 0.961300 ms MarkObjects: 21.798100 ms  DeleteObjects: 3.337800 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Duplicate assembly 'System.Runtime.CompilerServices.Unsafe.dll' with different versions detected, using 'Packages/com.bezi.sidekick/Plugins/System.Runtime.CompilerServices.Unsafe.dll, AssemblyName=System.Runtime.CompilerServices.Unsafe, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a' and ignoring 'Assets/Plugins/Protobuf/System.Runtime.CompilerServices.Unsafe.dll, AssemblyName=System.Runtime.CompilerServices.Unsafe, Version=4.0.4.1, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a'.Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.765 seconds
OnLevelWasLoaded was found on DOTweenComponent
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
Refreshing native plugins compatible for Editor in 0.75 ms, found 3 plugins.
OnLevelWasLoaded was found on DOTweenComponent
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
Assertion failed on expression: 'pred(*previous, *i)'
Native extension for Android target not found
Native extension for WindowsStandalone target not found
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Refreshing native plugins compatible for Editor in 0.81 ms, found 3 plugins.
SceneCameraResetButton: Initialized.
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:Log (object)
SceneCameraResetButton:.cctor () (at Assets/Editor/Tools/SceneCameraResetButton.cs:15)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: Assets/Editor/Tools/SceneCameraResetButton.cs Line: 15)

The project currently uses the compatibility mode where the Render Graph API is disabled. Support for this mode will be removed in future Unity versions. Migrate existing ScriptableRenderPasses to the new RenderGraph API. After the migration, disable the compatibility mode in Edit > Projects Settings > Graphics > Render Graph.
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogWarning (object)
UnityEngine.Rendering.Universal.WarnUsingNonRenderGraph:EmitConsoleWarning () (at ./Packages/com.unity.render-pipelines.universal/Runtime/Data/UniversalRenderPipelineAsset.cs:427)
System.Reflection.RuntimeMethodInfo:Invoke (object,System.Reflection.BindingFlags,System.Reflection.Binder,object[],System.Globalization.CultureInfo)
System.Reflection.MethodBase:Invoke (object,object[])
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadMethodAttributes ()

(Filename: ./Packages/com.unity.render-pipelines.universal/Runtime/Data/UniversalRenderPipelineAsset.cs Line: 427)

Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.899 seconds
Domain Reload Profiling: 1668ms
	BeginReloadAssembly (149ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (4ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (54ms)
	RebuildCommonClasses (23ms)
	RebuildNativeTypeToScriptingClass (8ms)
	initialDomainReloadingComplete (36ms)
	LoadAllAssembliesAndSetupDomain (552ms)
		LoadAssemblies (330ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (282ms)
			TypeCache.Refresh (34ms)
				TypeCache.ScanAssembly (22ms)
			BuildScriptInfoCaches (229ms)
			ResolveRequiredComponents (14ms)
	FinalizeReload (900ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (708ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (8ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (94ms)
			ProcessInitializeOnLoadAttributes (468ms)
			ProcessInitializeOnLoadMethodAttributes (132ms)
			AfterProcessingInitializeOnLoad (3ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (8ms)
Refreshing native plugins compatible for Editor in 1.25 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 29 Unused Serialized files (Serialized files now loaded: 0)
Unloading 8934 unused Assets / (7.8 MB). Loaded Objects now: 10189.
Memory consumption went from 242.6 MB to 234.8 MB.
Total: 31.070500 ms (FindLiveObjects: 0.886400 ms CreateObjectMapping: 0.645500 ms MarkObjects: 25.262600 ms  DeleteObjects: 4.274900 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
OnLevelWasLoaded was found on DOTweenComponent
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
Refreshing native plugins compatible for Editor in 1.43 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 29 Unused Serialized files (Serialized files now loaded: 0)
Unloading 8921 unused Assets / (7.2 MB). Loaded Objects now: 10189.
Memory consumption went from 242.7 MB to 235.6 MB.
Total: 30.793100 ms (FindLiveObjects: 0.855100 ms CreateObjectMapping: 0.705600 ms MarkObjects: 25.595400 ms  DeleteObjects: 3.635300 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Duplicate assembly 'System.Runtime.CompilerServices.Unsafe.dll' with different versions detected, using 'Packages/com.bezi.sidekick/Plugins/System.Runtime.CompilerServices.Unsafe.dll, AssemblyName=System.Runtime.CompilerServices.Unsafe, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a' and ignoring 'Assets/Plugins/Protobuf/System.Runtime.CompilerServices.Unsafe.dll, AssemblyName=System.Runtime.CompilerServices.Unsafe, Version=4.0.4.1, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a'.Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.835 seconds
OnLevelWasLoaded was found on DOTweenComponent
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
Refreshing native plugins compatible for Editor in 0.88 ms, found 3 plugins.
OnLevelWasLoaded was found on DOTweenComponent
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
Assertion failed on expression: 'pred(*previous, *i)'
Native extension for Android target not found
Native extension for WindowsStandalone target not found
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Refreshing native plugins compatible for Editor in 0.77 ms, found 3 plugins.
SceneCameraResetButton: Initialized.
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:Log (object)
SceneCameraResetButton:.cctor () (at Assets/Editor/Tools/SceneCameraResetButton.cs:15)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: Assets/Editor/Tools/SceneCameraResetButton.cs Line: 15)

The project currently uses the compatibility mode where the Render Graph API is disabled. Support for this mode will be removed in future Unity versions. Migrate existing ScriptableRenderPasses to the new RenderGraph API. After the migration, disable the compatibility mode in Edit > Projects Settings > Graphics > Render Graph.
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogWarning (object)
UnityEngine.Rendering.Universal.WarnUsingNonRenderGraph:EmitConsoleWarning () (at ./Packages/com.unity.render-pipelines.universal/Runtime/Data/UniversalRenderPipelineAsset.cs:427)
System.Reflection.RuntimeMethodInfo:Invoke (object,System.Reflection.BindingFlags,System.Reflection.Binder,object[],System.Globalization.CultureInfo)
System.Reflection.MethodBase:Invoke (object,object[])
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadMethodAttributes ()

(Filename: ./Packages/com.unity.render-pipelines.universal/Runtime/Data/UniversalRenderPipelineAsset.cs Line: 427)

Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.895 seconds
Domain Reload Profiling: 1734ms
	BeginReloadAssembly (160ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (4ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (57ms)
	RebuildCommonClasses (24ms)
	RebuildNativeTypeToScriptingClass (9ms)
	initialDomainReloadingComplete (36ms)
	LoadAllAssembliesAndSetupDomain (609ms)
		LoadAssemblies (367ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (308ms)
			TypeCache.Refresh (42ms)
				TypeCache.ScanAssembly (24ms)
			BuildScriptInfoCaches (247ms)
			ResolveRequiredComponents (13ms)
	FinalizeReload (895ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (701ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (7ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (91ms)
			ProcessInitializeOnLoadAttributes (458ms)
			ProcessInitializeOnLoadMethodAttributes (138ms)
			AfterProcessingInitializeOnLoad (3ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (9ms)
Refreshing native plugins compatible for Editor in 1.25 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 29 Unused Serialized files (Serialized files now loaded: 0)
Unloading 8934 unused Assets / (7.0 MB). Loaded Objects now: 10191.
Memory consumption went from 244.4 MB to 237.4 MB.
Total: 29.357100 ms (FindLiveObjects: 1.331900 ms CreateObjectMapping: 0.646600 ms MarkObjects: 23.469800 ms  DeleteObjects: 3.907300 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Duplicate assembly 'System.Runtime.CompilerServices.Unsafe.dll' with different versions detected, using 'Packages/com.bezi.sidekick/Plugins/System.Runtime.CompilerServices.Unsafe.dll, AssemblyName=System.Runtime.CompilerServices.Unsafe, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a' and ignoring 'Assets/Plugins/Protobuf/System.Runtime.CompilerServices.Unsafe.dll, AssemblyName=System.Runtime.CompilerServices.Unsafe, Version=4.0.4.1, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a'.Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.847 seconds
OnLevelWasLoaded was found on DOTweenComponent
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
Refreshing native plugins compatible for Editor in 0.73 ms, found 3 plugins.
OnLevelWasLoaded was found on DOTweenComponent
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
Assertion failed on expression: 'pred(*previous, *i)'
Native extension for Android target not found
Native extension for WindowsStandalone target not found
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Refreshing native plugins compatible for Editor in 0.82 ms, found 3 plugins.
SceneCameraResetButton: Initialized.
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:Log (object)
SceneCameraResetButton:.cctor () (at Assets/Editor/Tools/SceneCameraResetButton.cs:15)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: Assets/Editor/Tools/SceneCameraResetButton.cs Line: 15)

The project currently uses the compatibility mode where the Render Graph API is disabled. Support for this mode will be removed in future Unity versions. Migrate existing ScriptableRenderPasses to the new RenderGraph API. After the migration, disable the compatibility mode in Edit > Projects Settings > Graphics > Render Graph.
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogWarning (object)
UnityEngine.Rendering.Universal.WarnUsingNonRenderGraph:EmitConsoleWarning () (at ./Packages/com.unity.render-pipelines.universal/Runtime/Data/UniversalRenderPipelineAsset.cs:427)
System.Reflection.RuntimeMethodInfo:Invoke (object,System.Reflection.BindingFlags,System.Reflection.Binder,object[],System.Globalization.CultureInfo)
System.Reflection.MethodBase:Invoke (object,object[])
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadMethodAttributes ()

(Filename: ./Packages/com.unity.render-pipelines.universal/Runtime/Data/UniversalRenderPipelineAsset.cs Line: 427)

Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.896 seconds
Domain Reload Profiling: 1746ms
	BeginReloadAssembly (175ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (4ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (56ms)
	RebuildCommonClasses (27ms)
	RebuildNativeTypeToScriptingClass (8ms)
	initialDomainReloadingComplete (37ms)
	LoadAllAssembliesAndSetupDomain (603ms)
		LoadAssemblies (365ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (318ms)
			TypeCache.Refresh (9ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (285ms)
			ResolveRequiredComponents (15ms)
	FinalizeReload (896ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (697ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (8ms)
			SetLoadedEditorAssemblies (5ms)
			BeforeProcessingInitializeOnLoad (97ms)
			ProcessInitializeOnLoadAttributes (461ms)
			ProcessInitializeOnLoadMethodAttributes (124ms)
			AfterProcessingInitializeOnLoad (3ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (8ms)
Refreshing native plugins compatible for Editor in 1.27 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 29 Unused Serialized files (Serialized files now loaded: 0)
Unloading 8934 unused Assets / (7.0 MB). Loaded Objects now: 10193.
Memory consumption went from 244.6 MB to 237.6 MB.
Total: 25.133100 ms (FindLiveObjects: 0.881000 ms CreateObjectMapping: 0.762500 ms MarkObjects: 20.186300 ms  DeleteObjects: 3.301600 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Duplicate assembly 'System.Runtime.CompilerServices.Unsafe.dll' with different versions detected, using 'Packages/com.bezi.sidekick/Plugins/System.Runtime.CompilerServices.Unsafe.dll, AssemblyName=System.Runtime.CompilerServices.Unsafe, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a' and ignoring 'Assets/Plugins/Protobuf/System.Runtime.CompilerServices.Unsafe.dll, AssemblyName=System.Runtime.CompilerServices.Unsafe, Version=4.0.4.1, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a'.Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.748 seconds
OnLevelWasLoaded was found on DOTweenComponent
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
Refreshing native plugins compatible for Editor in 0.73 ms, found 3 plugins.
OnLevelWasLoaded was found on DOTweenComponent
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
Assertion failed on expression: 'pred(*previous, *i)'
Native extension for Android target not found
Native extension for WindowsStandalone target not found
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Refreshing native plugins compatible for Editor in 0.82 ms, found 3 plugins.
SceneCameraResetButton: Initialized.
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:Log (object)
SceneCameraResetButton:.cctor () (at Assets/Editor/Tools/SceneCameraResetButton.cs:15)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: Assets/Editor/Tools/SceneCameraResetButton.cs Line: 15)

The project currently uses the compatibility mode where the Render Graph API is disabled. Support for this mode will be removed in future Unity versions. Migrate existing ScriptableRenderPasses to the new RenderGraph API. After the migration, disable the compatibility mode in Edit > Projects Settings > Graphics > Render Graph.
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogWarning (object)
UnityEngine.Rendering.Universal.WarnUsingNonRenderGraph:EmitConsoleWarning () (at ./Packages/com.unity.render-pipelines.universal/Runtime/Data/UniversalRenderPipelineAsset.cs:427)
System.Reflection.RuntimeMethodInfo:Invoke (object,System.Reflection.BindingFlags,System.Reflection.Binder,object[],System.Globalization.CultureInfo)
System.Reflection.MethodBase:Invoke (object,object[])
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadMethodAttributes ()

(Filename: ./Packages/com.unity.render-pipelines.universal/Runtime/Data/UniversalRenderPipelineAsset.cs Line: 427)

Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.878 seconds
Domain Reload Profiling: 1630ms
	BeginReloadAssembly (140ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (4ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (49ms)
	RebuildCommonClasses (22ms)
	RebuildNativeTypeToScriptingClass (8ms)
	initialDomainReloadingComplete (37ms)
	LoadAllAssembliesAndSetupDomain (543ms)
		LoadAssemblies (328ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (272ms)
			TypeCache.Refresh (10ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (242ms)
			ResolveRequiredComponents (14ms)
	FinalizeReload (879ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (683ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (7ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (88ms)
			ProcessInitializeOnLoadAttributes (449ms)
			ProcessInitializeOnLoadMethodAttributes (132ms)
			AfterProcessingInitializeOnLoad (3ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (8ms)
Refreshing native plugins compatible for Editor in 1.36 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 29 Unused Serialized files (Serialized files now loaded: 0)
Unloading 8934 unused Assets / (7.7 MB). Loaded Objects now: 10195.
Memory consumption went from 244.5 MB to 236.8 MB.
Total: 23.636700 ms (FindLiveObjects: 0.864600 ms CreateObjectMapping: 0.665900 ms MarkObjects: 18.320800 ms  DeleteObjects: 3.784200 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
OnLevelWasLoaded was found on DOTweenComponent
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
Refreshing native plugins compatible for Editor in 1.45 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 29 Unused Serialized files (Serialized files now loaded: 0)
Unloading 8921 unused Assets / (7.4 MB). Loaded Objects now: 10195.
Memory consumption went from 244.7 MB to 237.3 MB.
Total: 29.964200 ms (FindLiveObjects: 0.905200 ms CreateObjectMapping: 0.588100 ms MarkObjects: 24.844900 ms  DeleteObjects: 3.624400 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Duplicate assembly 'System.Runtime.CompilerServices.Unsafe.dll' with different versions detected, using 'Packages/com.bezi.sidekick/Plugins/System.Runtime.CompilerServices.Unsafe.dll, AssemblyName=System.Runtime.CompilerServices.Unsafe, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a' and ignoring 'Assets/Plugins/Protobuf/System.Runtime.CompilerServices.Unsafe.dll, AssemblyName=System.Runtime.CompilerServices.Unsafe, Version=4.0.4.1, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a'.Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.905 seconds
OnLevelWasLoaded was found on DOTweenComponent
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
Refreshing native plugins compatible for Editor in 0.75 ms, found 3 plugins.
OnLevelWasLoaded was found on DOTweenComponent
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
Assertion failed on expression: 'pred(*previous, *i)'
Native extension for Android target not found
Native extension for WindowsStandalone target not found
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Refreshing native plugins compatible for Editor in 1.24 ms, found 3 plugins.
SceneCameraResetButton: Initialized.
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:Log (object)
SceneCameraResetButton:.cctor () (at Assets/Editor/Tools/SceneCameraResetButton.cs:15)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: Assets/Editor/Tools/SceneCameraResetButton.cs Line: 15)

The project currently uses the compatibility mode where the Render Graph API is disabled. Support for this mode will be removed in future Unity versions. Migrate existing ScriptableRenderPasses to the new RenderGraph API. After the migration, disable the compatibility mode in Edit > Projects Settings > Graphics > Render Graph.
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogWarning (object)
UnityEngine.Rendering.Universal.WarnUsingNonRenderGraph:EmitConsoleWarning () (at ./Packages/com.unity.render-pipelines.universal/Runtime/Data/UniversalRenderPipelineAsset.cs:427)
System.Reflection.RuntimeMethodInfo:Invoke (object,System.Reflection.BindingFlags,System.Reflection.Binder,object[],System.Globalization.CultureInfo)
System.Reflection.MethodBase:Invoke (object,object[])
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadMethodAttributes ()

(Filename: ./Packages/com.unity.render-pipelines.universal/Runtime/Data/UniversalRenderPipelineAsset.cs Line: 427)

Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.049 seconds
Domain Reload Profiling: 1958ms
	BeginReloadAssembly (176ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (4ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (75ms)
	RebuildCommonClasses (26ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (39ms)
	LoadAllAssembliesAndSetupDomain (658ms)
		LoadAssemblies (371ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (349ms)
			TypeCache.Refresh (40ms)
				TypeCache.ScanAssembly (26ms)
			BuildScriptInfoCaches (287ms)
			ResolveRequiredComponents (14ms)
	FinalizeReload (1049ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (825ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (9ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (105ms)
			ProcessInitializeOnLoadAttributes (541ms)
			ProcessInitializeOnLoadMethodAttributes (163ms)
			AfterProcessingInitializeOnLoad (3ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (7ms)
Refreshing native plugins compatible for Editor in 1.71 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 29 Unused Serialized files (Serialized files now loaded: 0)
Unloading 8934 unused Assets / (7.5 MB). Loaded Objects now: 10197.
Memory consumption went from 244.6 MB to 237.1 MB.
Total: 28.879600 ms (FindLiveObjects: 0.928900 ms CreateObjectMapping: 0.845400 ms MarkObjects: 22.921500 ms  DeleteObjects: 4.182400 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
OnLevelWasLoaded was found on DOTweenComponent
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
Refreshing native plugins compatible for Editor in 1.78 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 29 Unused Serialized files (Serialized files now loaded: 0)
Unloading 8921 unused Assets / (8.2 MB). Loaded Objects now: 10197.
Memory consumption went from 244.7 MB to 236.5 MB.
Total: 34.953400 ms (FindLiveObjects: 0.906500 ms CreateObjectMapping: 0.707900 ms MarkObjects: 28.681700 ms  DeleteObjects: 4.655400 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Duplicate assembly 'System.Runtime.CompilerServices.Unsafe.dll' with different versions detected, using 'Packages/com.bezi.sidekick/Plugins/System.Runtime.CompilerServices.Unsafe.dll, AssemblyName=System.Runtime.CompilerServices.Unsafe, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a' and ignoring 'Assets/Plugins/Protobuf/System.Runtime.CompilerServices.Unsafe.dll, AssemblyName=System.Runtime.CompilerServices.Unsafe, Version=4.0.4.1, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a'.Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.755 seconds
OnLevelWasLoaded was found on DOTweenComponent
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
Refreshing native plugins compatible for Editor in 0.67 ms, found 3 plugins.
OnLevelWasLoaded was found on DOTweenComponent
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
Assertion failed on expression: 'pred(*previous, *i)'
Native extension for Android target not found
Native extension for WindowsStandalone target not found
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Refreshing native plugins compatible for Editor in 0.85 ms, found 3 plugins.
SceneCameraResetButton: Initialized.
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:Log (object)
SceneCameraResetButton:.cctor () (at Assets/Editor/Tools/SceneCameraResetButton.cs:15)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: Assets/Editor/Tools/SceneCameraResetButton.cs Line: 15)

The project currently uses the compatibility mode where the Render Graph API is disabled. Support for this mode will be removed in future Unity versions. Migrate existing ScriptableRenderPasses to the new RenderGraph API. After the migration, disable the compatibility mode in Edit > Projects Settings > Graphics > Render Graph.
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogWarning (object)
UnityEngine.Rendering.Universal.WarnUsingNonRenderGraph:EmitConsoleWarning () (at ./Packages/com.unity.render-pipelines.universal/Runtime/Data/UniversalRenderPipelineAsset.cs:427)
System.Reflection.RuntimeMethodInfo:Invoke (object,System.Reflection.BindingFlags,System.Reflection.Binder,object[],System.Globalization.CultureInfo)
System.Reflection.MethodBase:Invoke (object,object[])
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadMethodAttributes ()

(Filename: ./Packages/com.unity.render-pipelines.universal/Runtime/Data/UniversalRenderPipelineAsset.cs Line: 427)

Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.866 seconds
Domain Reload Profiling: 1625ms
	BeginReloadAssembly (145ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (4ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (51ms)
	RebuildCommonClasses (21ms)
	RebuildNativeTypeToScriptingClass (8ms)
	initialDomainReloadingComplete (36ms)
	LoadAllAssembliesAndSetupDomain (549ms)
		LoadAssemblies (333ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (274ms)
			TypeCache.Refresh (35ms)
				TypeCache.ScanAssembly (22ms)
			BuildScriptInfoCaches (220ms)
			ResolveRequiredComponents (14ms)
	FinalizeReload (867ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (674ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (7ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (86ms)
			ProcessInitializeOnLoadAttributes (440ms)
			ProcessInitializeOnLoadMethodAttributes (134ms)
			AfterProcessingInitializeOnLoad (3ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (9ms)
Refreshing native plugins compatible for Editor in 1.45 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 29 Unused Serialized files (Serialized files now loaded: 0)
Unloading 8934 unused Assets / (8.3 MB). Loaded Objects now: 10199.
Memory consumption went from 244.6 MB to 236.3 MB.
Total: 25.036800 ms (FindLiveObjects: 1.332700 ms CreateObjectMapping: 0.709200 ms MarkObjects: 18.991600 ms  DeleteObjects: 4.001600 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Duplicate assembly 'System.Runtime.CompilerServices.Unsafe.dll' with different versions detected, using 'Packages/com.bezi.sidekick/Plugins/System.Runtime.CompilerServices.Unsafe.dll, AssemblyName=System.Runtime.CompilerServices.Unsafe, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a' and ignoring 'Assets/Plugins/Protobuf/System.Runtime.CompilerServices.Unsafe.dll, AssemblyName=System.Runtime.CompilerServices.Unsafe, Version=4.0.4.1, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a'.Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.754 seconds
OnLevelWasLoaded was found on DOTweenComponent
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
Refreshing native plugins compatible for Editor in 0.88 ms, found 3 plugins.
OnLevelWasLoaded was found on DOTweenComponent
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
Assertion failed on expression: 'pred(*previous, *i)'
Native extension for Android target not found
Native extension for WindowsStandalone target not found
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Refreshing native plugins compatible for Editor in 0.80 ms, found 3 plugins.
SceneCameraResetButton: Initialized.
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:Log (object)
SceneCameraResetButton:.cctor () (at Assets/Editor/Tools/SceneCameraResetButton.cs:15)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: Assets/Editor/Tools/SceneCameraResetButton.cs Line: 15)

The project currently uses the compatibility mode where the Render Graph API is disabled. Support for this mode will be removed in future Unity versions. Migrate existing ScriptableRenderPasses to the new RenderGraph API. After the migration, disable the compatibility mode in Edit > Projects Settings > Graphics > Render Graph.
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogWarning (object)
UnityEngine.Rendering.Universal.WarnUsingNonRenderGraph:EmitConsoleWarning () (at ./Packages/com.unity.render-pipelines.universal/Runtime/Data/UniversalRenderPipelineAsset.cs:427)
System.Reflection.RuntimeMethodInfo:Invoke (object,System.Reflection.BindingFlags,System.Reflection.Binder,object[],System.Globalization.CultureInfo)
System.Reflection.MethodBase:Invoke (object,object[])
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadMethodAttributes ()

(Filename: ./Packages/com.unity.render-pipelines.universal/Runtime/Data/UniversalRenderPipelineAsset.cs Line: 427)

Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.886 seconds
Domain Reload Profiling: 1644ms
	BeginReloadAssembly (144ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (4ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (49ms)
	RebuildCommonClasses (22ms)
	RebuildNativeTypeToScriptingClass (8ms)
	initialDomainReloadingComplete (37ms)
	LoadAllAssembliesAndSetupDomain (548ms)
		LoadAssemblies (336ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (269ms)
			TypeCache.Refresh (9ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (239ms)
			ResolveRequiredComponents (14ms)
	FinalizeReload (886ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (693ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (7ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (94ms)
			ProcessInitializeOnLoadAttributes (452ms)
			ProcessInitializeOnLoadMethodAttributes (133ms)
			AfterProcessingInitializeOnLoad (3ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (8ms)
Refreshing native plugins compatible for Editor in 1.55 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 29 Unused Serialized files (Serialized files now loaded: 0)
Unloading 8934 unused Assets / (7.5 MB). Loaded Objects now: 10201.
Memory consumption went from 244.4 MB to 237.0 MB.
Total: 26.015600 ms (FindLiveObjects: 0.922100 ms CreateObjectMapping: 0.615500 ms MarkObjects: 20.821600 ms  DeleteObjects: 3.654900 ms)

Prepare: number of updated asset objects reloaded= 0

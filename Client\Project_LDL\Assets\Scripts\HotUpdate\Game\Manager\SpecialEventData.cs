using System;
using System.Collections.Generic;
using System.Linq;
using Activity;
using Game.Hotfix.Config;
using UnityEngine;
using UnityEngine.Events;

namespace Game.Hotfix
{
    public class SpecialEventData
    {
        //==============================后端返回  [配置]  数据缓存==============================
        private Dictionary<int, PushActivityPowerConfig> wishLightConfigDic;//希望的灯火模板活动缓存：（int）模板id作为键
        
        //==============================后端返回  [变更]  数据缓存==============================
        private Dictionary<int, PushActivityPowerData> wishLightMsgDic;//希望的灯火模板活动缓存：（int）模板id作为键
        
        protected ChaoZhiData ChaoZhiManager => GameEntry.LogicData.ChaoZhiData;
        
        //邮件配置数据
        private activity_set activitySet; 
        public activity_set ActivitySet
        {
            get
            {
                if (activitySet == null)
                {
                    if (ToolScriptExtend.GetTable<activity_set>(out var config))
                    {
                        activitySet = config[0];
                    }
                }

                return activitySet;
            }
        }
        
        private Dictionary<int, int> RedDotDic;//红点字典
        
        
        //判断功能入口解锁初始化逻辑
        public void CheckSpecialEventEntry(bool isInit,GameObject entryObj)
        {
            var isSpecialEventUnlock = IsSpecialEventUnlock();
            if (isSpecialEventUnlock)
            {
                if (!isInit)
                {
                    UpdateSpecialEventActivityMsg();
                }
            }
            else
            {
                entryObj.SetActive(false);
            }
        }
        
        public void Init()
        {
            RedDotDic = new Dictionary<int, int>();
            wishLightConfigDic = new Dictionary<int, PushActivityPowerConfig>();
            wishLightMsgDic = new Dictionary<int, PushActivityPowerData>();
            
            //-------------------------------------------希望的灯火-----------------------------------
            //希望的灯火配置数据推送
            RegisterProtoEvent(Protocol.MessageID.PushActivityPowerConfig, OnPushWishLightConfig);
            //希望的灯火变更数据推送
            RegisterProtoEvent(Protocol.MessageID.PushActivityPowerData, OnPushWishLightData);
        }
        
        //注册协议推送逻辑
        private void RegisterProtoEvent(Protocol.MessageID id, UnityAction<object> callback = null)
        {
            var ProtoId = (int)id;
            NetEventDispatch.Instance.RegisterEvent(ProtoId, message =>
            {
                callback?.Invoke(message);
            });
        }
        
        //特殊事件入口是否解锁
        public bool IsSpecialEventUnlock()
        {
            if (ActivitySet.events_entry_demand == 0)
            {
                return false;
            }
            return ToolScriptExtend.GetDemandUnlock((int)ActivitySet.events_entry_demand);
        }
        
        #region 红点逻辑
        //特殊事件入口红点
        public int CheckSpecialEventRedDot()
        {
            var sum = 0;
            if (RedDotDic != null)
            {
                foreach (var data in RedDotDic)
                {
                    sum += data.Value;
                }
            }
            return sum;
        }
        
        public int GetRedDotCountById(int templateId)
        {
            if (RedDotDic.TryGetValue(templateId, out var result))
            {
                return result;
            }
            return 0;
        }
        
        //希望的灯火红点逻辑
        public int CheckWishLightRedDot(int templateId)
        {
            var ConfigData = GetWishLightConfig(templateId);
            var MsgData = GetWishLightMsg(templateId);
            if (ConfigData == null || MsgData == null)
            {
                return 0;
            }

            var sum = 0;
            var dataList = ConfigData.PowerRewards.ToList();
            foreach (var data in dataList)
            {
                //0:未达标 1：可领取 2：已领取
                var status = CheckWishLightItemStatus(MsgData,data);
                if (status == 1)
                {
                    sum++;
                }
            }
            
            UpdateRedDotLogic(templateId, sum);
            return sum;
        }
        
        private void UpdateRedDotLogic(int templateId,int count)
        {
            RedDotDic[templateId] = count;
            CheckAndRefreshForm<ValueTuple<string, int>>(EnumUIForm.UISpecialEventForm,("RedDot",templateId));
            GameEntry.Event.Fire(SpecialEventArgs.EventId, SpecialEventArgs.Create());
        }
        
        #endregion
        
        #region 导表配置
        
        
        
        
        #endregion
        
        #region 协议
        //协议返回数据打印
        private void ProtoLog(bool isRequest,string protoName,object data)
        {
            bool isShow = true;
            if (isShow)
            {
                ColorLog.ProtoLog(isRequest,protoName,data);
            }
        }
        #endregion
        
        //先判断界面是否打开，然后在推刷新逻辑
        public void CheckAndRefreshForm<T>(EnumUIForm flag,T data)
        {
            var form = GameEntry.UI.GetUIForm(flag);
            if (form != null)
            {
                GameEntry.UI.RefreshUIForm(flag,data);
            }
        }
        
        #region 希望的灯火
        //希望的灯火配置数据推送
        private void OnPushWishLightConfig(object message)
        {
            var resp = (PushActivityPowerConfig)message;
            ProtoLog(false, " 希望的灯火配置数据推送", resp);
            wishLightConfigDic[(int)resp.Template] = resp;
            
        }
        
        //希望的灯火变更数据推送
        private void OnPushWishLightData(object message)
        {
            var resp = (PushActivityPowerData)message;
            ProtoLog(false, "希望的灯火变更数据推送", resp);
            var templateId = (int)resp.Template;
            wishLightMsgDic[templateId] = resp;
            CheckWishLightRedDot(templateId);
            ChaoZhiManager.CheckAndRefreshForm<ValueTuple<string, int>>(EnumUIForm.UISpecialEventForm,("SpecialEvent_WishLight",1));
        }
        
        //获取希望的灯火活动的配置缓存数据
        public PushActivityPowerConfig GetWishLightConfig(int templateId)
        {
            if (wishLightConfigDic.TryGetValue(templateId, out var data))
            {
                return data;
            }
            return null;
        }
        
        //获取希望的灯火活动的变更缓存数据
        public PushActivityPowerData GetWishLightMsg(int templateId)
        {
            if (wishLightMsgDic.TryGetValue(templateId, out var data))
            {
                return data;
            }
            return null;
        }
        
        //判断希望的灯火任务完成状态
        //0:未达标 1：可领取 2：已领取
        public int CheckWishLightItemStatus(PushActivityPowerData msgData,ActivityPowerCondition config)
        {
            var myPower = (int)GameEntry.LogicData.RoleData.Power;
            if (myPower < config.Power)
            {
                return 0;
            }
            else
            {
                return msgData.DrawIds.Contains(config.Id) ? 2 : 1;
            }
        }
        
        #endregion
        
        public void LoadSpecialEventActivityList(Action<List<ActivityTime>> callback = null)
        {
            ChaoZhiManager.C2SActivityOpenInfosReq((resp) =>
            {
                var activityList = ChaoZhiManager.GetActivityList(3,false);
                if (activityList.Count > 0)
                {
                    callback?.Invoke(activityList);
                }
            });
        }

        public void UpdateSpecialEventActivityMsg(Action<List<ActivityTime>> callback = null)
        {
            LoadSpecialEventActivityList((list) =>
            {
                foreach (var data in list)
                {
                    ChaoZhiManager.C2SLoadActivityInfo(data);
                }
                callback?.Invoke(list);
            });
        }
    }
}


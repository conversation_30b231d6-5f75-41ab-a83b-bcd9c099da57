using UnityEngine;

namespace Game.Hotfix
{
    public enum MapOpType
    {
        DEFAULT_MODE,
        BUILDING_CONSTRUCT_MODE,
        BUILDING_MOVE_MODE,
        TOWN_MOVE_MODE
    }

    public class MapOperationController
    {
        private readonly float m_zoomMax = 25;
        private readonly float m_ZoomMin = 5;
        private readonly float m_ZoomSpeed = 5;
        private readonly int m_MapMaxSize = 140;

        private readonly float m_ZoomValueP = 7;
        private readonly float m_ZoomMaxP = 310;
        private readonly float m_ZoomMinP = 120;
        private readonly float m_ZoomSpeedP = 52;
        
        private OpModeDefault m_OpModeDefault;
        private OpModeBuildingConstruct m_OpModeBuildingConstruct;
        private OpModeBuildingMove m_OpModeBuildingMove;

        private OpModeBase m_CurOpMode = null;

        public MapOperationController()
        {
            m_OpModeDefault = new OpModeDefault();
            m_OpModeBuildingConstruct = new OpModeBuildingConstruct();
            m_OpModeBuildingMove = new OpModeBuildingMove();

            SwitchOp(MapOpType.DEFAULT_MODE);
        }

        private void SwitchOp(MapOpType type)
        {
            var old = m_CurOpMode?.GetOpType();
            
            m_CurOpMode?.SetEnable(false);
            
            if (type == MapOpType.DEFAULT_MODE)
            {
                m_CurOpMode = m_OpModeDefault;
            }
            else if (type == MapOpType.BUILDING_CONSTRUCT_MODE)
            {
                m_CurOpMode = m_OpModeBuildingConstruct;
            }
            else if (type == MapOpType.BUILDING_MOVE_MODE)
            {
                m_CurOpMode = m_OpModeBuildingMove;
            }

            if (m_CurOpMode != null) m_CurOpMode.SetEnable(true);

            GameEntry.Event.Fire(this, OnOpModeChangeEventArgs.Create(old, m_CurOpMode?.GetOpType()));
        }

        /// <summary>
        /// 进入建造模式
        /// </summary>
        /// <param name="buildId"></param>
        public void BuildMode(int buildId,int level)
        {
            var cityCamera = GameEntry.Camera.CityCamera;
            var data = BuildingModule.Create(buildId,level, Game.GameEntry.Entity.GenerateSerialId());
            var pos = MapGridUtils.GetCameraLookAtWorldPosition(cityCamera);
            var tempPos = MapGridUtils.WorldToGrid(pos);
            
            //尝试找一个空地
            var cityMap = GameEntry.CityMap;
            if (cityMap)
            {
                var v = cityMap.GetEmptyGridPos(data);
                if (v != null)
                {
                    tempPos = v.Value;
                    GameEntry.Camera.LookAtPosition(new Vector3(tempPos.x, 0, tempPos.y));
                }
            }
            
            data.SetGridPos(tempPos.x, tempPos.y);
            
            int uid = m_OpModeBuildingConstruct.SetBuildModule(data);
            SwitchOp(MapOpType.BUILDING_CONSTRUCT_MODE);
            
            //打开界面
            var formParam = new UIBuildingMoveFormParam
            {
                IsBuild = true,
                BuildingModule = data,
                TargetUid = uid
            };
            GameEntry.UI.OpenUIForm(EnumUIForm.UIBuildingMoveForm, formParam);
        }

        /// <summary>
        /// 取消建造
        /// </summary>
        public void BuildCancel()
        {
            if (this.m_CurOpMode.GetOpType() == MapOpType.BUILDING_CONSTRUCT_MODE)
            {
                m_OpModeBuildingConstruct.CancelBuildModule();
            }
            SwitchOp(MapOpType.DEFAULT_MODE);
        }

        public void BuildConfirm()
        {
            if (this.m_CurOpMode.GetOpType() == MapOpType.BUILDING_CONSTRUCT_MODE)
            {
                m_OpModeBuildingConstruct.BuildConfirm();
            }
            SwitchOp(MapOpType.DEFAULT_MODE);
        }

        public void BuildingBeginMove(EL_Building building)
        {
            int uid = m_OpModeBuildingMove.SetBuilding(building);
            SwitchOp(MapOpType.BUILDING_MOVE_MODE);
            
            var formParam = new UIBuildingMoveFormParam
            {
                IsBuild = false,
                BuildingModule = building.GetBuildingModule(),
                TargetUid = uid
            };
            GameEntry.UI.OpenUIForm(EnumUIForm.UIBuildingMoveForm, formParam);
            if (GameEntry.UI.HasUIForm(EnumUIForm.UIBuildingMenuForm))
            {
                GameEntry.UI.CloseUIForm(EnumUIForm.UIBuildingMenuForm);
            }
        }

        public void BuildingMoveCancel()
        {
            if (this.m_CurOpMode.GetOpType() == MapOpType.BUILDING_MOVE_MODE)
            {
                m_OpModeBuildingMove.BuildingMoveCancel();
            }
            SwitchOp(MapOpType.DEFAULT_MODE);
        }

        public void BuildingMoveConfirm()
        {
            if (this.m_CurOpMode.GetOpType() == MapOpType.BUILDING_MOVE_MODE)
            {
                m_OpModeBuildingMove.BuildingMoveConfirm();
            }
            SwitchOp(MapOpType.DEFAULT_MODE);
        }
        
        public void Update()
        {
            if (m_CurOpMode != null)
            {
                m_CurOpMode.Update(Time.deltaTime);
            }
        }

        public void LateUpdate()
        {
            if (m_CurOpMode != null)
            {
                m_CurOpMode.LateUpdate(Time.deltaTime);
            }
        }

        public void Destroy()
        {
            m_OpModeDefault.Destroy();
            m_OpModeBuildingConstruct.Destroy();
            m_OpModeBuildingMove.Destroy();
        }
    }
}
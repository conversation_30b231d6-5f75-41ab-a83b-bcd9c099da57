using System;
using System.Collections;
using System.Collections.Generic;
using Game.Hotfix.Config;
using GameFramework.Event;
using OfficeOpenXml.FormulaParsing.Excel.Functions.Math;
using UnityEditor;
using UnityEngine;
using UnityEngine.UI;
using UnityGameFramework.Runtime;

namespace Game.Hotfix
{
    public partial class UIUnionHelpForm : UGuiFormEx
    {
        private int limitNum;
        private List<Union.UnionHelp> helpList = new();

        private float deltaTime = 0;
        private Dictionary<int, Action> timeActDic = new();
        private List<int> timeActList = new();
        protected override void OnInit(object userData)
        {
            base.OnInit(userData);

            InitBind();

            m_txtNone.text = ToolScriptExtend.GetLang(80400009);

            var config = GameEntry.LogicData.UnionData.GetUnionConst(44);
            limitNum = config != null ? int.Parse(config[0]) : 0;

            m_TableViewV.GetItemCount = () => helpList.Count;
            m_TableViewV.GetItemGo = () => m_goItem;
            m_TableViewV.UpdateItemCell = OnUpdateItem;
        }

        protected override void OnOpen(object userData)
        {
            base.OnOpen(userData);

            if (!GameEntry.Event.Check(UnionHelpChangeEventArgs.EventId, OnUpdateEvent))
                GameEntry.Event.Subscribe(UnionHelpChangeEventArgs.EventId, OnUpdateEvent);

            GameEntry.LogicData.UnionData.OnReqUnionHelpList((score, list) =>
            {
                OnUpdateInfo(score, list);
            });
            OnBtnTipMaskClick();
        }

        protected override void OnClose(bool isShutdown, object userData)
        {
            base.OnClose(isShutdown, userData);
            if (GameEntry.Event.Check(UnionHelpChangeEventArgs.EventId, OnUpdateEvent))
                GameEntry.Event.Unsubscribe(UnionHelpChangeEventArgs.EventId, OnUpdateEvent);
        }

        public override void OnRefresh(object userData)
        {
            base.OnRefresh(userData);
        }

        protected override void OnUpdate(float elapseSeconds, float realElapseSeconds)
        {
            base.OnUpdate(elapseSeconds, realElapseSeconds);

            deltaTime += elapseSeconds;
            if (deltaTime >= 1)
            {
                deltaTime = 0;
                if (timeActList.Count > 0)
                {
                    for (int i = 0; i < timeActList.Count; i++)
                    {
                        timeActDic.TryGetValue(timeActList[i], out Action act);
                        act?.Invoke();
                    }
                }
            }
        }

        private void OnUpdateEvent(object sender, GameEventArgs e)
        {
            var unionHelpChangeEventArgs = e as UnionHelpChangeEventArgs;
            if (unionHelpChangeEventArgs.RefreshType != 1) return;
            
            GameEntry.LogicData.UnionData.OnReqUnionHelpList((score, list) =>
            {
                OnUpdateInfo(score, list, true);
            });
        }

        private void OnUpdateInfo(int score, List<Union.UnionHelp> list, bool isReload = false)
        {
            timeActDic.Clear();
            timeActList.Clear();

            list.Sort((a, b) =>
            {
                if (a.RoleId != b.RoleId)
                {
                    return a.RoleId == GameEntry.RoleData.RoleID ? -1 : 1;
                }
                return a.QueueId.CompareTo(b.QueueId);
            });
            helpList = list;
            m_txtNone.gameObject.SetActive(helpList.Count <= 0);

            if (isReload) m_TableViewV.ReloadData();
            else m_TableViewV.InitTableViewByIndex(0);

            var ratio = (float)score / limitNum;
            ratio = ratio > 1 ? 1 : ratio;
            m_imgProgress.rectTransform.sizeDelta = new Vector2(712.5f * ratio, 47);
            m_txtProgress.text = ToolScriptExtend.GetLangFormat(80400002, new Dictionary<string, object>
            {
                {"today_now", score}, {"today_max", limitNum}
            });
        }

        private void OnUpdateItem(int index, GameObject go)
        {
            var itemIdx = Convert.ToInt32(go.name);
            OnRemoveTimeAct(itemIdx);

            var rootTrans = go.transform.Find("bg");
            var bg = rootTrans.GetComponent<UIImage>();
            var headBg = rootTrans.Find("headBtn").GetComponent<UIImage>();
            var headSp = rootTrans.Find("headBtn/headSp").GetComponent<UIImage>();
            var nameTxt = rootTrans.Find("nameTxt").GetComponent<UIText>();
            var descTxt = rootTrans.Find("descTxt").GetComponent<UIText>();
            var progressBg = rootTrans.Find("progressBg").gameObject;
            var progressSp = rootTrans.Find("progressBg/progressSp").GetComponent<UIImage>();
            var progressTxt = rootTrans.Find("progressBg/progressTxt").GetComponent<UIText>();

            var data = helpList[index];
            var param = data.Args;
            var args0 = param.Count > 0 ? param[0] : 0;
            if (data.QueueType == Build.QueueType.BuildUpgrade)
            {
                var buildingCfg = Game.GameEntry.LDLTable.GetTableById<build_config>((int)data.BuildId);
                descTxt.text = ToolScriptExtend.GetLangFormat(80400003, new Dictionary<string, object>
                {
                    { "build_lv", args0}, {"build_name", ToolScriptExtend.GetLang(buildingCfg.name)}
                });
            }
            else if (data.QueueType == Build.QueueType.BuildTech)
            {
                var techConfig = Game.GameEntry.LDLTable.GetTableById<tech_config>((int)args0);
                descTxt.text = ToolScriptExtend.GetLangFormat(80400004, new Dictionary<string, object>
                {
                    {"tech_name", ToolScriptExtend.GetLang(techConfig.tech_title)}
                });
            }
            else if (data.QueueType == Build.QueueType.BuildSoldierTreatment)
            {
                descTxt.text = ToolScriptExtend.GetLang(80400005);
            }
            
            var isMySelf = data.RoleId == GameEntry.RoleData.RoleID;
            if (isMySelf)
            {
                var ratio = (float)data.HelpTimes / data.MaxHelpTimes;
                ratio = ratio > 1 ? 1 : ratio;
                progressSp.rectTransform.sizeDelta = new Vector2(307f * ratio, 47);

                var isShowTime = true;
                var time = 0;
                void OnCheckTime()
                {
                    if (time == 0)
                    {
                        isShowTime = !isShowTime;
                        time = 5;
                        if (isShowTime)
                        {
                            progressTxt.text = ToolScriptExtend.GetLangFormat(80400007, new Dictionary<string, object>
                            {
                                {"time_total", data.ReduceTime}
                            });
                        }
                        else
                        {
                            progressTxt.text = ToolScriptExtend.GetLangFormat(80400006, new Dictionary<string, object>
                            {
                                {"number_now", data.HelpTimes}, {"number_max", data.MaxHelpTimes}
                            });
                        }
                    }
                    time--;
                }
                OnCheckTime();
                OnAddTimeAct(itemIdx, OnCheckTime);
            }
            progressBg.SetActive(isMySelf);
            bg.SetImage(isMySelf ? "Sprite/ui_lianmeng/bangzhu_dikuang1.png" : "Sprite/ui_public/win2_small_dikuang2.png");

            GameEntry.LogicData.RoleData.RequestRoleQueryLocalSingle(data.RoleId, (roleBrief) =>
            {
                // headBg.SetImage(roleBrief.HeadBorder);
                // headSp.SetImage(roleBrief.IsCustomAvatar ? roleBrief.HeadSystemAvatar : roleBrief.HeadCustomAvatar);

                nameTxt.text = roleBrief.Name;
            });
        }

        private void OnAddTimeAct(int index, Action act)
        {
            if (!timeActDic.TryAdd(index, act))
            {
                timeActDic[index] = act;
            }
            else
            {
                timeActList.Add(index);
            }
        }

        private void OnRemoveTimeAct(int index)
        {
            timeActDic.Remove(index);
            timeActList.Remove(index);
        }

        private void OnBtnCloseClick()
        {
            Close();
        }

        private void OnBtnExpClick()
        {
            OnShowTip(1100288, m_btnExp.transform.position, true);
        }

        private void OnBtnTipClick()
        {
            OnShowTip(80400001, m_btnTip.transform.position, false);
        }

        private void OnShowTip(int langId, Vector3 pos, bool isLeft)
        {
            m_btnTipMask.gameObject.SetActive(true);

            m_txtTip.text = ToolScriptExtend.GetLang(langId);

            m_goTip.transform.SetPositionX(pos.x);
            m_goTip.transform.AddLocalPositionX(isLeft ? 350 : -350);
            m_transArrow.SetLocalPositionX(isLeft ? -330 : 330);
            m_transArrow.localRotation = Quaternion.Euler(0, 0, isLeft ? -90 : 90);

            var rect = m_goTip.GetComponent<RectTransform>();
            LayoutRebuilder.ForceRebuildLayoutImmediate(rect);
        }

        private void OnBtnOneKeyClick()
        {
            var isNeedHelp = GameEntry.LogicData.UnionData.IsNeedHelp();
            if (isNeedHelp)
            {
                GameEntry.LogicData.UnionData.OnReqUnionHelpAll((score, list) =>
                {
                    OnUpdateInfo(score, list);
                });
            }

            GameEntry.UI.OpenUIForm(EnumUIForm.UIFlyTextForm, new FlyTextParams()
            {
                Content = ToolScriptExtend.GetLang(isNeedHelp ? 80400011 : 80400012),
            });
        }

        private void OnBtnTipMaskClick()
        {
            if (m_btnTipMask.gameObject.activeSelf) m_btnTipMask.gameObject.SetActive(false);
        }
    }
}

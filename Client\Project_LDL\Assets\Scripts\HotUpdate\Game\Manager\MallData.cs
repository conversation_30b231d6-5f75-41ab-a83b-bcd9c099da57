using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using DG.Tweening;
using Game.Hotfix.Config;
using Google.Protobuf.Collections;
using Shop;
using UnityEngine;
using UnityEngine.Events;
using itemid = Game.Hotfix.Config.itemid;
using paymenttype = Game.Hotfix.Config.paymenttype;
using reward = Game.Hotfix.Config.reward;
using shopping_dailydeal = Game.Hotfix.Config.shopping_dailydeal;

namespace Game.Hotfix
{
    public class MallData
    {
        private GameObject rechargeScoreObj;//累充积分预制体资源引用
        private GameObject rewardObj;//背包道具通用预制体引用
        public GameObject RewardObj => rewardObj;
        
        public List<int> unlockIdList;//已解锁的商店id列表
        
        //每日特惠缓存数据
        private PushShopDailyDeal dailyDealMsg;
        
        //每日必买缓存数据
        private ShopDailyMustHaveLoadResp dailyMustBuyMsg;
        
        //每周特惠缓存数据
        private ShopWeekDealLoadResp weeklyDealMsg;
        
        //超级月卡缓存数据
        private ShopMonthCardLoadResp monthlyCardMsg;
        
        //周卡缓存数据
        private ShopWeekCardLoadResp weeklyCardMsg;
        
        //礼包商城缓存数据
        private ShopGiftPackMallLoadResp giftShopMsg;
        
        //黎明基金缓存数据
        private ShopDawnFundLoadResp growthFundMsg;
        
        //钻石直购缓存数据
        private ShopDiamondLoadResp diamondShopMsg;

        private List<shopping_foundation> fundConfigList;//基金列表配置数据
        
        
        //判断功能入口解锁初始化逻辑
        public void CheckMallEntry(bool isInit,GameObject entryObj)
        {
            var isMallActive = IsMallUnlock();
            entryObj.SetActive(isMallActive);
            //商城协议请求
            if (isMallActive)
            {
                if (!isInit)
                {
                    C2SShopLoadReq((resp) =>
                    {
                        RequestInitMsg();
                    });
                }
            }
        }
        
        public void Init()
        {
            unlockIdList = new List<int>();
            ToolScriptExtend.LoadPrefab("Common/RechargeScore", (obj) => { rechargeScoreObj = obj; });
            ToolScriptExtend.LoadPrefab("Common/itemObj", (obj) => { rewardObj = obj; });

            dailyDealMsg = new PushShopDailyDeal();
            dailyMustBuyMsg = new ShopDailyMustHaveLoadResp();
            weeklyDealMsg = new ShopWeekDealLoadResp();
            monthlyCardMsg = new ShopMonthCardLoadResp();
            weeklyCardMsg = new ShopWeekCardLoadResp();
            giftShopMsg = new ShopGiftPackMallLoadResp();
            growthFundMsg = new ShopDawnFundLoadResp();
            diamondShopMsg = new ShopDiamondLoadResp();
            NetEventDispatch.Instance.RegisterEvent((int)Protocol.MessageID.PushShopDailyDeal, message =>
            {
                var data = (PushShopDailyDeal)message;
                OnDailyDealChange(data);
            });

            fundConfigList = new List<shopping_foundation>();
            var data = GameEntry.LDLTable.GetTable<shopping_foundation>();
            if (data != null)
            {
                fundConfigList.AddRange(data);
            }

            BindRedDotLogic();
        }

        public void RequestInitMsg(Action callback = null)
        {
            var unlockList = GetUnlockList().Select(x => x.id).ToList();
            if (unlockList.Count > 0)
            {
                var sequence = DOTween.Sequence();
                foreach (var module in unlockList)
                {
                    sequence.AppendCallback(() =>
                    {
                        RequestNewMsg(module);
                    });
                    sequence.AppendInterval(0.05f);
                }

                sequence.OnComplete(() =>
                {
                    callback?.Invoke();
                });
            }
        }
        
        //判断商城入口是否解锁
        public bool IsMallUnlock()
        {
            if (!GameEntry.LDLTable.HaseTable<shopping_center>()) return false;
            var main = GameEntry.LDLTable.GetTable<shopping_center>();
            var config = main.FirstOrDefault(x => x.id == paymenttype.paymenttype_entrance);
            if (config == null) return false;
            return ToolScriptExtend.GetDemandUnlock(config.open_condition);
        }

        //获取已解锁标签的列表
        public List<shopping_center> GetUnlockList()
        {
            var resultList = new List<shopping_center>();
            if (GameEntry.LDLTable.HaseTable<shopping_center>())
            {
                var main = GameEntry.LDLTable.GetTable<Config.shopping_center>().Skip(1).ToList();
                for (var i = 0; i < main.Count; i++)
                {
                    var config = main[i];
                    if(IsUnlockCheckByServer((int)config.id)){
                        //特殊处理成长基金,如果奖励全部领取完了，就不再显示页签
                        if (config.id == paymenttype.paymenttype_dawnfund)
                        {
                            var isFinish = IsAllDawnFundRewardFinish();
                            if (isFinish)
                            {
                                continue;
                            }
                        }
                        resultList.Add(config);
                    }
                }
            }
            
            resultList.Sort((a, b) =>
            {
                var priorityA = GetFinishPriority(a.id,a.priority);
                var priorityB = GetFinishPriority(b.id,b.priority);
                return priorityA - priorityB;
            });
            return resultList;
        }

        private int GetFinishPriority(paymenttype type,int priority)
        {
            var value = priority;
            if (type == paymenttype.paymenttype_dailydeal)
            {
                if (IsDailyDealSoldOut())
                {
                    value = priority + 1000;
                }
            }
            else if (type == paymenttype.paymenttype_weeklydeal)
            {
                if (IsWeeklyDealSoldOut())
                {
                    value = priority + 1000;
                }
            }
            else if (type == paymenttype.paymenttype_giftpackmall)
            {
                if (IsGiftShopSoldOut())
                {
                    value = priority + 1000;
                }
            }
            return value;
        }

        //通过服务端的数据判断商店是否解锁
        public bool IsUnlockCheckByServer(int configId)
        {
            return unlockIdList.Any(x => x == configId);
        }

        //判断商城商店是否为空
        public bool IsEmptyMall()
        {
            return unlockIdList.Count == 0;
        }
        
        
        #region 红点逻辑
        
        public void BindRedDotLogic()
        {
            var rootFlag = EnumRed.Mall.ToString();
            //招募根节点
            RedPointManager.Instance.AddNodeTo(EnumRed.Root.ToString(), rootFlag);
            
            //每日特惠
            RedPointManager.Instance.AddNodeTo(rootFlag, EnumRed.Mall_DialyDeal.ToString(), () =>
            {
                var sum = 0;
                var isFree = GameEntry.LogicData.MallData.IsCanFree(paymenttype.paymenttype_dailydeal);
                if (isFree)
                {
                    sum++;
                } 
                return sum;
            });
            
            //每日必买
            RedPointManager.Instance.AddNodeTo(rootFlag, EnumRed.Mall_DailyBuy.ToString(), () =>
            {
                var redNum = CheckDailyBuyRedDot();
                return redNum;
            });
            
            //月卡
            RedPointManager.Instance.AddNodeTo(rootFlag, EnumRed.Mall_MonthlyCard.ToString(), () =>
            {
                var status = GetMonthlyCardStatus();
                return status == 1 ? 1 : 0;
            });
            
            //周卡
            RedPointManager.Instance.AddNodeTo(rootFlag, EnumRed.Mall_WeeklyCard.ToString(), () =>
            {
                var sum = 0;
                //免费奖励
                var isFree = IsCanFree(paymenttype.paymenttype_weeklycard);
                if (isFree)
                {
                    sum++;
                } 
                //可领取礼包奖励
                var list = GetWeeklyCardReceiveList();
                if (list.Count > 0)
                {
                    sum += list.Count;
                }
                return sum;
            });
            
            //黎明基金    
            RedPointManager.Instance.AddNodeTo(rootFlag, EnumRed.Mall_GrowthFund.ToString(), () =>
            {
                if (IsAllDawnFundRewardFinish())
                {
                    return 0;
                }
                
                var sum = 0;
                foreach (var config in fundConfigList)
                {
                    var target = config.foundation_target;
                    var freeStatus = GetGrowthFundRewardStatus(target, false);
                    var vipStatus = GetGrowthFundRewardStatus(target, true);
                    if (freeStatus == 1)
                    {
                        sum+=config.reward_free.Count;
                    }

                    if (vipStatus == 1)
                    {
                        sum+=config.reward_paid.Count;
                    }
                }
                return sum;
            });
        }
        #endregion

        #region 导表配置
        
        //读取礼包配置数据
        public Config.gift_pack GetGiftConfig(int giftId)
        {
            if (ToolScriptExtend.GetConfigById<Config.gift_pack>(giftId, out var data))
            {
                return data;
            }
            return null;
        }

        //读取充值配置数据
        public payment GetPaymentConfig(paymentid paymentId)
        {
            if (ToolScriptExtend.GetConfigById<payment>((int)paymentId, out var data))
            {
                return data;
            }
            return null;
        }

        //获取价格文本
        public string GetPrice(paymentid paymentId)
        {
            var data = GetPaymentConfig(paymentId);
            if (data != null)
            {
                return ToolScriptExtend.GetLang(data.price_lang_id);
            }
            return "";
        }
        
        /// <summary>
        /// 获取奖励列表
        /// </summary>
        /// <param name="giftId">gift_pack中的id</param>
        /// <param name="bindExtra">是否拼接Payment表中配置的钻石奖励和联盟宝箱奖励</param>
        public List<reward> GetRewardList(int giftId,bool bindExtra = true)
        {
            var result = new List<reward>();
            var data = GetGiftConfig(giftId);
            if (data != null)
            {
                if (bindExtra)
                {
                    var payData = GetPaymentConfig(data.payment_id);
                    if (payData != null && payData.diamond > 0)
                    {
                        result.Add(new reward(){item_id = itemid.itemid_6,num = payData.diamond});
                    }
                    result.AddRange(data.gift_pack_reward);
                    if (payData != null && payData.alliance_chest.Count > 0)
                    {
                        foreach (var chest in payData.alliance_chest)
                        {
                            result.Add(new Config.reward()
                            {
                                num = 1,
                                item_id = (itemid)chest
                            });
                        }
                    }
                }
                else
                {
                    result.AddRange(data.gift_pack_reward);
                }
            }
            return result;
        }
        
        #endregion
        
        #region 每日特惠
        //每日特惠变更推送(新增或修改)回调
        private void OnDailyDealChange(PushShopDailyDeal resp)
        {
            if (resp.Gifts.Count <= 0)
            {
                Debug.Log("推送数据为空！");
                return;
            }
            ProtoLog(false, "每日特惠数据变更推送(新增或修改)协议", resp.ToString());
            dailyDealMsg = resp;

            var form = GameEntry.UI.GetUIForm(EnumUIForm.UIMallForm);
            if (form != null)
            {
                GameEntry.UI.RefreshUIForm(EnumUIForm.UIMallForm,("DailyDeal",1));
            }
            
            RedPointManager.Instance.Dirty(EnumRed.Mall_DialyDeal.ToString());
            GameEntry.Event.Fire(MallChangeEventArgs.EventId, MallChangeEventArgs.Create());
        }
        
        /// <summary>
        /// 每日特惠信息请求
        /// </summary>
        /// <param name="id">商品id,对应 store_type 表中的id; 0为全部商店</param>
        /// <param name="action">成功响应回调</param>
        public void C2SDailyDiscountInfo(UnityAction<ShopDailyDealLoadResp> callback = null)
        {
            var req = new ShopDailyDealLoadReq();
            ProtoLog(true, "每日特惠信息", req.ToString());
            GameEntry.LDLNet.Send(Protocol.MessageID.ShopDailyDealLoad, req, (message) =>
            {
                var resp = (ShopDailyDealLoadResp)message;
                ProtoLog(false, "每日特惠信息", resp.ToString());
                callback?.Invoke(resp);
            });
        }

        /// <summary>
        /// 每日特惠切换信息请求
        /// </summary>
        /// <param name="heroId">英雄类型,0:默认历史,对应shopping_dailydeal.xml中的heroId</param>
        /// <param name="callback"></param>
        public void C2SDailyDiscountSwitch(int heroId, UnityAction<ShopDailyDealSwitchResp> callback = null)
        {
            var req = new ShopDailyDealSwitchReq()
            {
                Id = heroId
            };
            ProtoLog(true, "每日特惠切换信息", req.ToString());
            GameEntry.LDLNet.Send(Protocol.MessageID.ShopDailyDealSwitch, req, (message) =>
            {
                var resp = (ShopDailyDealSwitchResp)message;
                ProtoLog(false, "每日特惠切换信息", resp.ToString());
                callback?.Invoke(resp);
            });
        }

        /// <summary>
        /// 每日特惠免费礼包领取请求
        /// </summary>
        /// <param name="callback"></param>
        public void C2SDailyDiscountFreeReceive(UnityAction<ShopDailyDealClaimFreeResp> callback = null)
        {
            var req = new ShopDailyDealClaimFreeReq();
            ProtoLog(true, "每日特惠免费礼包领取", req.ToString());
            GameEntry.LDLNet.Send(Protocol.MessageID.ShopDailyDealClaimFree, req, (message) =>
            {
                var resp = (ShopDailyDealClaimFreeResp)message;
                ProtoLog(false, "每日特惠免费礼包领取", resp.ToString());
                callback?.Invoke(resp);
                ShowRewardGet(resp.Rewards.ToList(),false);
            });
        }

        //每日特惠是否可领取免费礼包
        public bool IsDailyDiscountFree()
        {
            return false;
        }
        
        //读取shopping_dailydeal中的配置数据
        public shopping_dailydeal GetDailyDealConfig(int id)
        {
            if (ToolScriptExtend.GetConfigById<shopping_dailydeal>(id, out var data))
            {
                return data;
            }
            return null;
        }

        /// <summary>
        /// 获取当前每日特惠Id
        /// </summary>
        /// <returns></returns>
        public int GetCurDailyDealId()
        {
            var giftId = dailyDealMsg.Id;
            return giftId;
        }
        
        public int GetDailyDealEndTime()
        {
            return (int)dailyDealMsg.EndAt;
        }
        
        //判断今天的特惠礼包是否购买锁定
        public bool IsDailyDealLocked()
        {
            return dailyDealMsg?.IsLocked ?? false;
        }
        
        //判断今天的特惠礼包是否都已经卖完了
        public bool IsDailyDealSoldOut()
        {
            if (dailyDealMsg == null) return false;
            return IsSoldOut(dailyDealMsg.Gifts.ToList());
        }
        
        #endregion

        #region 每日必买

        /// <summary>
        /// 每日必买信息请求
        /// </summary>
        /// <param name="callback"></param>
        public void C2SDailyMustBuyInfo(UnityAction<ShopDailyMustHaveLoadResp> callback = null)
        {
            var req = new ShopDailyMustHaveLoadReq();
            ProtoLog(true, "每日必买信息", req.ToString());
            GameEntry.LDLNet.Send(Protocol.MessageID.ShopDailyMustHaveLoad, req, (message) =>
            {
                var resp = (ShopDailyMustHaveLoadResp)message;
                dailyMustBuyMsg = resp;
                ProtoLog(false, "每日必买信息", resp.ToString());
                callback?.Invoke(resp);
                RedPointManager.Instance.Dirty(EnumRed.Mall_DailyBuy.ToString());
                GameEntry.Event.Fire(MallChangeEventArgs.EventId, MallChangeEventArgs.Create());
            });
        }
        
        /// <summary>
        /// 每日必买钻石领取请求
        /// </summary>
        /// <param name="callback"></param>
        public void C2SDailyMustBuyReceive(UnityAction<ShopDailyMustHaveClaimResp> callback = null)
        {
            var req = new ShopDailyMustHaveClaimReq();
            ProtoLog(true, "每日必买钻石领取", req.ToString());
            GameEntry.LDLNet.Send(Protocol.MessageID.ShopDailyMustHaveClaim, req, (message) =>
            {
                var resp = (ShopDailyMustHaveClaimResp)message;
                ShowRewardGet(resp.Rewards.ToList(),true);
                ProtoLog(false, "每日必买钻石领取", resp.ToString());
                RedPointManager.Instance.Dirty(EnumRed.Mall_DailyBuy.ToString());
                callback?.Invoke(resp);
                GameEntry.Event.Fire(MallChangeEventArgs.EventId, MallChangeEventArgs.Create());
            });
        }

        //还未兑现的值
        public int GetDailyBuyExp()
        {
            return (int)(dailyMustBuyMsg?.RemainProcess ?? 0);
        }

        //已领取节点值  0、10、50、100、200、300
        public int GetDailyBuyProgressValue()
        {
            return (int)(dailyMustBuyMsg?.ClaimedProcess ?? 0);
        }


        private int CheckDailyBuyRedDot()
        {
            if (!ToolScriptExtend.GetConfigById<shopping_settings>(102002, out var progressConfig)) return 0;
            var compareList = new List<int>();
            var list = progressConfig.value;
            for (var i = 0; i < list.Count; i++)
            {
                var data = list[i].Split("|");
                compareList.Add(int.Parse(data[0]));
            }
            var exp = GetDailyBuyExp();
            var progressValue = GetDailyBuyProgressValue();
            var maxValue = compareList.LastOrDefault();
            var sumValue = progressValue + exp;

            var num = 0;
            for (var i = 0; i < compareList.Count; i++)
            {
                var compareValue = compareList[i];
                if (sumValue <= maxValue)
                {
                    if (sumValue >= compareValue && progressValue < compareValue)
                    {
                        num++;
                    }
                }
                else
                {
                    if (progressValue < compareValue)
                    {
                        num++;
                    }
                }
            }
            return num;
        }
        
        #endregion
        
        #region 每周特惠

        /// <summary>
        /// 每周特惠信息请求
        /// </summary>
        /// <param name="callback"></param>
        public void C2SWeeklyDiscountInfo(UnityAction<ShopWeekDealLoadResp> callback = null)
        {
            var req = new ShopWeekDealLoadReq();
            ProtoLog(true, "每周特惠信息", req.ToString());
            GameEntry.LDLNet.Send(Protocol.MessageID.ShopWeekDealLoad, req, (message) =>
            {
                var resp = (ShopWeekDealLoadResp)message;
                weeklyDealMsg = resp;
                ProtoLog(false, "每周特惠信息", resp.ToString());
                callback?.Invoke(resp);
                GameEntry.Event.Fire(MallChangeEventArgs.EventId, MallChangeEventArgs.Create());
            });
        }
        
        //每周特惠中的所有礼包是否都售罄
        public bool IsWeeklyDealSoldOut()
        {
            if (weeklyDealMsg == null) return false;
            return IsSoldOut(weeklyDealMsg.Gifts.ToList());
        }

        #endregion

        #region 超级月卡

        /// <summary>
        /// 月卡信息请求
        /// </summary>
        /// <param name="callback"></param>
        public void C2SMonthlyCardInfo(UnityAction<ShopMonthCardLoadResp> callback = null)
        {
            var req = new ShopMonthCardLoadReq();
            ProtoLog(true, "超级月卡信息", req.ToString());
            GameEntry.LDLNet.Send(Protocol.MessageID.ShopMonthCardLoad, req, (message) =>
            {
                var resp = (ShopMonthCardLoadResp)message;
                monthlyCardMsg = resp;
                ProtoLog(false, "超级月卡信息", resp.ToString());
                callback?.Invoke(resp);
                RedPointManager.Instance.Dirty(EnumRed.Mall_MonthlyCard.ToString());
                CheckShopBubble();
                GameEntry.Event.Fire(MallChangeEventArgs.EventId, MallChangeEventArgs.Create());
            });
        }
        
        /// <summary>
        /// 月卡每日领取奖励请求
        /// </summary>
        /// <param name="callback"></param>
        public void C2SMonthlyCardDailyReceive(UnityAction<ShopMonthCardClaimResp> callback = null)
        {
            var req = new ShopMonthCardClaimReq();
            ProtoLog(true, "月卡每日领取奖励", req.ToString());
            GameEntry.LDLNet.Send(Protocol.MessageID.ShopMonthCardClaim, req, (message) =>
            {
                var resp = (ShopMonthCardClaimResp)message;
                ShowRewardGet(resp.Rewards.ToList(),true);
                ProtoLog(false, "月卡每日领取奖励", resp.ToString());
                callback?.Invoke(resp);
                CheckShopBubble();
                GameEntry.Event.Fire(MallChangeEventArgs.EventId, MallChangeEventArgs.Create());
            });
        }

        //月卡是否已激活
        public bool IsMonthlyCardActive()
        {
            var endTime = monthlyCardMsg?.EndAt ?? 0;
            var now = (long)TimeComponent.Now;
            return endTime > now;
        }
        
        //月卡车队等级
        public int GetMonthlyCardLevel()
        {
            if (!IsMonthlyCardActive()) return 1;
            return monthlyCardMsg?.TeamFourLevel ?? 1;
        }
        
        //月卡剩余天数
        public int GetMonthlyCardDays()
        {
            if (!IsMonthlyCardActive()) return 0;
            if (monthlyCardMsg == null) return 0;
            var time = (ulong)monthlyCardMsg.EndAt - TimeComponent.Now;
            if (time <= 0) return 0;
            TimeSpan span = new TimeSpan(0,0,(int)time);
            return span.Days;
        }
        
        /// <summary>
        /// 获取月卡状态
        /// </summary>
        /// <returns>状态 :0:未激活  1:已激活未领取当天奖励  2:已激活已领领取当天奖励</returns>
        public int GetMonthlyCardStatus()
        {
            if (monthlyCardMsg == null) return 0;
            var isActive = IsMonthlyCardActive();
            if (isActive)
            {
                return monthlyCardMsg.IsClaimed ? 2 : 1;
            }
            else
            {
                return 0;
            }
        }
        
        //月卡配表id
        public int GetMonthlyCardConfigId()
        {
            if (GameEntry.LDLTable.HaseTable<shopping_monthycard_weeklycard>())
            {
                var data = GameEntry.LDLTable.GetTable<Config.shopping_monthycard_weeklycard>(); 
                var config = data.FirstOrDefault(x => x.card_type == paymenttype.paymenttype_monthycard);
                return config?.card_id??0;
            }
            return 0;
        }
        
        //获取月卡截止日期
        public int GetMonthlyCardEndTime()
        {
            if (!IsMonthlyCardActive()) return 0;
            if (monthlyCardMsg == null) return 0;
            return (int)(monthlyCardMsg?.EndAt ?? 0);
        }
        #endregion

        #region 周卡

        /// <summary>
        /// 周卡信息请求
        /// </summary>
        /// <param name="callback"></param>
        public void C2SWeeklyCardInfo(UnityAction<ShopWeekCardLoadResp> callback = null)
        {
            var req = new ShopWeekCardLoadReq();
            ProtoLog(true, "周卡信息", req.ToString());
            GameEntry.LDLNet.Send(Protocol.MessageID.ShopWeekCardLoad, req, (message) =>
            {
                var resp = (ShopWeekCardLoadResp)message;
                weeklyCardMsg = resp;
                ProtoLog(false, "周卡信息", resp.ToString());
                callback?.Invoke(resp);
                RedPointManager.Instance.Dirty(EnumRed.Mall_WeeklyCard.ToString());
                CheckShopBubble();
                GameEntry.Event.Fire(MallChangeEventArgs.EventId, MallChangeEventArgs.Create());
            });
        }

        /// <summary>
        /// 周卡免费物品领取请求
        /// </summary>
        /// <param name="callback"></param>
        public void C2SWeeklyCardFreeReceive(UnityAction<ShopWeekCardClaimFreeResp> callback = null)
        {
            var req = new ShopWeekCardClaimFreeReq();
            ProtoLog(true, "周卡免费物品领取", req.ToString());
            GameEntry.LDLNet.Send(Protocol.MessageID.ShopWeekCardClaimFree, req, (message) =>
            {
                var resp = (ShopWeekCardClaimFreeResp)message;
                ProtoLog(false, "周卡免费物品领取", resp.ToString());
                ShowRewardGet(resp.Rewards.ToList(),false);
                callback?.Invoke(resp);
                CheckShopBubble();
                GameEntry.Event.Fire(MallChangeEventArgs.EventId, MallChangeEventArgs.Create());
            });
        }
        
        /// <summary>
        /// 周卡每天领取请求
        /// </summary>
        /// <param name="id">礼包id,对应 shopping_monthycard_weeklycard 表中的id   0:就是一键领取所有</param>
        /// <param name="callback"></param>
        public void C2SWeeklyCardReceivedDaily(int id,UnityAction<ShopWeekCardClaimTodayResp> callback = null)
        {
            var req = new ShopWeekCardClaimTodayReq()
            {
                Id = id
            };
            ProtoLog(true, "周卡每天领取", req.ToString());
            GameEntry.LDLNet.Send(Protocol.MessageID.ShopWeekCardClaimToday, req, (message) =>
            {
                var resp = (ShopWeekCardClaimTodayResp)message;
                ShowRewardGet(resp.Rewards.ToList(),true);
                ProtoLog(false, "周卡每天领取", resp.ToString());
                callback?.Invoke(resp);
                CheckShopBubble();
                GameEntry.Event.Fire(MallChangeEventArgs.EventId, MallChangeEventArgs.Create());
            });
        }

        private shopping_monthycard_weeklycard GetWeeklyCardConfigById(int giftId)
        {
            if (ToolScriptExtend.GetTable<shopping_monthycard_weeklycard>(out var data))
            {
                var config = data.FirstOrDefault(x => x.card_id == giftId);
                if (config != null)
                {
                    return config;
                }
            }
            return null;
        }
        
        //获取周卡礼包列表
        private List<ShopWeekCardGift> GetWeeklyCardGiftList()
        {
            var list = new List<ShopWeekCardGift>();
            if (weeklyCardMsg.Gifts != null)
            {
                list.AddRange(FilterNullWeeklyCardGift(weeklyCardMsg.Gifts));
            }
            return list;
        }
        
        //获取周卡礼包列表
        public List<ShopWeekCardGift> GetWeeklyCardGiftSortList()
        {
            var list = GetWeeklyCardGiftList();
            if (ToolScriptExtend.GetTable<shopping_monthycard_weeklycard>(out var data))
            {
                list.Sort((a, b) =>
                {
                    var statusA = CheckSingleWeeklyCardGiftStatus(a);
                    var statusB = CheckSingleWeeklyCardGiftStatus(b);
                    var flagA = GetCompareFlag(statusA);
                    var flagB = GetCompareFlag(statusB);
                    if(flagA != flagB)return flagA - flagB;
                    var configA = data.FirstOrDefault(x => x.card_id == a.Id);
                    var configB = data.FirstOrDefault(x => x.card_id == b.Id);
                    return configA.priority - configB.priority;
                });
            }
            return list;
        }
        
        //礼包判空筛选
        public List<ShopWeekCardGift> FilterNullWeeklyCardGift(RepeatedField<ShopWeekCardGift> source)
        {
            var list = new List<ShopWeekCardGift>();

            if (ToolScriptExtend.GetTable<shopping_monthycard_weeklycard>(out var data))
            {
                foreach (var gift in source)
                {
                    var config = data.FirstOrDefault(x => x.card_id == gift.Id);
                    if (config != null)
                    {
                        list.Add(gift); 
                    }
                }
            }
            return list;
        }
        
        // 已购买未领取(1)>>已购买已领取(2)>>未购买(3)
        private int GetCompareFlag(int status)
        {
            var flag = 3;
            // 状态 :0:未购买  1:已购买未领取  2:已购买已领取
            if (status == 0)
            {
                flag = 3;
            }
            else if (status == 1)
            {
                flag = 1;
            }
            else if (status == 2)
            {
                flag = 2;
            }
            return flag;
        }

        // 状态 :0:未购买  1:已购买未领取  2:已购买已领取
        public int CheckSingleWeeklyCardGiftStatus(ShopWeekCardGift gift)
        {
            var isEnd = TimeComponent.IsEnd(gift.EndAt);
            if (!isEnd)
            {
                return gift.IsClaimedToday ? 2 : 1;
            }
            return 0;
        }
        
        /// <summary>
        /// 获取周卡礼包状态
        /// </summary>
        /// <param name="giftId">礼包id,对应 shopping_monthycard_weeklycard 表中的id</param>
        /// <returns>状态 :0:未购买  1:已购买未领取  2:已购买已领取</returns>
        public int GetWeeklyCardGiftStatus(int giftId)
        {
            var list = GetWeeklyCardGiftList();
            foreach (var info in list)
            {
                if (info.Id != giftId) continue;
                var isEnd = TimeComponent.IsEnd(info.EndAt);
                if (!isEnd)
                {
                    return info.IsClaimedToday ? 2 : 1;
                }
            }
            return 0;
        }
        
        //获取周卡礼包中已购买未领取的礼包id列表
        public List<int> GetWeeklyCardReceiveList()
        {
            var list = GetWeeklyCardGiftList();
            var idList = list.Where(x => GetWeeklyCardGiftStatus(x.Id) == 1)
                .Select(x => x.Id).ToList();
            return idList;
        }
        
        /*判断周卡合购按钮的状态
         *0：一个礼包都没购买
         * 1：有礼包购买，当天奖励奖励有未领取的
         * 2:有礼包购买，当天奖励可领取奖励都已领取
         */
        public int GetWeeklyCardSumBtnStatus()
        {
            var list = GetWeeklyCardGiftList();
            var noBuyCount = 0;
            var dailyFinishCount = 0;
            foreach (var info in list)
            {
                var status = GetWeeklyCardGiftStatus(info.Id);
                // 0:未购买  1:已购买未领取  2:已购买已领取
                if (status == 1)
                {
                    return 1;
                }
                else if (status == 0)
                {
                    noBuyCount++;
                }
                else if (status == 2)
                {
                    dailyFinishCount++;
                }
            }

            if (noBuyCount == list.Count)
            {
                return 0;
            }
            return 2;
        }
        
        //获取指定周卡礼包的截止日期
        public int GetWeeklyCardGiftEndTime(int giftId)
        {
            var list = GetWeeklyCardGiftList();
            foreach (var info in list)
            {
                if (info.Id == giftId)
                {

                    return (int)(info?.EndAt ?? 0);
                }
            }
            return 0;
        }
        
        #endregion

        #region 礼包商城

        /// <summary>
        /// 礼包商城信息请求
        /// </summary>
        /// <param name="callback"></param>
        public void C2SSupermarketInfo(UnityAction<ShopGiftPackMallLoadResp> callback = null)
        {
            var req = new ShopGiftPackMallLoadReq();
            ProtoLog(true, "礼包商城", req.ToString());
            GameEntry.LDLNet.Send(Protocol.MessageID.ShopGiftPackMallLoad, req, (message) =>
            {
                var resp = (ShopGiftPackMallLoadResp)message;
                giftShopMsg = resp;
                ProtoLog(false, "礼包商城", resp.ToString());
                // var temp = resp.GiftList.Select(x => x.Code);
                // Debug.Log(string.Join(",",temp));
                callback?.Invoke(resp);
                GameEntry.Event.Fire(MallChangeEventArgs.EventId, MallChangeEventArgs.Create());
            });
        }
        
        //礼包商城中的所有礼包是否都售罄
        public bool IsGiftShopSoldOut()
        {
            if (giftShopMsg == null) return false;
            return IsSoldOut(giftShopMsg.Gifts.ToList());
        }
        
        #endregion

        #region 成长基金(黎明基金)
        
        /// <summary>
        /// 黎明基金信息请求
        /// </summary>
        /// <param name="callback"></param>
        public void C2SDawnfundInfo(UnityAction<ShopDawnFundLoadResp> callback = null)
        {
            var req = new ShopDawnFundLoadReq();
            ProtoLog(true, "黎明基金", req.ToString());
            GameEntry.LDLNet.Send(Protocol.MessageID.ShopDawnFundLoad, req, (message) =>
            {
                var resp = (ShopDawnFundLoadResp)message;
                growthFundMsg = resp;
                ProtoLog(false, "黎明基金", resp.ToString());
                callback?.Invoke(resp);
                RedPointManager.Instance.Dirty(EnumRed.Mall_GrowthFund.ToString());
                GameEntry.Event.Fire(MallChangeEventArgs.EventId, MallChangeEventArgs.Create());
            });
        }
        
        /// <summary>
        /// 黎明基金领取请求
        /// </summary>
        /// <param name="callback"></param>
        public void C2SDawnfundReceive(UnityAction<ShopDawnFundClaimResp> callback = null)
        {
            var req = new ShopDawnFundClaimReq();
            ProtoLog(true, "黎明基金领取", req.ToString());
            GameEntry.LDLNet.Send(Protocol.MessageID.ShopDawnFundClaim, req, (message) =>
            {
                var resp = (ShopDawnFundClaimResp)message;
                ShowRewardGet(resp.Rewards.ToList(),true);
                ProtoLog(false, "黎明基金领取", resp.ToString());
                callback?.Invoke(resp);
                GameEntry.Event.Fire(MallChangeEventArgs.EventId, MallChangeEventArgs.Create());
            });
        }

        //获取黎明基金相关的建筑的等级
        public int GetDawnfundLevel()
        {
            return growthFundMsg?.BuildLevel ?? 1;
        }
        
        //获取成长基金奖励状态
        //status:0:未解锁  1：已解锁未领取  2：已解锁已领取
        public int GetGrowthFundRewardStatus(int targetLevel, bool isVip)
        {
            if (growthFundMsg == null) return 0;
            //当前建筑等级
            var level = GetDawnfundLevel();
            var isUnlock = false;
            if (isVip)
            {
                isUnlock = growthFundMsg.IsPurchased;
            }
            else
            {
                isUnlock = targetLevel <= level;
            }
            var progress = isVip ? growthFundMsg.AdvanceProgress : growthFundMsg.FreeProgress;
            if (isUnlock)
            {
                if (targetLevel > level)
                {
                    return 0;
                }
                else
                {
                    if (progress == 0)
                    {
                        return 1;
                    }
                    if (ToolScriptExtend.GetConfigById<shopping_foundation>(progress, out var config))
                    {
                        return targetLevel > config.foundation_target ? 1 : 2;
                    }
                }
            }
            return 0;
        }
        
        //获取黎明基金vip是否被解锁
        public bool IsDawnfundVipActive()
        {
            return growthFundMsg != null && growthFundMsg.IsPurchased;
        }

        //判断是否可领取黎明基金奖励
        public bool IsCanGetDawnFundReward()
        {
            var data = GameEntry.LDLTable.GetTable<Config.shopping_foundation>();
            foreach (var config in data)
            {
                var target = config.foundation_target;
                var freeStatus = GetGrowthFundRewardStatus(target, false);
                var vipStatus = GetGrowthFundRewardStatus(target, true);
                if (freeStatus == 1 || vipStatus == 1)
                {
                    return true;
                }
            }
            return false;
        }
        
        //判断成长基金奖励是否都领取完
        public bool IsAllDawnFundRewardFinish()
        {
            if (!GameEntry.LDLTable.HaseTable<shopping_foundation>()) return false;
            var data = GameEntry.LDLTable.GetTable<shopping_foundation>();
            if (data != null) return false;
            var lastData = data.LastOrDefault();
            if (lastData == null) return false;
            
            var target = lastData.foundation_target;
            var freeStatus = GetGrowthFundRewardStatus(target, false);
            var vipStatus = GetGrowthFundRewardStatus(target, true);
            return freeStatus == 2 && vipStatus == 2;
        }
        
        #endregion

        #region 钻石直购

        /// <summary>
        /// 钻石直购信息请求
        /// </summary>
        /// <param name="callback"></param>
        public void C2SDiamondInfo(UnityAction<ShopDiamondLoadResp> callback = null)
        {
            var req = new ShopDiamondLoadReq();
            ProtoLog(true, "钻石商城信息", req.ToString());
            GameEntry.LDLNet.Send(Protocol.MessageID.ShopDiamondLoad, req, (message) =>
            {
                var resp = (ShopDiamondLoadResp)message;
                diamondShopMsg = resp;
                ProtoLog(false, "钻石商城信息", resp.ToString());
                callback?.Invoke(resp);
                GameEntry.Event.Fire(MallChangeEventArgs.EventId, MallChangeEventArgs.Create());
            });
        }
        
        //获取钻石直购礼包列表
        public List<Gift.Gift> GetDiamondGift()
        {
            var list = new List<Gift.Gift>();
            if (diamondShopMsg == null)
            {
                return list;
            }

            foreach (var gift in diamondShopMsg.Gifts)
            {
                if (GetGiftConfig(gift.Id) != null)
                {
                    list.Add(gift);
                }
            }
            list.Sort((a, b) =>
            {
                var configA = GetGiftConfig(a.Id);
                var configB = GetGiftConfig(b.Id);
                return configB.priority - configA.priority;
            });
            return list;
        }

        //是否可首购双倍返还
        public bool IsDouble(int giftId)
        {
            if (diamondShopMsg.GiftId.Count == 0) return true;
            foreach (var id in diamondShopMsg.GiftId)
            {
                if (giftId == id)
                {
                    return false;
                }
            }
            return true;
        }
        #endregion

        #region 礼包获取途径信息请求
        /// <summary>
        /// 礼包获取途径信息请求
        /// </summary>
        /// <param name="giftId">需要检测的礼包id</param>
        /// <param name="callback"></param>
        public void C2SGiftGetWay(int giftId,UnityAction<int> callback = null)
        {
            var req = new ShopGetWayReq
            {
                Id = giftId
            };
            ProtoLog(true, "礼包获取途径信息", req.ToString());
            GameEntry.LDLNet.Send(Protocol.MessageID.ShopGetWay, req, (message) =>
            {
                var resp = (ShopGetWayResp)message;
                ProtoLog(false, "礼包获取途径信息", resp.ToString());
                //礼包id,对应 gift_pack 表中的id,为0时表示没有礼包,或者表示礼包无法购买
                var resultId = resp?.Id ?? 0;
                callback?.Invoke(resultId);
            });
        }
        
        #endregion
        
        #region 协议

        /// <summary>
        /// 商城加载  从后端获取哪些商城解锁
        /// </summary>
        /// <param name="callback"></param>
        public void C2SShopLoadReq(UnityAction<ShopLoadResp> callback = null)
        {
            var req = new ShopLoadReq();
            ProtoLog(true, "商城解锁列表", req.ToString());
            GameEntry.LDLNet.Send(Protocol.MessageID.ShopLoad, req, (message) =>
            {
                var resp = (ShopLoadResp)message;
                ProtoLog(false, "商城解锁列表", resp.ToString());
                unlockIdList.Clear();
                unlockIdList.AddRange(resp.Id);
                callback?.Invoke(resp);
            });
        }

        //协议返回数据打印
        private void ProtoLog(bool isRequest, string protoName, string protoDetail = null)
        {
            ColorLog.ProtoLog(isRequest,protoName,protoDetail);
        }

        #endregion


        /// <summary>
        /// 获取礼包列表
        /// </summary>
        /// <param name="type"></param>
        /// <returns></returns>
        /// <exception cref="ArgumentOutOfRangeException"></exception>
        public List<Gift.Gift> GetGiftList(Config.paymenttype type)
        {
            var list = new List<Gift.Gift>();
            switch (type)
            {
                case paymenttype.paymenttype_dailydeal:
                    if (dailyDealMsg != null && dailyDealMsg.Gifts != null)
                    {
                        list.AddRange(FilterNullGift(dailyDealMsg.Gifts));
                        return SortGiftByPriority(list);
                    }
                    break;
                case paymenttype.paymenttype_dailymusthave:
                    if (dailyMustBuyMsg != null && dailyMustBuyMsg.Gifts != null)
                    {
                        list.AddRange(FilterNullGift(dailyMustBuyMsg.Gifts));
                        list.Sort(SortGiftByPriorityAndSoldOut);
                        return list;
                    }
                    break;
                case paymenttype.paymenttype_weeklydeal:
                    if (weeklyDealMsg != null && weeklyDealMsg.Gifts != null)
                    {
                        list.AddRange(FilterNullGift(weeklyDealMsg.Gifts));
                        list.Sort(SortGiftByPriorityAndSoldOut);
                        return list;
                    }
                    break;
                case paymenttype.paymenttype_giftpackmall:
                    if (giftShopMsg != null && giftShopMsg.Gifts != null)
                    {
                        list.AddRange(FilterNullGift(giftShopMsg.Gifts));
                        list.Sort(SortGiftByPriorityAndSoldOut);
                        return list;
                    }
                    break;
            }
            
            return list;
        }

        public List<Gift.Gift> SortGiftByPriority(List<Gift.Gift> giftList)
        {
            var result = from gift in giftList
                let config = GetGiftConfig(gift.Id)
                where config != null
                orderby config.priority
                select gift;
            return result.ToList();
        }
        
        public int SortGiftByPriorityAndSoldOut(Gift.Gift a, Gift.Gift b)
        {
            var configA = GetGiftConfig(a.Id);
            var configB = GetGiftConfig(b.Id);
            var priorityA = configA.priority;
            var priorityB = configB.priority;
            var isSoldOutA = a.Amount >= configA.buy_limit_times ? 0:1;
            var isSoldOutB = b.Amount >= configB.buy_limit_times ? 0:1;
            if (isSoldOutA != isSoldOutB) return isSoldOutB - isSoldOutA;
            if (priorityA != priorityB) return priorityA - priorityB;
            return a.Id - b.Id;
        }
        
        //礼包判空筛选
        public List<Gift.Gift> FilterNullGift(RepeatedField<Gift.Gift> source)
        {
            var list = new List<Gift.Gift>();
            foreach (var gift in source)
            {
                var config = GetGiftConfig(gift.Id);
                if (config != null)
                {
                    list.Add(gift); 
                }
            }
            return list;
        }
        
        /// <summary>
        /// 创建充值积分标签
        /// </summary>
        /// <param name="payId">充值表id</param>
        /// <param name="node">设置生成标签的父节点</param>
        /// <param name="offsetX">x轴偏移,一般为正数</param>
        /// <param name="offsetY">y轴偏移，一般为负数</param>
        public void CreateRechargeScore(paymentid payId, Transform node,float offsetX = 40,float offsetY = -20)
        {
            //如果累充活动未开启，则不显示累充积分标签
            if (!GameEntry.LogicData.ChaoZhiData.IsRechargeActivityUnlock())
            {   
                var score = node.Find("rechargeScoreObj");
                if (score != null)
                {
                    score.gameObject.SetActive(false);
                }
                return;
            }
            
            if (rechargeScoreObj == null) return;
            var data = GetPaymentConfig(payId);
            if (data == null) return;
            var root = node.Find("rechargeScoreObj");
            if (root == null)
            {
                var obj = GameObject.Instantiate(rechargeScoreObj, node);
                obj.name = "rechargeScoreObj";
                root = obj.transform;
            }
            
            var script = root.GetComponent<RechargeScore>();
            if (script != null)
            {
                script.SetScoreInfo((int)data.recharge_score);
            }
            
            var rect = root.GetComponent<RectTransform>();
            rect.anchoredPosition = new Vector2(offsetX, offsetY);
        }
        
        /// <summary>
        /// 设置通用奖励信息
        /// </summary>
        /// <param name="root">itemObj生成的父节点</param>
        /// <param name="obj">itemObj实例对象</param>
        /// <param name="id">道具id</param>
        /// <param name="count">道具数量</param>
        /// <param name="txtScale">itemObj中的文本scale</param>
        /// <param name="callback">点击自定义函数</param>
        public void SetRewardInfo(Transform root,GameObject obj,itemid id,int count,float txtScale = 1,Action<UIItemModule> callback = null)
        {
            ToolScriptExtend.SetItemObjInfo(root,obj,id,count,txtScale);
        }
        
        //重复利用已经实例化的item对象
        //数量少时进行实例化，数量多的部分进行隐藏
        public void RecycleOrCreate(GameObject template, Transform root, int needCount)
        {
            ToolScriptExtend.RecycleOrCreate(template,root,needCount);
        }
        
        //判断是否已售罄
        public bool IsSoldOut(List<Gift.Gift> list)
        {
            if (list == null) return false;
            foreach (var gift in list)
            {
                if (gift.Amount < gift.MaxAmount)
                {
                    return false;
                }
            }
            return true;
        }
        
        //判断是否可以免费领取
        public bool IsCanFree(paymenttype type)
        {
            switch (type)
            {
                case paymenttype.paymenttype_dailydeal:
                    return !dailyDealMsg.IsClaimedFree;
                case paymenttype.paymenttype_weeklycard:
                    return !weeklyCardMsg.IsClaimedFree;
            }
            return false;
        }
        
        //获取商城购买开放时间
        public int GetShoppingTime(paymenttype type)
        {
            long record = 0;
            switch (type)
            {
                case paymenttype.paymenttype_dailydeal:
                    record = dailyDealMsg.EndAt;
                    break;
                case paymenttype.paymenttype_dailymusthave:
                    record = dailyMustBuyMsg.EndAt;
                    break;
                case paymenttype.paymenttype_weeklydeal:
                    record = weeklyDealMsg.EndAt;
                    break;
            }
            
            var time = (ulong)record - TimeComponent.Now;
            return time <= 0 ? 0 : (int)time;
        }
        
        //通用展示获取奖励界面
        //isSpecial:特殊处理，标题显示“领取成功”
        public void ShowRewardGet( List<Article.Article> rewardList,bool isSpecial)
        {
            ToolScriptExtend.DisplayRewardGet(rewardList,isSpecial);
        }
        
        //请求新的信息，更新界面
        public void RequestNewMsg(paymenttype type,UnityAction updateLogic = null)
        {
            switch(type)
            {
                case paymenttype.paymenttype_dailydeal:
                    C2SDailyDiscountInfo(resp => { updateLogic?.Invoke();});
                    break;
                case paymenttype.paymenttype_dailymusthave:
                    C2SDailyMustBuyInfo(resp => { updateLogic?.Invoke();});
                    break;
                case paymenttype.paymenttype_weeklydeal:
                    C2SWeeklyDiscountInfo(resp => { updateLogic?.Invoke();});
                    break;
                case paymenttype.paymenttype_monthycard:
                    C2SMonthlyCardInfo(resp => { updateLogic?.Invoke();});
                    break;
                case paymenttype.paymenttype_weeklycard:
                    C2SWeeklyCardInfo(resp => { updateLogic?.Invoke();});
                    break;
                case paymenttype.paymenttype_giftpackmall:
                    C2SSupermarketInfo(resp => { updateLogic?.Invoke();});
                    break;
                case paymenttype.paymenttype_dawnfund:
                    C2SDawnfundInfo(resp => { updateLogic?.Invoke();});
                    break;
                case paymenttype.paymenttype_diamondshop:
                    C2SDiamondInfo(resp => { updateLogic?.Invoke();});
                    break;
                default:
                    break;
            }
        }
        
        //更新特效层级
        public void SetParticleDepth(GameObject parentObj)
        {
            var form = GameEntry.UI.GetUIForm(EnumUIForm.UIMallForm);
            if (form != null)
            {
                ToolScriptExtend.SetParticleSystemSortingOrder(parentObj, form.Depth);
            }
        }
        
        //更新Canvas特效层级
        public void SetParticleCanvasDepth(GameObject obj,int offset = 0)
        {
            var form = GameEntry.UI.GetUIForm(EnumUIForm.UIMallForm);
            if (form != null)
            { 
                var canvas = obj.GetComponent<Canvas>();
                if (canvas != null)
                {
                    canvas.sortingOrder = form.Depth+offset;
                }
            }
        }

        //初始化判断商店气泡是否显示
        public bool InitShopBubbleShow()
        {
            string checkKey = ToolScriptExtend.GetRolePlayerPrefsKeyById("CheckShopToday");
            bool isTodayCheck = ToolScriptExtend.GetCommonTipsIsTodayCheckBox(checkKey);
            if (!isTodayCheck)
            {
                return true;
            }
            
            var mallManager = GameEntry.LogicData.MallData;
            //商城是否解锁
            if (!mallManager.IsMallUnlock()) return false;
            var main = GameEntry.LDLTable.GetTable<Config.shopping_center>();
            
            //判断周卡是否解锁
            var config1 = main.FirstOrDefault(x => x.id == paymenttype.paymenttype_weeklycard);
            if (config1 != null)
            {
                if (ToolScriptExtend.GetDemandUnlock(config1.open_condition))
                {
                    //可领取礼包奖励
                    var list = mallManager.GetWeeklyCardReceiveList();
                    if (list.Count > 0)
                    {
                        return true;
                    }
                }
            }
            
            //判断月卡是否解锁
            var config2 = main.FirstOrDefault(x => x.id == paymenttype.paymenttype_monthycard);
            if (config2 != null)
            {
                if (ToolScriptExtend.GetDemandUnlock(config2.open_condition))
                {
                    var status = mallManager.GetMonthlyCardStatus();
                    if (status == 1)
                    {
                        return true;
                    }
                }
            }
            return false;
        }
        
        //判断商城中月卡/周卡是否有可领奖(商城建筑上的气泡显示)
        private void CheckShopBubble()
        {
            string checkKey = ToolScriptExtend.GetRolePlayerPrefsKeyById("CheckShopToday");
            bool isTodayCheck = ToolScriptExtend.GetCommonTipsIsTodayCheckBox(checkKey);
            if (!isTodayCheck)
            {
                SetShopBubbleActive(true);
                return;
            }
            
            var mallManager = GameEntry.LogicData.MallData;
            //商城是否解锁
            if (!mallManager.IsMallUnlock()) return;
            var main = GameEntry.LDLTable.GetTable<Config.shopping_center>();
            
            //判断周卡是否解锁
            var config1 = main.FirstOrDefault(x => x.id == paymenttype.paymenttype_weeklycard);
            if (config1 != null)
            {
                if (ToolScriptExtend.GetDemandUnlock(config1.open_condition))
                {
                    //可领取礼包奖励
                    var list = mallManager.GetWeeklyCardReceiveList();
                    if (list.Count > 0)
                    {
                        SetShopBubbleActive(true);
                        return;
                    }
                }
            }
            
            //判断月卡是否解锁
            var config2 = main.FirstOrDefault(x => x.id == paymenttype.paymenttype_monthycard);
            if (config2 != null)
            {
                if (ToolScriptExtend.GetDemandUnlock(config2.open_condition))
                {
                    var status = mallManager.GetMonthlyCardStatus();
                    if (status == 1)
                    {
                        SetShopBubbleActive(true);
                        return;
                    }
                }
            }
            SetShopBubbleActive(false);
        }
        
        private void SetShopBubbleActive(bool isActive)
        {
            var module = GameEntry.LogicData.BuildingData.GetBuildingModuleById(1401);
            module?.OnShopStateChange(isActive ? ShopBuildingState.CanGetReward:ShopBuildingState.Remove);
        }
    }
}
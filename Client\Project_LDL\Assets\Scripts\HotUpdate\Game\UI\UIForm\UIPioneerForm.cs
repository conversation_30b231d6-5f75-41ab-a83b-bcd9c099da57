using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using Activity;
using DG.Tweening;
using Game.Hotfix.Config;
using UnityEngine;
using UnityEngine.UI;

namespace Game.Hotfix
{
    public partial class UIPioneerForm : UGuiFormEx
    {
        private ChaoZhiData ChaoZhiManager => GameEntry.LogicData.ChaoZhiData;

        private ActivityTime activityMsg;
        private int TemplateId;
        private PushActivityBattlePassConfig ConfigData;
        private PushActivityBattlePassData MsgData;

        //-------------------------第一个标签-------------------------
        private class DayNode
        {
            public int day; //第几天
            public GameObject offBg; //普通页签状态
            public GameObject lockBg; //锁定页签状态
            public GameObject onBg; //选中页签状态（已解锁）
            public GameObject lockOnBg; //选中页签状态（未解锁）
            public UIButton btn; //点击按钮
            public Transform dot; //红点父节点
        }

        private List<DayNode> dayNodeList = new List<DayNode>();
        private int curSelectDay = -1;
        private UISwitchTagGroup SwitchTargetTaskGroup;
        private List<activity_battlepass_task> configTasks = new List<activity_battlepass_task>();
        private ScrollRect scrollTargetTask;
        private Transform scrollTargetTaskRoot;
        private float taskItemHeight = 200;
        private float taskSpacing = 10;
        private int checkDayNodeIndex = 1;
        private itemid iconId;
        private string iconPath;
        private int curTaskIndex = -1;
        //-------------------------第二个标签-------------------------
        private List<ActivityBattlePassScore> scoreConfig = new List<ActivityBattlePassScore>();
        private RectTransform cycleContent;
        private ScrollRect cycleScroll;
        private ScrollRect sliderScroll;
        private WaitForSeconds delayTime = new WaitForSeconds(0.2f);
        private bool isTimer = false;
        private int timerCount; //倒计时数值
        
        //-------------------------第三个标签-------------------------
        


        //---------------------------------------------------------------------
        private UISwitchTagGroup SwitchTagGroup;
        private List<int> uniqueIdList = new List<int>();

        protected override void OnInit(object userData)
        {
            base.OnInit(userData);
            scoreConfig.Clear();
            m_goZLNode.gameObject.SetActive(true);
            InitBind();
            CheckMultiLanguage(gameObject);
            scrollTargetTask = m_goTargetTask.transform.Find("Scroll View").GetComponent<ScrollRect>();
            scrollTargetTaskRoot = scrollTargetTask.content.transform;

            SwitchTagGroup = m_goTagGroup.GetComponent<UISwitchTagGroup>();
            taskItemHeight = m_goTaskItem.GetComponent<RectTransform>().rect.height;
            taskSpacing = scrollTargetTaskRoot.GetComponent<VerticalLayoutGroup>().spacing;

            cycleScroll = m_TableViewVPass.gameObject.GetComponent<ScrollRect>();
            cycleContent = cycleScroll.content;
            sliderScroll = m_goRewardNode.transform.Find("followScroll").GetComponent<ScrollRect>();
            //给循环滑动事件添加逻辑，让sliderScroll跟随循环列表滑动
            cycleScroll.onValueChanged.AddListener((a) => { sliderScroll.normalizedPosition = a; });

            var nameList = new List<string>()
            {
                ToolScriptExtend.GetLang(1100480), //"先锋目标", 
                ToolScriptExtend.GetLang(1100481), //"先锋战令"
                ToolScriptExtend.GetLang(1100482), //"先锋指挥官"
            };
            SwitchTagGroup.Init(m_goTagItem, m_goTagRoot.transform, nameList, (index) => { OnSwitchTagLogic(index); });
            uniqueIdList.Clear();
            uniqueIdList.AddRange(new List<int>() { 1, 2, 3 });
            SwitchTagGroup.BindUniqueId(uniqueIdList);

            for (var i = 0; i < 3; i++)
            {
                //显示标签图标
                var tagObj = SwitchTagGroup.GetTagObjByIndex(i);
                if (tagObj != null)
                {
                    var icon = tagObj.transform.Find("btn/onBg/icon").GetComponent<UIImage>();
                    icon.SetImage($"Sprite/ui_xianfeng/xianfeng_qieye_icon{i + 1}.png");
                }
            }

            InitDayTagList();
            InitTargetTask();
        }

        protected override void OnOpen(object userData)
        {
            base.OnOpen(userData);
            
            var startFlag = 0;
            if (userData != null)
            {
                startFlag = Mathf.Clamp((int)userData, 0, 2);
            }
            
            scoreConfig.Clear();
            if (ChaoZhiManager.GetPioneerActivityTime(out var data))
            {
                activityMsg = data;
                TemplateId = (int)activityMsg.Template;
                iconId = ChaoZhiManager.GetZLScoreIcon(TemplateId);
                iconPath = ToolScriptExtend.GetItemIcon(iconId);
                ConfigData = ChaoZhiManager.GetZLConfig(TemplateId);
                MsgData = ChaoZhiManager.GetZLMsg(TemplateId);
            }

            m_TableViewVPass.GetItemCount = () =>
            {
                if (ConfigData == null || ConfigData.ScoreRewards == null)
                {
                    return 0;
                }
                else
                {
                    return ConfigData.ScoreRewards.Count;
                }
            };
            m_TableViewVPass.GetItemGo = () => m_goScoreRewardItem;
            m_TableViewVPass.UpdateItemCell = UpdateScoreLogic;
            m_TableViewVPass.InitTableViewByIndex(0);
            
            //高级奖励描述
            m_txtDesc.text = ToolScriptExtend.GetLang(1100412);
            curSelectDay = -1;
            m_goPrefab.SetActive(false);
            //先锋目标   先锋战令   先锋指挥官
            configTasks.Clear();
            configTasks = ChaoZhiManager.GetAllPioneerTaskList();
            OnSelectDayNodeByIndex(1);
            
            InitPageView();
            ChaoZhiManager.C2SPioneerMsg();
            //开启一个定时器
            Timers.Instance.Add(GetInstanceID().ToString(), 1, (a) =>
            {
                OnTimer();
            },86400);
            SwitchTagGroup.ForceSelect(startFlag);
        }

        protected override void OnClose(bool isShutdown, object userData)
        {
            base.OnClose(isShutdown, userData);
            Timers.Instance.Remove(GetInstanceID().ToString());
            scoreConfig.Clear();
        }

        public override void OnRefresh(object userData)
        {
            base.OnRefresh(userData);
            var flag = (int)userData;
            if (flag == 1)
            {
                TemplateId = (int)activityMsg.Template;
                iconId = ChaoZhiManager.GetZLScoreIcon(TemplateId);
                iconPath = ToolScriptExtend.GetItemIcon(iconId);
                ConfigData = ChaoZhiManager.GetZLConfig(TemplateId);
                MsgData = ChaoZhiManager.GetZLMsg(TemplateId);
                InitPageView();
            }
            else if (flag == 2)
            {
                CheckRedDot();
            }
        }
        
        //计时器逻辑
        public void OnTimer()
        {
            if (!isTimer) return;
            var temp = timerCount - 1;
            if (temp >= 0)
            {
                timerCount--;
                if (timerCount == 0)
                {
                    isTimer = false;
                    //初始化请求
                }
                ShowTimerTxt();
            }
        }
        
        private void OnSwitchTagLogic(int index)
        {
            m_goTargetNode.SetActive(index == 0);
            m_goZLNode.SetActive(index == 1);
            m_goCommanderNode.SetActive(index == 2);

            //显示标签图标
            var tagObj = SwitchTagGroup.GetTagObjByIndex(index);
            if (tagObj != null)
            {
                var icon = tagObj.transform.Find("btn/onBg/icon");
                if (icon != null)
                {
                    icon.DOKill();
                    icon.DOScale(1.1f, 0.2f)
                        .SetEase(Ease.OutBack)
                        .OnComplete(() =>
                        {
                            icon.DOScale(1, 0.1f)
                                .SetEase(Ease.InOutQuad);
                        });
                }
            }
            
            if (index == 0)
            {
                
            }
            else if (index == 1)
            {
                ScrollToRewardIndex();
            }
            else if (index == 2)
            {
                m_scrollviewGuide.normalizedPosition = new Vector2(0, 1);
            }
        }
        
        private void InitPageView()
        {
            if (MsgData == null || ConfigData == null) return;
            scoreConfig = ConfigData.ScoreRewards.ToList();
            //---------------------先锋目标---------------------
            ShowTargetScore();
            SetAllScoreIcon();
            SetScoreProgress();
            
            var heroId = GetTargetHero();
            ToolScriptExtend.ShowHeroSpine(heroId, m_spuiRole);
            ToolScriptExtend.ShowHeroSpine(heroId, m_spuiRole2);

            //更新目标任务
            
            SwitchTargetTaskGroup.ForceSelect(curTaskIndex == -1 ? 0 : curTaskIndex);
            //---------------------先锋战令---------------------
            ShowMainReward();
            
            if (ToolScriptExtend.GetTable<activity_battlepass_grade>(out var table2))
            {
                var gradeConfig = table2.FirstOrDefault(x => (int)x.activity_templateid == TemplateId);
                if (gradeConfig != null)
                {
                    //全购状态  性价比  原价  折扣价  累充积分
                    m_txtDiscount.text = $"{gradeConfig.effectiveness}%";
                    GameEntry.LogicData.MallData.CreateRechargeScore(gradeConfig.payment_id, m_btnBuy.transform, 40,
                        -90);
                    if (ToolScriptExtend.GetConfigById<payment>((int)gradeConfig.payment_id, out var data1))
                    {
                        var priceTxt = m_btnBuy.transform.Find("Text").GetComponent<UIText>();
                        priceTxt.text = ToolScriptExtend.GetLang(data1.price_lang_id);
                    }
                }
            }
            
            //积分信息数量
            m_txtScore.text = MsgData.Score.ToString();
            var num = MsgData.Score.ToString().Length;
            var scoreBg = m_goScoreBg.GetComponent<RectTransform>();
            scoreBg.SetSizeWithCurrentAnchors(RectTransform.Axis.Horizontal,num < 4 ?205:260);
            
            //购买状态显示
            var isSoldOut = IsSoldOut(new[] { 1 });
            m_txtDesc.gameObject.SetActive(!isSoldOut);
            m_btnBuy.gameObject.SetActive(!isSoldOut);
            
            //活动倒计时
            timerCount = ChaoZhiManager.GetRemainTime((ulong)activityMsg.EndTime);
            isTimer = timerCount > 0;
            ShowTimerTxt();
            
            //无限宝箱
            ShowInfiniteBox();

            CalculateProgress();
            //---------------------先锋指挥官---------------------

            ShowGuideList();

        }

        #region 公共逻辑

        //实现多语言
        private void CheckMultiLanguage(GameObject obj)
        {
            if (obj == null) return;
            var list = obj.GetComponentsInChildren<MultiLanguage>(true);
            foreach (var item in list)
            {
                var txt = item.gameObject.GetComponent<UIText>();
                if (txt != null)
                {
                    txt.text = ToolScriptExtend.GetLang(item.langId);
                }
            }
        }

        private void SetAllScoreIcon()
        {
            m_imgScore.SetImage(iconPath);
            m_imgScoreIcon1.SetImage(iconPath);
            m_imgScoreIcon2.SetImage(iconPath);
        }

        private int GetTargetHero()
        {
            //目标英雄
            if (ToolScriptExtend.GetTable<activity_battlepass_main>(out var table1))
            {
                var config = table1.FirstOrDefault(x => (int)x.activity_templateid == TemplateId);
                if (config != null)
                {
                    return (int)config.hero_image;
                };
            }
            return -1;
        }
        
        #endregion

        #region 按钮事件注册

        private void OnBtnExitClick()
        {
            Close();
        }

        //先锋战令 帮助
        private void OnBtnHelpClick()
        {
            ChaoZhiManager.ShowActivityTip(TemplateId);
        }

        //一键领取按钮
        private void OnBtnGetAllClick()
        {
            //奖励页签领奖
            var zlPassDot = ChaoZhiManager.GetPioneerRedDotCountById(3);
            if (zlPassDot == 0)
            {
                GameEntry.UI.OpenUIForm(EnumUIForm.UIFlyTextForm, new FlyTextParams()
                {
                    Content = ToolScriptExtend.GetLang(1100426) //目前没有领取的奖励
                });
                return;
            }

            var reqData = new ActivityDrawReq
            {
                Type = activityMsg.Type,
                Template = MsgData.Template,
                LoopTimes = MsgData.LoopTimes,
                DrawId = 1
            };
            ChaoZhiManager.C2SActivityDrawReq(reqData, (resp) => { });
        }

        //查看英雄跳转
        private void OnBtnCheckClick()
        {
            var heroId = GetTargetHero();
            var module = GameEntry.LogicData.HeroData.GetHeroModule((itemid)heroId);
            if (module.IsCombind)
            {
                GameEntry.UI.OpenUIForm(EnumUIForm.UIHeroForm);
            }
            else
            {
                GameEntry.LogicData.HeroData.OpenHeroSingleForm((itemid)heroId);
            }
        }

        private void OnBtnBuyClick()
        {
            GameEntry.UI.OpenUIForm(EnumUIForm.UIZhanLingBuyForm, TemplateId); 
        }

        //先锋目标帮助
        private void OnBtnTargetHelpClick()
        {
            GameEntry.UI.OpenUIForm(EnumUIForm.UIComDescForm, 20);
        }

        private void OnBtnTargetExtraRewardClick()
        {
            GameEntry.UI.OpenUIForm(EnumUIForm.UIPioneerRewardForm, TemplateId);
        }

        private void OnBtnCheck2Click()
        {
            OnBtnCheckClick();
        }

        private void OnBtnAddScoreClick()
        {
            SwitchTagGroup.OnSelectLogic(0);
        }

        #endregion

        #region 第一个标签 先锋目标

        #region 天数节点查看

        private void InitDayTagList()
        {
            dayNodeList.Clear();
            var root = m_goDayNode.transform;
            var txts = root.GetComponentsInChildren<UIText>(true);
            var str = ToolScriptExtend.GetLang(1100485); //“天数”
            foreach (var txt in txts)
            {
                txt.text = str;
            }

            for (var i = 1; i <= 5; i++)
            {
                var child = root.Find($"node{i}");
                dayNodeList.Add(new DayNode()
                {
                    day = i,
                    offBg = child.Find("offBg").gameObject,
                    lockBg = child.Find("lockBg").gameObject,
                    onBg = child.Find("onBg").gameObject,
                    lockOnBg = child.Find("lockOnBg").gameObject,
                    btn = child.Find("btn").GetComponent<UIButton>(),
                    dot = child.Find("dot"),
                });
            }

            foreach (var node in dayNodeList)
            {
                ToolScriptExtend.BindBtnLogic(node.btn, () => { OnSelectDayNodeByIndex(node.day); });
            }
        }

        /// <summary>
        /// 控制DayNode显隐
        /// </summary>
        /// <param name="isSelect">是否点击选中</param>
        /// <param name="today">今天是第几天</param>
        /// <param name="node"></param>
        private void SetDayNodeActive(bool isSelect, int today, DayNode node)
        {
            var isUnlock = node.day <= today;
            node.onBg.SetActive(isSelect && isUnlock);
            node.lockOnBg.SetActive(isSelect && !isUnlock);
            node.offBg.SetActive(!isSelect && isUnlock);
            node.lockBg.SetActive(!isSelect && !isUnlock);
        }
        
        private void OnSelectDayNodeByIndex(int day)
        {
            if (curSelectDay == day)
            {
                return;
            }

            curSelectDay = day;

            var today = ChaoZhiManager.GetToday(activityMsg.EndTime);
            foreach (var node in dayNodeList)
            {
                SetDayNodeActive(day == node.day, today, node);
            }

            var isUnlock = day <= today;
            m_goTargetTask.SetActive(isUnlock);
            m_goTargetLock.SetActive(!isUnlock);
            if (isUnlock)
            {
                SwitchTargetTaskGroup.SetTagNameList(GetTaskTagNameList(day));
                SwitchTargetTaskGroup?.ForceSelect(0);
            }
            else
            {
                ShowDayRewardPre(day);
            }

            //活动第{activity_days}天即可解锁目前的先锋任务
            var format = new Dictionary<string, object> { { "activity_days", curSelectDay } };
            m_txtTargetLockTip.text = ToolScriptExtend.GetLangFormat(1100486, format);
            
            CheckTaskTagRedDot(TemplateId, curSelectDay);
        }

        private List<string> GetTaskTagNameList(int day)
        {
            var result = new List<string>();
            var list = ChaoZhiManager.GetTargetTaskListByType(TemplateId,day, 0);
            for (var i = 1; i <= 3; i++)
            {
                var data = list.FirstOrDefault(x => x.page_order == i);
                result.Add( data != null ?ToolScriptExtend.GetLang(data.page_name):"");
            }
            return result;
        }
        
        #endregion

        #region 目标任务

        private void InitTargetTask()
        {
            SwitchTargetTaskGroup = m_goTargetTagGroup.GetComponent<UISwitchTagGroup>();
            var nameList = GetTaskTagNameList(1);
            var root = SwitchTargetTaskGroup.transform.Find("Viewport/root");
            SwitchTargetTaskGroup.Init(m_goTargetTagItem, root, nameList,
                (index) =>
                {
                    curTaskIndex = index;
                    ShowTaskList(curSelectDay, index + 1);
                });
            SwitchTargetTaskGroup.BindUniqueId(new List<int>() { 1, 2, 3 });
            
            //完成任务获得：莫妮卡
            m_txtTargetTip.text = ToolScriptExtend.GetLang(1100483);
        }
        
        private void ShowTaskList(int day, int index)
        {
            if (day < 0) return;
            var list = ChaoZhiManager.GetTargetTaskListByType(TemplateId,day, index);
            var sumCount = list.Count;
            var value = taskItemHeight * sumCount + taskSpacing * (sumCount - 1);
            scrollTargetTask.content.SetSizeWithCurrentAnchors(RectTransform.Axis.Vertical, value);
            scrollTargetTask.normalizedPosition = new Vector2(0, 1);
            ToolScriptExtend.RecycleOrCreate(m_goTaskItem, scrollTargetTaskRoot, sumCount);
            for (var i = 0; i < sumCount; i++)
            {
                var data = list[i];
                var child = scrollTargetTaskRoot.GetChild(i);
                UpdateTaskLogic(i, child.gameObject, data);
            }
        }

        private void UpdateTaskLogic(int index, GameObject obj, activity_battlepass_task config)
        {
            if (config == null) return;
            var msg = ChaoZhiManager.GetZLTaskMsg(TemplateId, config.task_id);
            if (msg == null) return;
            var root = obj.transform.Find("bg");
            var name = root.Find("name").GetComponent<UIText>();
            var btnGo = root.Find("btnGo").GetComponent<UIButton>();
            var btnGray = root.Find("btnGray").GetComponent<UIButton>();
            var btnGet = root.Find("btnGet").GetComponent<UIButton>();
            var goFinish = root.Find("goFinish");
            var scroll = root.Find("Scroll View").GetComponent<ScrollRect>();
            var rewardRoot = root.Find("Scroll View/Viewport/Content");
            var progress = root.Find("progress").GetComponent<UIText>();
            
            var content =
                ChaoZhiManager.GetTaskFormatStr(config.activity_task_desc, config.task_type, config.task_value);
            var result = $"({msg.Process}/{config.task_target_value})";
            name.text = content;
            progress.text = result;
            
            LayoutRebuilder.ForceRebuildLayoutImmediate(name.rectTransform);

            List<reward> rewardList = config.activity_task_reward;
            ToolScriptExtend.RecycleOrCreate(m_goTaskReward, rewardRoot, rewardList.Count);

            var rewards = new List<PbGameconfig.reward>();
            foreach (var node in rewardList)
            {
                rewards.Add(new PbGameconfig.reward() { ItemId = (PbGameconfig.itemid)node.item_id, Num = node.num });
            }

            ChaoZhiManager.ShowRewardList(m_goReward, rewardRoot, rewards, 0.56f);
            scroll.enabled = rewardList.Count > 4;
            
            //奖励领取状态0：进行中 1：可领取  2：已领取
            var status = ChaoZhiManager.GetZLTaskStatus(msg);
            btnGo.gameObject.SetActive(config.task_type != affairtype.affairtype_1019 && status == 0);
            btnGray.gameObject.SetActive(config.task_type == affairtype.affairtype_1019 &&  status == 0);
            btnGet.gameObject.SetActive(status == 1);
            goFinish.gameObject.SetActive(status >= 2);
            progress.gameObject.SetActive(status < 2);

            ToolScriptExtend.BindBtnLogic(btnGo, () => { });
            ToolScriptExtend.BindBtnLogic(btnGet, () =>
            {
                var req = new ActivityBattlePassDrawTaskReq
                {
                    Template = MsgData.Template,
                    LoopTimes = MsgData.LoopTimes,
                    TaskId = msg.Id
                };
                ChaoZhiManager.C2SActivityBattlePassDrawTaskReq(req, (resp) =>
                {
                    var temp = config.activity_task_reward.FirstOrDefault(x => x.item_id == iconId);
                    if (temp != null)
                    {
                        FlyResManager.UIFlyByTrans(iconId, (int)temp.num, btnGet.transform, m_imgScore.transform);
                    }
                });
            });
        }

        //展示指定天数预览奖励
        private void ShowDayRewardPre(int day)
        {
            var list = ChaoZhiManager.GetTargetTaskListByType(TemplateId,day, 0);

            var dic = new Dictionary<itemid, long>();
            foreach (var node in list)
            {
                foreach (var rewards in node.activity_task_reward)
                {
                    if (!dic.ContainsKey(rewards.item_id))
                    {
                        dic[rewards.item_id] = 0;
                    }
                    dic[rewards.item_id] += rewards.num;
                }
            }
            
            var count = dic.TryGetValue(iconId, out var value) ? value : 0;
            m_imgScoreIcon2.transform.Find("txt").GetComponent<UIText>().text = $"x{ count}";
            
            List<reward> rewardList = new List<reward>();
            foreach (var data in dic)
            {
                if (data.Key == iconId) continue;
                if (ToolScriptExtend.GetItemConfig(data.Key) == null) continue;
                rewardList.Add(new reward { item_id = data.Key, num = data.Value });
            }

            rewardList.Sort(ChaoZhiManager.CompareRewardLogic);
            var root = m_scrollviewPreview.content.transform;
            ToolScriptExtend.RecycleOrCreate(m_goTaskReward, root, rewardList.Count);
            for (var j = 0; j < rewardList.Count; j++)
            {
                var info = rewardList[j];
                var rewardChild = root.GetChild(j);
                var node = rewardChild.Find("node");
                ChaoZhiManager.LoadReward(node,info,null,0.5f,1.3f);
            }
        }

        #endregion

        #region 目标分数进度

        private void ShowTargetScore()
        {
            var list = ChaoZhiManager.GetAllPioneerVipList();
            if (list == null) return;
            var root = m_goScoreRewardNode.transform;
            var sumCount = list.Count;
            if (sumCount <= 0 || sumCount != root.childCount) return;

            for (var i = 0; i < list.Count; i++)
            {
                var child = root.GetChild(i);
                var data = list[i];
                // 状态：0：积分未达标 1：可领取 2: 已领取 3：上锁
                var status = ChaoZhiManager.CheckPioneerVipStatus(data,MsgData);
                var rewardNode = child.Find("reward");
                var lockNode = child.Find("lock");
                var txt = child.Find("txt").GetComponent<UIText>();

                rewardNode.gameObject.SetActive(status == 1);
                lockNode.gameObject.SetActive(status == 3);
                txt.text = list[i].score.ToString();
            }
        }

        //显示积分进度数据
        private void SetScoreProgress()
        {
            var value1 = MsgData.Score;
            if (value1 < 0)
            {
                value1 = 0;
            }

            var list = ChaoZhiManager.GetAllPioneerVipList();
            var maxValue = list.Max(x => x.score);
            var value2 = maxValue;

            foreach (var data in list)
            {
                if (value1 < data.score)
                {
                    value2 = data.score;
                    break;
                }
            }

            //解锁下次奖励：
            m_txtTargetProgressTip.text =
                $"{ToolScriptExtend.GetLang(1100484)}  <color=#ffd800>{value1}/{value2}</color>";
            m_txtTargetCount.text = $"{value1}/{maxValue}";
            //进度条显示
            m_sliderTarget.value = ChaoZhiManager.GetPioneerTargetProgress(MsgData, 0.105f);
            
            //入口奖励锁判断
            bool showLock = false;
            var vipLevel = GameEntry.LogicData.VipData.GetVipLevel();
            foreach (var data in list)
            {
                if (value1 >= data.score)
                {
                    if (vipLevel < data.vip_level)
                    {
                        showLock = true;
                        break;
                    }
                }
            }
            m_btnTargetExtraReward.transform.Find("lock").gameObject.SetActive(showLock);
        }

        #endregion

        #endregion

        #region 第二个标签 先锋战令

        private void ShowTimerTxt()
        {
            var str = timerCount <= 0 ? "00:00:00" :ToolScriptExtend.FormatTime(timerCount);
            m_txtTimer1.text = str;
            m_txtTimer2.text = str;
            m_txtTimer3.text = str;
        }
        //奖励列表
        private void ShowMainReward()
        {
            if (m_TableViewVPass.itemPrototype == null)
            {
                m_TableViewVPass.InitTableViewByIndex(0);
            }
            else
            {
                m_TableViewVPass.ReloadData();
            }

            ScrollToRewardIndex();

            //统一进度条、滑动条Scroll的Content高度，和循环列表的Content高度一样
            AutoFitContent();
        }
        
        //列表滑动到第一个可领取奖励的位置上,或者进行中的节点item
        private void ScrollToRewardIndex()
        {
            if (ConfigData == null || MsgData == null)
            {
                return;
            }

            var dataList = ConfigData.ScoreRewards.ToList();
            List<ActivityBattlePassScore> sortList = new List<ActivityBattlePassScore>();
            sortList.AddRange(ConfigData.ScoreRewards);
            sortList.Sort((a, b) =>
            {
                var statusA = ChaoZhiManager.CheckZLItemStatus(a.Id, a.Score,MsgData.Score,TemplateId);
                var statusB = ChaoZhiManager.CheckZLItemStatus(b.Id, b.Score,MsgData.Score,TemplateId);
                var flagA = GetRewardCompareFlag(statusA);
                var flagB = GetRewardCompareFlag(statusB);
                if (flagA != flagB) return flagA - flagB;
                return a.Score - b.Score;
            });

            var result = sortList[0];
            var resultScore = result.Score;
            var resultStatus = ChaoZhiManager.CheckZLItemStatus(result.Id, result.Score,MsgData.Score,TemplateId);
            var index = dataList.FindIndex(x => x.Score == resultScore);
            if (resultStatus == 0)
            {
                if (index - 1 < 0)
                {
                    index = 0;
                }
                else
                {
                    index--;
                }
            }

            if (gameObject.activeInHierarchy)
            {
                StartCoroutine(DelayScroll(() =>
                {
                    m_TableViewVPass.MoveToTargetIndex(index,0.4f);
                    sliderScroll.normalizedPosition = cycleScroll.normalizedPosition;
                }));
            }
        }

        IEnumerator DelayScroll(Action callback)
        {
            yield return delayTime;
            callback?.Invoke();
        }

        // 自定义奖励排序优先级 :可领取(1),未解锁(2),继续领取(3),已领取(4),
        private int GetRewardCompareFlag(int status)
        {
            // 积分里程碑奖励领取状态  0：未解锁 1:可领取 2：已领取 3：继续领取 
            if (status == 0)
            {
                return 2;
            }
            else if (status == 1)
            {
                return 1;
            }
            else if (status == 2)
            {
                return 4;
            }
            else if (status == 3)
            {
                return 3;
            }

            return 4;
        }

        private void AutoFitContent()
        {
            //统一进度条、滑动条Scroll的Content高度，和循环列表的Content高度一样
            var itemRect = m_sliderProgress.GetComponent<RectTransform>();
            var calculateHeight = cycleContent.rect.height;
            itemRect.SetSizeWithCurrentAnchors(RectTransform.Axis.Horizontal, calculateHeight - 200);
            sliderScroll.content.SetSizeWithCurrentAnchors(RectTransform.Axis.Vertical, calculateHeight);
        }
        
        private void UpdateScoreLogic(int index, GameObject obj)
        {
            if (scoreConfig.Count == 0) return;
            var scoreData = scoreConfig[index];
            var id = scoreData.Id;
            var root = obj.transform.Find("main");
            var btnGet = root.Find("btnGet").GetComponent<UIButton>();
            var btnReGet = root.Find("btnReGet").GetComponent<UIButton>();
            var btnLock = root.Find("btnLock").GetComponent<UIButton>();
            var goFinish = root.Find("goFinish").gameObject;
            var scoreIcon = root.Find("scoreIcon").GetComponent<UIImage>();
            var scoreTxt = root.Find("scoreIcon/scoreTxt").GetComponent<UIText>();

            var freeReward = root.Find("freeReward");
            var payReward1 = root.Find("payReward1");
            var payReward2 = root.Find("payReward2");

            var scoreValue = scoreData.Score;
            var status = ChaoZhiManager.CheckZLItemStatus(id, scoreValue,MsgData.Score,TemplateId);
            //奖励领取状态 0：未解锁 1:可领取 2：已领取 3：继续领取 
            btnLock.gameObject.SetActive(status == 0);
            btnGet.gameObject.SetActive(status == 1);
            goFinish.gameObject.SetActive(status == 2);
            btnReGet.gameObject.SetActive(status == 3);

            var hex = status == 0 ? "#FFFFFF" : "#3AF416";
            scoreTxt.text = $"<color={hex}>{scoreValue}</color>";

            m_imgScore.SetImage(ToolScriptExtend.GetItemIcon(iconId));

            if (!string.IsNullOrEmpty(iconPath))
            {
                scoreIcon.SetImage(iconPath);
            }
            
            //奖励
            //免费奖励
            var reward1 = scoreData.Reward0.ToList().First();
            ChaoZhiManager.ShowZLRewardMsg(id, true, freeReward, reward1, scoreValue,m_goReward,TemplateId);

            //付费第一档奖励
            var temp = scoreData.Reward1.ToList();
            ChaoZhiManager.ShowZLRewardMsg(id, false, payReward1, temp[0], scoreValue,m_goReward,TemplateId);
            ChaoZhiManager.ShowZLRewardMsg(id, false, payReward2, temp[1], scoreValue,m_goReward,TemplateId);
            
            ToolScriptExtend.BindBtnLogic(btnGet, () =>
            {
                var reqData = new ActivityDrawReq
                {
                    Type = activityMsg.Type,
                    Template = MsgData.Template,
                    LoopTimes = MsgData.LoopTimes,
                    DrawId = scoreData.Id
                };
                ChaoZhiManager.C2SActivityDrawReq(reqData, (resp) => { });
            });

            ToolScriptExtend.BindBtnLogic(btnReGet, () => { GameEntry.UI.OpenUIForm(EnumUIForm.UIZhanLingBuyForm, (int)MsgData.Template); });
            CheckMultiLanguage(obj);
        }
        
        //计算进度值
        private void CalculateProgress()
        {
            var startRatio = 0;
            var scoreList = ConfigData.ScoreRewards.Select(x => x.Score).ToList();
            scoreList.Sort((a, b) => a - b);
            var maxValue = scoreList.Max(x=>x);
            var minValue = scoreList.Min(x => x);
            var myScore = MsgData.Score;
            if (MsgData.Score >= maxValue)
            {
                m_sliderProgress.value = 1;
            }
            else if (MsgData.Score <= minValue)
            {
                m_sliderProgress.value = 0;
            }
            else
            {
                float ratioSum = 0;
                var lastNodeValue = minValue;
                var averageRatio = 1.0f / (scoreList.Count - 1);
                for (var i = 1; i < scoreList.Count; i++)
                {
                    var curValue = scoreList[i];
                    if (myScore >= curValue)
                    {
                        ratioSum += averageRatio;
                        lastNodeValue = curValue;
                    }
                    else
                    {
                        var offset = myScore - lastNodeValue;
                        var section = curValue - lastNodeValue;
                        var ratio = (offset * 1.0f / section) * averageRatio;
                        ratioSum += ratio;
                        break;
                    }
                }
                m_sliderProgress.value = ratioSum;
            }
        }

        //是否售罄,即是否可购买
        //gradeList：有一档购买填{1} 有两档购买填{1,2} 
        private bool IsSoldOut(int[] gradeArray)
        {
            if (gradeArray == null || gradeArray.Length == 0)
            {
                return false;
            }

            var list = MsgData.UnlockGrade.ToList();
            foreach (var value in gradeArray)
            {
                if (!list.Contains(value))
                {
                    return false;
                }
            }

            return true;
        }
        
          //无限宝箱
        private void ShowInfiniteBox()
        {
            var root = m_goInfiniteBox.transform;
            var m_txtInfiniteBoxDesc = root.Find("m_txtInfiniteBoxDesc").GetComponent<UIText>();
            var lockObj = root.Find("Lock");
            var m_sliderInfinity = root.Find("m_sliderInfinity").GetComponent<Slider>();
            var btnGet = root.Find("btnGet").GetComponent<UIButton>();
            var btnGray = root.Find("btnGray").GetComponent<UIButton>();
            var btnCheck = root.Find("box/btnCheck").GetComponent<UIButton>();
            var m_txtInfinityValue = root.Find("m_sliderInfinity/m_txtInfinityValue").GetComponent<UIText>();
            var tip = root.Find("box/tip");
            var tipCount = root.Find("box/tip/txt").GetComponent<UIText>();

            var maxValue = ConfigData.BoxUnlockScore;

            var isUnlock = MsgData.Score >= maxValue;
            lockObj.gameObject.SetActive(!isUnlock);
            m_sliderInfinity.gameObject.SetActive(isUnlock);

            m_txtInfiniteBoxDesc.text = isUnlock
                ? ToolScriptExtend.GetLang(1100418)
                : //开启需累积战令积分
                ToolScriptExtend.GetLangFormat(1100416, maxValue.ToString()); //战令分数达到{0}后解锁

            var rewards = new List<reward>();
            foreach (var reward in ConfigData.BoxRewards)
            {
                rewards.Add(new reward() { item_id = (itemid)reward.ItemId, num = reward.Num });
            }

            ToolScriptExtend.BindBtnLogic(btnCheck,
                () => { GameEntry.UI.OpenUIForm(EnumUIForm.UIRewardPreviewForm, rewards); });

            bool isOk = false;
            if (isUnlock)
            {
                long availableValue = MsgData.Score;
                var sum = maxValue + ConfigData.BoxScore * MsgData.BoxDrawTimes;
                if (MsgData.Score >= sum)
                {
                    availableValue = MsgData.Score - sum;
                }

                var configValue = ConfigData.BoxScore;
                var showCount = availableValue % configValue;
                m_txtInfinityValue.text = $"{showCount}/{configValue}";
                var rewardCount = availableValue / configValue; //可领取数量
                var progress = showCount * 1.0f / configValue;
                m_sliderInfinity.value = Mathf.Clamp(progress, 0, 1);
                isOk = rewardCount > 0;
                tipCount.text = rewardCount.ToString();
            }

            tip.gameObject.SetActive(isUnlock && isOk);
            btnGet.gameObject.SetActive(isUnlock && isOk);
            btnGray.gameObject.SetActive(isUnlock && !isOk);

            ToolScriptExtend.BindBtnLogic(btnGet, () =>
            {
                var reqData = new ActivityDrawReq
                {
                    Type = activityMsg.Type,
                    Template = MsgData.Template,
                    LoopTimes = MsgData.LoopTimes,
                    DrawId = 0
                };
                ChaoZhiManager.C2SActivityDrawReq(reqData, (resp) => { });
            });
        }
        
        #endregion

        #region 第二个标签 先锋指挥官

        private void ShowGuideList()
        {
            if (!ToolScriptExtend.GetTable<activity_pioneer_getway>(out var data)) return;
            var root = m_scrollviewGuide.content.transform;
            ToolScriptExtend.RecycleOrCreate(m_goGuideItem, root, data.Count);
            for (var i = 0; i < data.Count; i++)
            {
                var info = data[i];
                var child = root.GetChild(i);
                var icon = child.Find("icon").GetComponent<UIImage>();
                var btn = child.Find("btn").GetComponent<UIButton>();
                var txt = child.Find("txt").GetComponent<UIText>();
                var received = child.Find("received");
                icon.SetImage(info.icon);

                var status = GetGuideStatus(info.id);
                if (status == 0)
                {
                    txt.text = ToolScriptExtend.GetLang(info.preview_desc);
                }
                else if (status == 1)
                {
                    txt.text = ToolScriptExtend.GetLang(info.in_progress_desc);
                    ToolScriptExtend.BindBtnLogic(btn, () =>
                    {
                        
                    });
                }
                else if (status == 2)
                {
                    txt.text = ToolScriptExtend.GetLang(info.end_desc);
                }
                received.gameObject.SetActive(status == 2);
            }
        }

        //指挥官列表item状态
        //0:开放预告  1：进行中  2：已结束
        private int GetGuideStatus(int id)
        {
            return 1;
        }
        
        
        #endregion


        #region 红点逻辑
        
        private void CheckRedDot()
        {
            
            //--------------------------先锋目标--------------------------
            var dayNodeCount = dayNodeList.Count;
            //天数节点
            for (var i = 0; i < dayNodeCount; i++)
            {
                var dayCount = i + 1;
                var node = dayNodeList[i];
                var count = ChaoZhiManager.CheckPioneerTargetDayRedDot(TemplateId,dayCount);
                ToolScriptExtend.SetNumRedDot(node.dot,count);
            }
            
            //任务奖励
            CheckTaskTagRedDot(TemplateId, curSelectDay);

            //进度奖励
            var targetProgressDot = m_btnTargetExtraReward.transform.Find("dot");
            ToolScriptExtend.SetNumRedDot(targetProgressDot,ChaoZhiManager.CheckPioneerTargetProgressRedDot(TemplateId));
            SwitchTagGroup.CheckNumDotLogic(1, ChaoZhiManager.CheckPioneerTargetRedDot());

            //--------------------------先锋战令--------------------------
            var zlPassDot = ChaoZhiManager.GetPioneerRedDotCountById(3);
            var getAllDot = m_btnGetAll.transform.Find("dot");
            getAllDot.gameObject.SetActive(zlPassDot > 0);
            SwitchTagGroup.CheckNumDotLogic(2, zlPassDot);
            
        }
        
        //判断当前正查看天数的任务页签红点
        private void CheckTaskTagRedDot(int templateId,int day)
        {
            for (var j = 1; j <= 3; j++)
            {
                var count = ChaoZhiManager.CheckPioneerTargetTaskRedDot(templateId,day,j);
                SwitchTargetTaskGroup.CheckNumDotLogic(j, count);
            }
        }
        
        
        #endregion
        
        
    }
}
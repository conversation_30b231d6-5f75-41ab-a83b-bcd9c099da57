using UnityEngine;
using UnityEngine.UI;

namespace Game.Hotfix
{
    public partial class UIUnionTechUpForm : UGuiFormEx
    {
        [SerializeField] private UIButton m_btnClose;
        [SerializeField] private UIButton m_btnTip;
        [SerializeField] private UIButton m_btnRecommend;
        [SerializeField] private UIButton m_btnCost_1;
        [SerializeField] private UIButton m_btnCost_2;
        [SerializeField] private UIButton m_btnSure;
        [SerializeField] private UIButton m_btnMax;
        [SerializeField] private UIButton m_btnTipMask;

        [SerializeField] private UIText m_txtName;
        [SerializeField] private UIText m_txtNum;
        [SerializeField] private UIText m_txtDesc;
        [SerializeField] private UIText m_txtRecommend;
        [SerializeField] private UIText m_txtBtn;
        [SerializeField] private UIText m_txtProgress;
        [SerializeField] private UIText m_txtCur;
        [SerializeField] private UIText m_txtNext;
        [SerializeField] private UIText m_txtReward_1;
        [SerializeField] private UIText m_txtReward_2;
        [SerializeField] private UIText m_txtNum_1;
        [SerializeField] private UIText m_txtCost_1;
        [SerializeField] private UIText m_txtCost_2;
        [SerializeField] private UIText m_txtWait;
        [SerializeField] private UIText m_txtTip;
        [SerializeField] private UIText m_txtSure;
        [SerializeField] private UIText m_txtMax;
        [SerializeField] private UIText m_txtCrit;
        [SerializeField] private UIText m_txtTipDesc;

        [SerializeField] private UIImage m_imgIcon;
        [SerializeField] private Image m_imgProgress;
        [SerializeField] private UIImage m_imgProgressIcon;
        [SerializeField] private UIImage m_imgReward_1;
        [SerializeField] private UIImage m_imgReward_2;
        [SerializeField] private UIImage m_imgCost_1;
        [SerializeField] private UIImage m_imgCost_2;

        [SerializeField] private GameObject m_goRecommend;
        [SerializeField] private GameObject m_goProgress;
        [SerializeField] private GameObject m_goShowCur;
        [SerializeField] private GameObject m_goShowNext;
        [SerializeField] private GameObject m_goReward;
        [SerializeField] private GameObject m_goCost_1;
        [SerializeField] private GameObject m_goCost_2;
        [SerializeField] private GameObject m_goWait;
        [SerializeField] private GameObject m_goMax;
        [SerializeField] private Transform m_transEffect;
        [SerializeField] private GameObject m_goCrit;
        [SerializeField] private GameObject m_goTipDesc;
        [SerializeField] private GameObject m_goTip;

        void InitBind()
        {
            m_btnClose.onClick.AddListener(OnBtnCloseClick);
            m_btnTip.onClick.AddListener(OnBtnTipClick);
            m_btnRecommend.onClick.AddListener(OnBtnRecommendClick);
            m_btnCost_1.onClick.AddListener(OnBtnCost_1Click);
            m_btnCost_2.onClick.AddListener(OnBtnCost_2Click);
            m_btnSure.onClick.AddListener(OnBtnSureClick);
            m_btnMax.onClick.AddListener(OnBtnMaxClick);
            m_btnTipMask.onClick.AddListener(OnBtnTipMaskClick);
        }
    }
}

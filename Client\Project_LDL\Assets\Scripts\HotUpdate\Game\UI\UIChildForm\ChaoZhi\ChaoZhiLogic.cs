using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using Activity;
using DG.Tweening;
using UnityEngine;
using UnityEngine.UI;

//超值活动公共逻辑
namespace Game.Hotfix
{
    //创角累充、每日累充、七日累充抽象公共基类
    public abstract class ChaoZhiRecharge:SwitchPanelLogic
    {
        [SerializeField] protected UIButton m_btnHelp;//帮助提示按钮
        [SerializeField] protected UIText m_txtTitle;//标题
        [SerializeField] protected UIText m_txtTimer; //倒计时文本
        [SerializeField] protected UIText m_txtDesc;//活动描述
        [SerializeField] protected UIText m_txtScore;//积分数量
        [SerializeField] protected UIImage m_imgIcon;//活动图标
        [SerializeField] protected GameObject m_goPrefab;
        [SerializeField] protected GameObject m_goReward;
        [SerializeField] protected GameObject m_goItemObj;
        [SerializeField] protected Transform m_goItemRoot;
        [SerializeField] protected Slider m_sliderProgress;
        [SerializeField] protected ScrollRect mainScroll;
        [SerializeField] protected RectTransform mainContent;
        [SerializeField] protected RectTransform sliderRect;
        [SerializeField] protected UIImage m_imgScore;//积分图标
        [SerializeField] protected RectTransform scoreBg;//积分底图
        
        protected int timerCount; //倒计时数值
        protected ChaoZhiData ChaoZhiManager => GameEntry.LogicData.ChaoZhiData;
        protected bool isTimer = false;
        private string iconPath;

        protected int TemplateId;
        protected ActivityTime activityMsg;
        protected PushActivityRechargeConfig ConfigData;
        protected PushActivityRechargeData MsgData;
        protected float startRatio = 0;
        private float itemHeight = 0;
        private float spacing = 0;
        private WaitForSeconds delayTime = new WaitForSeconds(0.1f);
        protected void InitFormData()
        {
            itemHeight = m_goItemObj.GetComponent<RectTransform>().rect.height;
            spacing = m_goItemRoot.GetComponent<VerticalLayoutGroup>().spacing;
            TemplateId = (int)activityMsg.Template;
            ConfigData = ChaoZhiManager.GetRechargeConfig(TemplateId);
            MsgData = ChaoZhiManager.GetRechargeMsg(TemplateId);
            InitPageView();
        }
        
        protected virtual void InitPageView()
        {
            if (MsgData == null || ConfigData == null) return;
            var TemplateId = (int)MsgData.Template;
            var config = ChaoZhiManager.GetActivityConfig(TemplateId);
            if (config == null) return;
            //说明按钮
            BindBtnLogic(m_btnHelp, () =>
            {
                ChaoZhiManager.ShowActivityTip(TemplateId);
            });
            m_txtTitle.text = ToolScriptExtend.GetLang(config.name);
            m_txtDesc.text = ToolScriptExtend.GetLang(config.activity_word);
            
            //计算倒计时
            timerCount = ChaoZhiManager.GetRemainTime((ulong)activityMsg.EndTime);
            isTimer = timerCount > 0;
            m_txtTimer.text = ToolScriptExtend.FormatTime(timerCount);
            
            iconPath = ChaoZhiManager.GetRechargeIcon(TemplateId);
            if (!string.IsNullOrEmpty(iconPath))
            {
                m_imgScore.SetImage(iconPath);
            }
            
            var itemCount = ConfigData.ScoreRewards.Count;
            AutoFitContent(itemCount);
            
            ToolScriptExtend.RecycleOrCreate(m_goItemObj,m_goItemRoot,itemCount);
            for (var i = 0; i < itemCount; i++)
            {
                var child = m_goItemRoot.GetChild(i);
                SetItemInfo(i,child);
            }

            m_sliderProgress.value = 0;

            var num = MsgData.Score.ToString().Length;
            scoreBg.SetSizeWithCurrentAnchors(RectTransform.Axis.Horizontal,num < 4 ?200:260);
            m_txtScore.rectTransform.SetSizeWithCurrentAnchors(RectTransform.Axis.Horizontal,num < 4 ?120:180);
            m_txtScore.text = MsgData.Score.ToString();
            
            CheckMultiLanguage(gameObject);

            CalculateProgress();
            ScrollToTargetIndex();
        }
        
        protected void SetItemInfo(int index,Transform root)
        {
            var rewardScroll = root.Find("Scroll View").GetComponent<ScrollRect>();
            var rewardRoot = root.Find("Scroll View/Viewport/Content");
            var scoreIcon = root.Find("scoreIcon").GetComponent<UIImage>();
            var scoreTxt = root.Find("scoreIcon/scoreTxt").GetComponent<UIText>();
            
            var curData = ConfigData.ScoreRewards[index];
            
            var rewardList = curData.Rewards.ToList();
            ToolScriptExtend.RecycleOrCreate(m_goReward,rewardRoot,rewardList.Count);
            
            ChaoZhiManager.ShowRewardList(m_goReward,rewardRoot, rewardList,0.56f,1.3f);
            
            var score = curData.Score;
            scoreTxt.text =score.ToString();
            var status = CheckItemStatus(curData.Id, score);
            SetItemStatus(root,curData.Id,status);
            if (!string.IsNullOrEmpty(iconPath))
            {
                scoreIcon.SetImage(iconPath);
            }
            
            rewardScroll.GetComponent<RectTransform>().SetSizeWithCurrentAnchors(RectTransform.Axis.Horizontal,rewardList.Count >= 4?556:420);
        }
        
        protected void AutoFitContent(int count)
        {
            var heightSum = itemHeight * count + spacing * (count - 1);
            mainContent.SetSizeWithCurrentAnchors(RectTransform.Axis.Vertical,heightSum);
            sliderRect.SetSizeWithCurrentAnchors(RectTransform.Axis.Horizontal,heightSum-50);
        }
        
        /// <summary>
        /// 设置积分里程碑奖励领取状态
        /// </summary>
        /// <param name="root"></param>
        /// <param name="status">积分里程碑奖励领取状态  0：未解锁 1:可领取 2：已领取</param>
        private void SetItemStatus(Transform root,int id,int status)
        {
            var btnGet = root.Find("m_btnGet").GetComponent<UIButton>();
            var btnLock = root.Find("m_btnLock").GetComponent<UIButton>();
            var finishObj = root.Find("m_goFinish");
            
            btnGet.gameObject.SetActive(status == 1);
            btnLock.gameObject.SetActive(status == 0);
            finishObj.gameObject.SetActive(status == 2);

            if (status == 1)
            {
                ToolScriptExtend.BindBtnLogic(btnGet, () =>
                {
                    var reqData = new ActivityDrawReq
                    {
                        Type = activityMsg.Type,
                        Template = MsgData.Template,
                        LoopTimes = MsgData.LoopTimes,
                        DrawId = id
                    };

                    ChaoZhiManager.C2SActivityDrawReq(reqData, (resp) =>
                    {
                    
                    });
                });
            }
        }

        // 积分里程碑奖励领取状态  0：未解锁 1:可领取 2：已领取
        private int CheckItemStatus(int id,int score)
        {
            if (MsgData.DrawIds.Contains(id))
            {
                return 2;
            }
            return MsgData.Score < score ? 0 : 1;
        }
        
        //计算进度值
        private void CalculateProgress()
        {
            var scoreList = ConfigData.ScoreRewards.Select(x => x.Score).ToList();
            scoreList.Sort((a, b) => (int)a - (int)b);
            var maxValue = scoreList.Last();
            if (MsgData.Score >= maxValue)
            {
                m_sliderProgress.value = 1;
            } 
            else
            {
                var list1 = new List<int>();
                foreach (var score in scoreList)
                {
                    list1.Add((int)score);
                }
                var ratioList = new List<float>();
                var nodeList = new List<int>();
                var lastValue = 0;
                
                var checkCount = scoreList.Count;
                var unit = (1 - startRatio) / (checkCount - 1);
                
                for (var i = 0; i < checkCount; i++)
                {
                    ratioList.Add(i == 0 ? startRatio : unit);
                }
                
                for (var i = 0; i < list1.Count; i++)
                {
                    var curScore = list1[i];
                    var offset = curScore - lastValue;
                    nodeList.Add(offset);
                    lastValue = curScore;
                }
                
                var value = MsgData.Score;
                float ratioSum = 0;
                int offsetSum = 0;
                for (var i = 0; i < list1.Count; i++)
                {
                    var curScore = list1[i];
                    if (value > curScore)
                    {
                        ratioSum += ratioList[i];
                        offsetSum = curScore;
                    }
                    else
                    {
                        var finalOffset = value - offsetSum;
                        ratioSum += (finalOffset*1.0f / nodeList[i]) * ratioList[i];
                        break;
                    }
                }
                m_sliderProgress.value = ratioSum;
            }
        }
        
        protected void ScrollToTargetIndex()
        {
            var contentHeight = mainContent.rect.height;
            var viewHeight = mainScroll.viewport.rect.height;
            if (contentHeight <= viewHeight)
            {
                return;
            }
            var dataList = ConfigData.ScoreRewards.ToList();
            
            dataList.Sort((a,b) =>
            {
                var statusA = CheckItemStatus(a.Id, a.Score);
                var statusB = CheckItemStatus(b.Id, b.Score);
                var flagA = GetCompareFlag(statusA);
                var flagB = GetCompareFlag(statusB);
                return flagA - flagB;
            });
            
            var result = dataList[0];
            var resultScore = result.Score;
            var resultStatus = CheckItemStatus(result.Id,result.Score);
            var index = ConfigData.ScoreRewards.ToList().FindIndex(x => x.Score == resultScore);
            if (resultStatus == 0)
            {
                if (index - 1 < 0)
                {
                    index = 0;
                }
                else
                {
                    index--;
                }
            }
            
            if(index == 0)return;
            
            var heightSum = itemHeight * index + spacing * (index - 1);
            var deadline = contentHeight - viewHeight;
            if (deadline < 0)
            {
                deadline = 0;
            }
            
            if (heightSum > deadline)
            {
                heightSum = deadline;
            }
            
            if (gameObject.activeInHierarchy)
            {
                StartCoroutine(DelayScroll(() =>
                {
                    mainContent.DOAnchorPos(new Vector2(0,heightSum), 0.3f);
                }));
            }
        }
        
        IEnumerator DelayScroll(Action callback)
        {
            yield return delayTime;
            callback?.Invoke();
        }
        
        // 可领(1)>>未达标(2)>>已领取(3)
        private int GetCompareFlag(int status)
        {
            var flag = 3;
            //0:未达标 1：可领取 2：已领取
            if (status == 0)
            {
                flag = 2;
            }
            else if (status == 1)
            {
                flag = 1;
            }
            else if (status == 2)
            {
                flag = 3;
            }
            return flag;
        }
    }
}



using System;
using System.Linq;
using Activity;
using Game.Hotfix.Config;
using Sirenix.OdinInspector;
using UnityEngine;
using UnityEngine.UI;

namespace Game.Hotfix
{
    public partial class UIPioneerRewardForm : UGuiFormEx
    {
        private ChaoZhiData ChaoZhiManager => GameEntry.LogicData.ChaoZhiData;
        
        private int TemplateId;
        private itemid iconId;
        private string iconPath;
        private PushActivityBattlePassConfig ConfigData;
        private PushActivityBattlePassData MsgData;
        protected override void OnInit(object userData)
        {
            base.OnInit(userData);
            m_goPrefab.SetActive(false);
            InitBind();
        }

        protected override void OnOpen(object userData)
        {
            base.OnOpen(userData);
            TemplateId = (int)userData;
            iconId = ChaoZhiManager.GetZLScoreIcon(TemplateId);
            iconPath = ToolScriptExtend.GetItemIcon(iconId);
            ConfigData = ChaoZhiManager.GetZLConfig(TemplateId);
            MsgData = ChaoZhiManager.GetZLMsg(TemplateId);
            
            InitPageView();
        }

        protected override void OnClose(bool isShutdown, object userData)
        {
            base.OnClose(isShutdown, userData);
        }

        public override void OnRefresh(object userData)
        {
            base.OnRefresh(userData);
            var flag = (int)userData;
            if (flag == 1)
            {
                iconId = ChaoZhiManager.GetZLScoreIcon(TemplateId);
                iconPath = ToolScriptExtend.GetItemIcon(iconId);
                ConfigData = ChaoZhiManager.GetZLConfig(TemplateId);
                MsgData = ChaoZhiManager.GetZLMsg(TemplateId);
                InitPageView();
            }
        }

        private void OnBtnCloseClick()
        {
            Close();
        }

        private void InitPageView()
        {
            var list = ChaoZhiManager.GetAllPioneerVipList();
            var root = m_goRoot.transform;
            ToolScriptExtend.RecycleOrCreate(m_goRewardItem,root,list.Count);
            for (var i = 0; i < list.Count; i++)
            {
                var data = list[i];
                var child = root.GetChild(i);
                var vipTxt = child.Find("vip/Text").GetComponent<UIText>();
                var score = child.Find("score").GetComponent<UIText>();
                var lockObj = child.Find("vip/lock");
                var normalReceived = child.Find("normal/received");
                var vipReceived = child.Find("vip/received");
                
                var active = child.Find("active").GetComponent<UIImage>();
                var normalNode = child.Find("normal/node");
                var vipNode = child.Find("vip/node");
                var btnNormal = child.Find("normal").GetComponent<UIButton>();
                var btnVip = child.Find("vip").GetComponent<UIButton>();
                var normalMask = child.Find("normal/mask").GetComponent<UIButton>();
                var vipMask = child.Find("vip/mask").GetComponent<UIButton>();
                var normalEffect = child.Find("normal/effect");
                var vipEffect = child.Find("vip/effect");
                
                score.text = data.score.ToString();
                vipTxt.text = $"VIP{data.vip_level}";
                // 状态：0：积分未达标 1：可领取 2: 已领取 3：上锁
                //免费奖励
                var freeStatus = ChaoZhiManager.CheckRewardStatus(data, MsgData, true);
                normalReceived.gameObject.SetActive(freeStatus == 2);
                normalEffect.gameObject.SetActive(freeStatus == 1);
                
                //vip奖励
                var vipStatus = ChaoZhiManager.CheckRewardStatus(data, MsgData, false);
                vipReceived.gameObject.SetActive(vipStatus == 2);
                
                var vipLevel = GameEntry.LogicData.VipData.GetVipLevel();
                lockObj.gameObject.SetActive(vipLevel < data.vip_level);
                vipEffect.gameObject.SetActive(vipStatus == 1);
               
                active.gameObject.SetActive(freeStatus != 0);
                active.SetImage(iconPath);
                
                var freeReward = data.reward_free.First();
                var vipReward = data.reward_vip.First();

                if (freeReward != null)
                {
                    ChaoZhiManager.LoadReward(normalNode, freeReward, (target) =>
                    {
                        ToolScriptExtend.SetGameObjectGrey(target.transform,freeStatus == 2);
                    },0.65f,1.3f);
                    ToolScriptExtend.BindBtnLogic(btnNormal, () =>
                    {
                        var heroId= GameEntry.LogicData.HeroData.GetHeroIdByChipId(freeReward.item_id);
                        GameEntry.LogicData.HeroData.OpenHeroSingleForm(heroId);
                    });
                    ToolScriptExtend.BindBtnLogic(normalMask, () =>
                    {
                        if (freeStatus == 1)
                        {
                            ReceivedActivityReward();
                        }
                        else
                        {
                            var heroId= GameEntry.LogicData.HeroData.GetHeroIdByChipId(freeReward.item_id);
                            GameEntry.LogicData.HeroData.OpenHeroSingleForm(heroId);
                        }
                    });
                }
                
                if (freeReward != null)
                {
                    ChaoZhiManager.LoadReward(vipNode, vipReward, (target) =>
                    {
                        ToolScriptExtend.SetGameObjectGrey(target.transform,vipStatus == 2);
                    },0.65f,1.3f);
                    ToolScriptExtend.BindBtnLogic(btnVip, () =>
                    {
                        var heroId= GameEntry.LogicData.HeroData.GetHeroIdByChipId(vipReward.item_id);
                        GameEntry.LogicData.HeroData.OpenHeroSingleForm(heroId);
                    });
                    ToolScriptExtend.BindBtnLogic(vipMask, () =>
                    {
                        if (vipStatus == 1)
                        {
                            ReceivedActivityReward();
                        }
                        else
                        {
                            var heroId= GameEntry.LogicData.HeroData.GetHeroIdByChipId(vipReward.item_id);
                            GameEntry.LogicData.HeroData.OpenHeroSingleForm(heroId);
                        }
                    });
                }
            }
            
            CalculateProgress();
            // m_sliderProgress.value = ChaoZhiManager.GetPioneerTargetProgress(MsgData, 0.085f);
        }
        
        private void ReceivedActivityReward()
        {
            if (ChaoZhiManager.GetPioneerActivityTime(out var data))
            {
                var reqData = new ActivityDrawReq
                {
                    Type = data.Type,
                    Template = MsgData.Template,
                    LoopTimes = MsgData.LoopTimes,
                    DrawId = 2
                };
                ChaoZhiManager.C2SActivityDrawReq(reqData, (resp) => { });
            }
        }

        private void CalculateProgress()
        {
            var list = ChaoZhiManager.GetAllPioneerVipList();
            SetSliderValue(0, list[0].score, m_slider1);
            SetSliderValue(list[0].score, list[1].score, m_slider2);
            SetSliderValue(list[1].score, list[2].score, m_slider3);
            SetSliderValue(list[2].score, list[3].score, m_slider4);
            SetSliderValue(list[3].score, list[4].score, m_slider5);
        }
        
        private void SetSliderValue(int minValue,int maxValue,Slider slider)
        {
            if (MsgData.Score >= maxValue)
            {
                slider.value = 1;
            }
            else
            {
                var offset = MsgData.Score - minValue;
                var ratio = offset*1.0f / (maxValue - minValue);
                slider.value = ratio;
            }
        }
    }
}

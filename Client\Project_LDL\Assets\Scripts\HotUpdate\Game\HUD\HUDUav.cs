using Game.Hotfix.Config;
using GameFramework.Event;
using UnityEngine;

namespace Game.Hotfix
{
    public class HUDUav : HUDItemTickAble
    {
        [SerializeField] private UIButton m_btnDisplayRoot;
        [SerializeField] private UIImage m_imgWait;
        [SerializeField] private UIImage m_imgIcon;
        [SerializeField] private UIImage m_imgFull;
        [SerializeField] private Animation m_Animation;
        
        private EL_Building m_Building;
        private BuildingModule m_BuildingModule;
        private float m_TimeElapsed = 0;
        private float m_TimeInterval = 1;
        private UAVModule m_CurUavModule;
        private uav_level uavNextLevelConfig;
        void Start()
        {
            m_btnDisplayRoot.onClick.AddListener(OnBtnUavClick);
        }
        
        protected override void OnInit(object param)
        {
            base.OnInit(param);
            m_Building = Owner as EL_Building;
            m_BuildingModule = m_Building?.GetBuildingModule();
            if (m_BuildingModule == null)
            {
                return;
            }
            
            m_BuildingModule.AddEventListener(BuildingModuleEvent.OnResetPosition,OnResetPosition);
            m_Building.AddEventListener(EL_BuildingEvent.OnMenuVisibleChange, OnMenuVisibleChange);
            GameEntry.Event.Subscribe(UavChangeLevelEventArgs.EventId, OnUpdateEvent);
            GameEntry.Event.Subscribe(ItemChangeEventArgs.EventId, OnItemChange);
            ResetUI();
        }

        private void OnUpdateEvent(object sender, GameEventArgs e)
        {
            if (e is UavChangeLevelEventArgs args)
            {
                ResetUI();
            }
        }

        protected override void OnUnInit()
        {
            m_BuildingModule.RemoveEventListener(BuildingModuleEvent.OnResetPosition, OnResetPosition);
            m_Building.RemoveEventListener(EL_BuildingEvent.OnMenuVisibleChange, OnMenuVisibleChange);
            GameEntry.Event.Unsubscribe(UavChangeLevelEventArgs.EventId, OnUpdateEvent);
            GameEntry.Event.Unsubscribe(ItemChangeEventArgs.EventId, OnItemChange);
            base.OnUnInit();
        }

        private void OnItemChange(object sender, GameEventArgs e)
        {
            var isUpdate = false;
            var args = (ItemChangeEventArgs)e;
            if (args != null && args.changList != null)
            {
                for (var i = 0; i < args.changList.Count; i++)
                {
                    if (args.changList[i] == itemid.itemid_1010041 || args.changList[i] == itemid.itemid_1010042)
                    {
                        isUpdate = true;
                        break;
                    }
                }
            }

            if (isUpdate)
            {
                ResetUI();
            }
        }

        protected void ResetUI()
        {
            m_CurUavModule = GameEntry.LogicData.UAVData.UavModule;
            uavNextLevelConfig = GameEntry.LogicData.UAVData.GetUavLevelConfig((int)m_CurUavModule.Id + 1);
            long oneConsume = uavNextLevelConfig.consume;
            long amount = GameEntry.LogicData.BagData.GetAmountById(itemid.itemid_1010042);
            bool canUp = amount >= oneConsume;
            bool isInUpgradeStage = m_CurUavModule.GetIsInUpgradeStage();
            if (isInUpgradeStage)
            {
                int costPart = uavNextLevelConfig.consume_part;
                bool costPartIsEnough = GameEntry.LogicData.BagData.GetResoureIsEnough(itemid.itemid_1010041,costPart);
                canUp = amount >= oneConsume && costPartIsEnough;
            }
            m_imgFull.gameObject.SetActive(canUp);
        }
        
        protected override void OnUpdate(float dt)
        {
            base.OnUpdate(dt);
            m_TimeElapsed += dt;
            if (m_TimeElapsed > m_TimeInterval)
            {
                m_TimeElapsed -= m_TimeInterval;
            }
           
        }
        
        protected override Vector3 GetOffset()
        {
            if (m_BuildingModule != null)
            {
                return m_BuildingModule.GetOffsetPos();
            }

            return base.GetOffset();
        }

        private void OnMenuVisibleChange(object obj)
        {
            if (obj is bool show)
                gameObject.SetActive(!show);
        }

        private void OnResetPosition(object obj)
        {
            Refresh(Owner.transform);
        }

        private void OnBtnUavClick()
        {
            
            GameEntry.UI.OpenUIForm(EnumUIForm.UIBuildingUAVForm, m_BuildingModule);
        }
    }
}
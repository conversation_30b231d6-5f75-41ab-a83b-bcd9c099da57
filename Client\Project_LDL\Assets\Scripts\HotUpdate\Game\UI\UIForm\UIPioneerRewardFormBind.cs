using UnityEngine;
using UnityEngine.UI;

namespace Game.Hotfix
{
    public partial class UIPioneerRewardForm : UGuiFormEx
    {
        [SerializeField] private Button m_btnClose;

        [SerializeField] private Slider m_sliderProgress;
        [SerializeField] private Slider m_slider1;
        [SerializeField] private Slider m_slider2;
        [SerializeField] private Slider m_slider3;
        [SerializeField] private Slider m_slider4;
        [SerializeField] private Slider m_slider5;

        [SerializeField] private GameObject m_goRoot;
        [SerializeField] private GameObject m_goPrefab;
        [SerializeField] private GameObject m_goRewardItem;

        void InitBind()
        {
            m_btnClose.onClick.AddListener(OnBtnCloseClick);
        }
    }
}

{"skeleton": {"hash": "koTXNOMijiI", "spine": "4.2.43", "x": -713.7, "y": -6.94, "width": 1439, "height": 2665.3, "images": "./images/", "audio": "./audio"}, "bones": [{"name": "root"}, {"name": "bone3", "parent": "root", "x": 4.3, "y": -440.21}, {"name": "bone", "parent": "root", "x": 87.52, "y": 734.53, "color": "ff0000ff", "icon": "star"}, {"name": "tun", "parent": "bone", "length": 413.78, "rotation": -126.25, "x": -129.43, "y": 26.29}, {"name": "tun2", "parent": "root", "length": 418.66, "rotation": -90.83, "x": -286.6, "y": 427.14}, {"name": "tun3", "parent": "bone", "length": 313.87, "rotation": -87.05, "x": 115.27, "y": -50.56}, {"name": "tun4", "parent": "root", "length": 383.07, "rotation": -71.85, "x": 218.97, "y": 370.52}, {"name": "ya<PERSON>i", "parent": "bone", "x": -40.45, "y": 69.05}, {"name": "yaodai2", "parent": "ya<PERSON>i", "length": 396.7, "rotation": -177.66, "x": -70.78, "y": -125.38}, {"name": "yaodai3", "parent": "yaodai2", "length": 231.33, "rotation": 56.61, "x": 420.47, "y": -98.72}, {"name": "shenti", "parent": "bone", "length": 335.4, "rotation": 69.16, "x": 22.25, "y": 71.23}, {"name": "shenti2", "parent": "shenti", "length": 390.93, "rotation": 24.1, "x": 335.4}, {"name": "shenti3", "parent": "shenti2", "length": 383.33, "rotation": 40.46, "x": 392.85, "y": 0.6}, {"name": "shenti4", "parent": "shenti3", "length": 228.66, "rotation": -41.69, "x": 390.51, "y": -1.27}, {"name": "lian", "parent": "shenti4", "length": 476.38, "rotation": -25.55, "x": 234.15, "y": -12.34, "color": "abe323ff"}, {"name": "zuoshou2", "parent": "shenti3", "length": 675.06, "rotation": 121, "x": 475.06, "y": 287.22}, {"name": "zuoshou1", "parent": "zuoshou2", "length": 558.17, "rotation": 172.25, "x": 701.65, "y": 17.75}, {"name": "<PERSON><PERSON><PERSON>", "parent": "zuoshou1", "length": 324.63, "rotation": 18.39, "x": 596.13, "y": -1.44}, {"name": "zuoshou3", "parent": "<PERSON><PERSON><PERSON>", "length": 151.58, "rotation": -8.46, "x": 324.63}, {"name": "zuoshou4", "parent": "zuoshou3", "length": 101.62, "rotation": -18.05, "x": 151.58}, {"name": "zuoshou5", "parent": "<PERSON><PERSON><PERSON>", "length": 55.79, "rotation": -38.89, "x": 360.44, "y": -66.08}, {"name": "zuoshou6", "parent": "zuoshou5", "length": 67.32, "rotation": -79.2, "x": 55.79}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "parent": "zuoshou1", "x": 574.45, "y": 28.9}, {"name": "youshou2", "parent": "shenti3", "length": 592.06, "rotation": 151.13, "x": 88.07, "y": -174.54}, {"name": "youshou3", "parent": "youshou2", "length": 510.1, "rotation": -1.08, "x": 588.89, "y": 17.48}, {"name": "you<PERSON>ou", "parent": "youshou3", "length": 164.45, "rotation": 2.67, "x": 514.55, "y": -7.34}, {"name": "youshou4", "parent": "you<PERSON>ou", "length": 143.6, "rotation": -17.24, "x": 164.45}, {"name": "youshou5", "parent": "youshou4", "length": 145.21, "rotation": -12.07, "x": 143.6}, {"name": "youshou6", "parent": "you<PERSON>ou", "length": 95.46, "rotation": -52.81, "x": 52.42, "y": -108.23}, {"name": "youshou7", "parent": "youshou6", "length": 81.52, "rotation": 6.64, "x": 95.46}, {"name": "<PERSON><PERSON>", "parent": "lian", "x": 377.5, "y": 306.57, "color": "9688ffff", "icon": "rotate"}, {"name": "toufa1", "parent": "lian", "length": 113.25, "rotation": 23.52, "x": 500.83, "y": -47.77, "color": "00ff00ff"}, {"name": "toufa2", "parent": "toufa1", "length": 113.32, "rotation": 92.05, "x": 113.25, "color": "00ff00ff"}, {"name": "toufa3", "parent": "toufa2", "length": 113.41, "rotation": 48.02, "x": 113.32, "color": "00ff00ff"}, {"name": "toufa4", "parent": "toufa3", "length": 120.56, "rotation": 17.24, "x": 113.41, "color": "00ff00ff"}, {"name": "toufa5", "parent": "toufa4", "length": 125.98, "rotation": 3.97, "x": 120.56, "color": "00ff00ff"}, {"name": "toufa6", "parent": "toufa5", "length": 132.24, "rotation": -4.7, "x": 125.98, "color": "00ff00ff"}, {"name": "toufa7", "parent": "toufa6", "length": 126.76, "rotation": -10.51, "x": 132.24, "color": "00ff00ff"}, {"name": "toufa8", "parent": "toufa7", "length": 64.52, "rotation": -23.86, "x": 125.08, "y": -1.13, "color": "00ff00ff"}, {"name": "toufa9", "parent": "lian", "length": 74.82, "rotation": -50.44, "x": 508.81, "y": -50.91, "color": "ff0000ff"}, {"name": "toufa10", "parent": "toufa9", "length": 83.38, "rotation": -44.64, "x": 74.82, "color": "ff0000ff"}, {"name": "toufa11", "parent": "toufa10", "length": 88.17, "rotation": -35.97, "x": 83.38, "color": "ff0000ff"}, {"name": "toufa12", "parent": "toufa11", "length": 103.99, "rotation": -28.53, "x": 88.17, "color": "ff0000ff"}, {"name": "toufa13", "parent": "toufa12", "length": 58.65, "rotation": -18.91, "x": 103.99, "color": "ff0000ff"}, {"name": "toufa14", "parent": "toufa13", "length": 52.19, "rotation": -40.95, "x": 58.65, "color": "ff0000ff"}, {"name": "toufa15", "parent": "lian", "length": 115.29, "rotation": -118.61, "x": 486.75, "y": -142.12, "color": "ff9000ff"}, {"name": "toufa16", "parent": "toufa15", "length": 113.86, "rotation": -18.15, "x": 115.29, "color": "ff9000ff"}, {"name": "toufa17", "parent": "toufa16", "length": 124.3, "rotation": -4.29, "x": 113.86, "color": "ff9000ff"}, {"name": "toufa18", "parent": "toufa17", "length": 122.16, "rotation": -35.17, "x": 124.3, "color": "ff9000ff"}, {"name": "toufa19", "parent": "toufa18", "length": 78.53, "rotation": -51.8, "x": 122.16, "color": "ff9000ff"}, {"name": "toufa20", "parent": "toufa19", "length": 58.68, "rotation": -36.48, "x": 78.53, "color": "ff9000ff"}, {"name": "toufa21", "parent": "lian", "length": 135.9, "rotation": -119.95, "x": 531.73, "y": -113.72, "color": "088000ff"}, {"name": "toufa22", "parent": "toufa21", "length": 153.6, "rotation": -27.44, "x": 135.9, "color": "088000ff"}, {"name": "toufa23", "parent": "toufa22", "length": 121.4, "rotation": -11, "x": 153.6, "color": "088000ff"}, {"name": "toufa24", "parent": "toufa23", "length": 117.66, "rotation": -26.86, "x": 127.54, "y": -1.82, "color": "088000ff"}, {"name": "toufa25", "parent": "toufa24", "length": 101.11, "rotation": -8.1, "x": 117.66, "color": "088000ff"}, {"name": "toufa26", "parent": "toufa25", "length": 113.32, "rotation": 38.92, "x": 101.11, "color": "088000ff"}, {"name": "toufa27", "parent": "lian", "length": 101.44, "rotation": -179.98, "x": 142.02, "y": -323.02, "color": "ff0000ff"}, {"name": "toufa28", "parent": "toufa27", "length": 79.28, "rotation": 17.64, "x": 101.44, "color": "ff0000ff"}, {"name": "toufa29", "parent": "toufa28", "length": 70.64, "rotation": 72.23, "x": 79.28, "color": "ff0000ff"}, {"name": "toufa30", "parent": "toufa29", "length": 59.48, "rotation": 58.94, "x": 70.64, "color": "ff0000ff"}, {"name": "toufa31", "parent": "lian", "length": 177.75, "rotation": 145.16, "x": 485.89, "y": 208, "color": "ff9000ff"}, {"name": "toufa32", "parent": "toufa31", "length": 156.23, "rotation": 21.71, "x": 177.75, "color": "ff9000ff"}, {"name": "toufa33", "parent": "toufa32", "length": 183.75, "rotation": 16.7, "x": 156.23, "color": "ff9000ff"}, {"name": "toufa34", "parent": "toufa33", "length": 122.72, "rotation": 6.25, "x": 184.79, "y": -2.87, "color": "ff9000ff"}, {"name": "toufa35", "parent": "toufa34", "length": 130.87, "rotation": -7.73, "x": 122.72, "color": "ff9000ff"}, {"name": "toufa36", "parent": "toufa35", "length": 125.13, "rotation": 6.66, "x": 130.87, "color": "ff9000ff"}, {"name": "toufa37", "parent": "toufa36", "length": 122, "rotation": -3.26, "x": 125.13, "color": "ff9000ff"}, {"name": "toufa38", "parent": "toufa37", "length": 99.93, "rotation": -37.93, "x": 122, "color": "ff9000ff"}, {"name": "toufa39", "parent": "toufa38", "length": 60.57, "rotation": -53.19, "x": 99.93, "color": "ff9000ff"}, {"name": "toufa40", "parent": "toufa39", "length": 44.57, "rotation": -37.05, "x": 60.57, "color": "ff9000ff"}, {"name": "toufa41", "parent": "lian", "length": 113.16, "rotation": 165.1, "x": -389.23, "y": 279.06, "color": "ff0000ff"}, {"name": "toufa42", "parent": "toufa41", "length": 99.69, "rotation": -15.94, "x": 113.16, "color": "ff0000ff"}, {"name": "toufa43", "parent": "toufa42", "length": 103.06, "rotation": 19.02, "x": 99.69, "color": "ff0000ff"}, {"name": "toufa44", "parent": "toufa43", "length": 92.17, "rotation": 41.05, "x": 103.06, "color": "ff0000ff"}, {"name": "toufa45", "parent": "toufa44", "length": 118.42, "rotation": 45.05, "x": 92.17, "color": "ff0000ff"}, {"name": "toufa46", "parent": "toufa45", "length": 87.51, "rotation": -31.38, "x": 118.42, "color": "ff0000ff"}, {"name": "toufa47", "parent": "toufa46", "length": 85, "rotation": -71.69, "x": 87.51, "color": "ff0000ff"}, {"name": "toufa48", "parent": "toufa47", "length": 75.51, "rotation": -59.06, "x": 85, "color": "ff0000ff"}, {"name": "touying", "parent": "lian", "length": 160.69, "rotation": 153.11, "x": 472.74, "y": 41.35, "color": "0033ffff"}, {"name": "touying2", "parent": "touying", "length": 146.01, "rotation": 30.83, "x": 160.69, "color": "0033ffff"}, {"name": "touying3", "parent": "touying2", "length": 123.09, "rotation": 5.19, "x": 146.01, "color": "0033ffff"}, {"name": "toufa49", "parent": "toufa32", "length": 102.6, "rotation": 12.86, "x": 43.58, "y": -111.37, "color": "ff9000ff"}, {"name": "toufa50", "parent": "toufa49", "length": 84.07, "rotation": 0.4, "x": 102.6, "color": "ff9000ff"}, {"name": "toufa51", "parent": "toufa50", "length": 89.68, "rotation": 16.04, "x": 84.07, "color": "ff9000ff"}, {"name": "toufa52", "parent": "toufa51", "length": 84.13, "rotation": 40.5, "x": 89.68, "color": "ff9000ff"}, {"name": "toufa53", "parent": "toufa52", "length": 43.26, "rotation": 38.72, "x": 84.13, "color": "ff9000ff"}, {"name": "toufa54", "parent": "lian", "length": 131.16, "rotation": 149.99, "x": 479.33, "y": 190.93, "color": "ff0000ff"}, {"name": "toufa55", "parent": "toufa54", "length": 118.9, "rotation": 8.53, "x": 131.16, "color": "ff0000ff"}, {"name": "toufa56", "parent": "toufa55", "length": 110.44, "rotation": 3.37, "x": 118.9, "color": "ff0000ff"}, {"name": "toufa57", "parent": "toufa56", "length": 100.67, "rotation": 1.87, "x": 110.44, "color": "ff0000ff"}, {"name": "toufa58", "parent": "toufa57", "length": 106.02, "rotation": 9.06, "x": 100.67, "color": "ff0000ff"}, {"name": "toufa59", "parent": "toufa58", "length": 96.68, "rotation": 20.96, "x": 106.02, "color": "ff0000ff"}, {"name": "toufa60", "parent": "toufa59", "length": 74.39, "rotation": 44.16, "x": 96.68, "color": "ff0000ff"}, {"name": "toufa61", "parent": "toufa60", "length": 72.1, "rotation": 11.66, "x": 74.39, "color": "ff0000ff"}, {"name": "toufa62", "parent": "lian", "length": 100.53, "rotation": 175.39, "x": 108.85, "y": 291.43, "color": "a500ffff"}, {"name": "toufa63", "parent": "toufa62", "length": 97.09, "rotation": -5.32, "x": 100.53, "color": "a500ffff"}, {"name": "toufa64", "parent": "toufa63", "length": 96.25, "rotation": 1.79, "x": 97.09, "color": "a500ffff"}, {"name": "toufa65", "parent": "toufa64", "length": 107.53, "rotation": -28.24, "x": 96.25, "color": "a500ffff"}, {"name": "toufa66", "parent": "toufa65", "length": 85.72, "rotation": 40.39, "x": 107.53, "color": "a500ffff"}, {"name": "toufa67", "parent": "toufa66", "length": 70.38, "rotation": 62.85, "x": 85.72, "color": "a500ffff"}, {"name": "toufa68", "parent": "lian", "length": 158.69, "rotation": -169.85, "x": -38.62, "y": 273.93, "color": "ff00f9ff"}, {"name": "toufa69", "parent": "toufa68", "length": 122, "rotation": -1.89, "x": 158.69, "color": "ff00f9ff"}, {"name": "toufa70", "parent": "toufa69", "length": 127.21, "rotation": -17.48, "x": 122, "color": "ff00f9ff"}, {"name": "toufa71", "parent": "toufa70", "length": 115.9, "rotation": -16.01, "x": 127.21, "color": "ff00f9ff"}, {"name": "toufa72", "parent": "toufa71", "length": 101.04, "rotation": 31.13, "x": 120.22, "y": 0.28, "color": "ff00f9ff"}, {"name": "toufa73", "parent": "toufa72", "length": 102.01, "rotation": 36.86, "x": 101.04, "color": "ff00f9ff"}, {"name": "toufa74", "parent": "toufa73", "length": 102.96, "rotation": 21.54, "x": 102.01, "color": "ff00f9ff"}, {"name": "toufa75", "parent": "toufa74", "length": 68.7, "rotation": -32.7, "x": 100.49, "y": 4.15, "color": "ff00f9ff"}, {"name": "toufa76", "parent": "lian", "length": 124.05, "rotation": -178.94, "x": 173.13, "y": 239.38, "color": "fffc00ff"}, {"name": "toufa77", "parent": "toufa76", "length": 102.77, "rotation": -0.29, "x": 124.05, "color": "fffc00ff"}, {"name": "toufa78", "parent": "toufa77", "length": 108.83, "rotation": -6.69, "x": 102.77, "color": "fffc00ff"}, {"name": "toufa79", "parent": "toufa78", "length": 103.79, "rotation": 38.77, "x": 108.83, "color": "fffc00ff"}, {"name": "toufa80", "parent": "toufa79", "length": 68.52, "rotation": 42.02, "x": 103.27, "y": 6.28, "color": "fffc00ff"}, {"name": "toufa81", "parent": "toufa80", "length": 93.56, "rotation": 43.35, "x": 68.52, "color": "fffc00ff"}, {"name": "mai", "parent": "lian", "length": 86.86, "rotation": -128.11, "x": 12.51, "y": -136.53, "color": "8180ffff"}, {"name": "mai2", "parent": "mai", "length": 62.69, "rotation": 60.23, "x": 86.86, "color": "8180ffff"}, {"name": "toufa82", "parent": "lian", "length": 137.71, "rotation": -159.03, "x": 278.89, "y": -212.32, "scaleY": 1.2031, "color": "ffe500ff"}, {"name": "toufa83", "parent": "toufa82", "length": 110.75, "rotation": -23.67, "x": 137.71, "scaleY": 0.8669, "color": "ffe500ff"}, {"name": "toufa84", "parent": "toufa83", "length": 98.51, "rotation": -21.93, "x": 110.75, "scaleY": 0.916, "color": "ffe500ff"}, {"name": "toufa85", "parent": "toufa84", "length": 70.93, "rotation": 11.03, "x": 104.08, "y": -2.94, "color": "ffe500ff"}, {"name": "toufa86", "parent": "toufa85", "length": 81.4, "rotation": 71.4, "x": 70.93, "color": "ffe500ff"}, {"name": "toufa87", "parent": "toufa86", "length": 77.84, "rotation": 36.21, "x": 81.4, "color": "ffe500ff"}, {"name": "toufa88", "parent": "lian", "length": 107.8, "rotation": -138.3, "x": 428.3, "y": -162.3, "color": "1eff00ff"}, {"name": "toufa89", "parent": "toufa88", "length": 116.82, "rotation": -12.17, "x": 107.8, "color": "1eff00ff"}, {"name": "toufa90", "parent": "toufa89", "length": 99.12, "rotation": -23.05, "x": 116.82, "color": "1eff00ff"}, {"name": "toufa91", "parent": "toufa90", "length": 115.37, "rotation": -14.97, "x": 99.12, "color": "1eff00ff"}, {"name": "toufa92", "parent": "toufa91", "length": 96.5, "rotation": 28.37, "x": 112.29, "y": -3.73, "color": "1eff00ff"}, {"name": "toufa93", "parent": "toufa92", "length": 104.09, "rotation": 33.59, "x": 96.5, "color": "1eff00ff"}, {"name": "toufa94", "parent": "toufa93", "length": 91.44, "rotation": 8.26, "x": 104.09, "color": "1eff00ff"}, {"name": "toufa95", "parent": "toufa94", "length": 85.66, "rotation": -36.17, "x": 91.44, "color": "1eff00ff"}, {"name": "toufa96", "parent": "lian", "length": 95.95, "rotation": -125.83, "x": -3.46, "y": -257.41, "scaleY": 1.7781, "color": "0022ffff"}, {"name": "toufa97", "parent": "toufa96", "length": 80.71, "rotation": 18.46, "x": 95.95, "scaleX": 0.8679, "scaleY": 0.7296, "color": "0022ffff"}, {"name": "toufa98", "parent": "toufa97", "length": 64.49, "rotation": 13.42, "x": 80.71, "scaleX": 0.8992, "scaleY": 0.7925, "color": "0022ffff"}, {"name": "toufa99", "parent": "toufa98", "length": 70.93, "rotation": 42.56, "x": 64.49, "scaleX": 0.6473, "scaleY": 0.9399, "color": "0022ffff"}, {"name": "toufa100", "parent": "toufa99", "length": 74.15, "rotation": 38.56, "x": 70.93, "color": "0022ffff"}, {"name": "toufa101", "parent": "toufa98", "length": 66.37, "rotation": -33.01, "x": 76.74, "y": -47.97, "color": "0022ffff"}, {"name": "toufa102", "parent": "toufa101", "length": 56.56, "rotation": -51.55, "x": 66.37, "color": "0022ffff"}, {"name": "toufa103", "parent": "lian", "length": 110.2, "rotation": 179.8, "x": 159.27, "y": -187.69, "scaleX": 1.0759, "scaleY": 1.6551, "color": "fd00ffff"}, {"name": "toufa104", "parent": "toufa103", "length": 83.95, "rotation": 13.23, "x": 110.2, "scaleY": 1.1507, "color": "fd00ffff"}, {"name": "toufa105", "parent": "toufa104", "length": 97.16, "rotation": 23.22, "x": 83.95, "scaleY": 1.1019, "color": "fd00ffff"}, {"name": "toufa106", "parent": "toufa105", "length": 90.5, "rotation": 39.83, "x": 97.16, "scaleX": 0.4536, "scaleY": 0.9548, "color": "fd00ffff"}, {"name": "toufa107", "parent": "toufa106", "length": 90.33, "rotation": 13.49, "x": 90.5, "color": "fd00ffff"}, {"name": "toufa108", "parent": "toufa107", "length": 87.56, "rotation": -21.74, "x": 90.33, "color": "fd00ffff"}, {"name": "toufa109", "parent": "toufa108", "length": 100.48, "rotation": -31.1, "x": 87.56, "color": "fd00ffff"}, {"name": "toufa110", "parent": "lian", "length": 112.22, "rotation": 179.4, "x": 129.92, "y": 186.21, "color": "ff007fff"}, {"name": "toufa111", "parent": "toufa110", "length": 95.34, "rotation": 12.09, "x": 112.22, "color": "ff007fff"}, {"name": "toufa112", "parent": "toufa111", "length": 92.03, "rotation": 16.79, "x": 95.34, "color": "ff007fff"}, {"name": "toufa113", "parent": "toufa112", "length": 85.52, "rotation": 19.51, "x": 92.03, "color": "ff007fff"}, {"name": "toufa114", "parent": "toufa113", "length": 97.5, "rotation": 24.54, "x": 85.52, "color": "ff007fff"}, {"name": "toufa115", "parent": "lian", "length": 204.4, "rotation": -166.35, "x": 18.13, "y": 209.59, "color": "18b800ff"}, {"name": "toufa116", "parent": "toufa115", "length": 203.51, "rotation": -8.96, "x": 204.4, "color": "18b800ff"}, {"name": "toufa117", "parent": "toufa116", "length": 187.95, "rotation": 3.98, "x": 203.51, "color": "18b800ff"}, {"name": "toufa118", "parent": "toufa117", "length": 165.03, "rotation": 36.65, "x": 187.95, "color": "18b800ff"}, {"name": "toufa119", "parent": "toufa118", "length": 161.82, "rotation": 8.23, "x": 165.03, "color": "18b800ff"}, {"name": "toufa120", "parent": "toufa119", "length": 143.42, "rotation": -45.98, "x": 161.82, "color": "18b800ff"}, {"name": "yan<PERSON>i", "parent": "lian", "x": 233.98, "y": -4.93, "color": "ff0000ff"}, {"name": "tongkong", "parent": "lian", "x": 243.64, "y": 52.8, "color": "ffffffff"}, {"name": "lian2", "parent": "lian", "x": 274.76, "y": -142.63, "color": "ffffffff"}, {"name": "biyan", "parent": "lian", "x": 302.95, "y": -55.09, "color": "28ff00ff"}, {"name": "erji1", "parent": "lian", "x": 202.08, "y": -207.97, "color": "9688ffff", "icon": "rotate"}, {"name": "meimao", "parent": "lian", "x": 301.49, "y": -155.31, "color": "fff200ff"}, {"name": "toufa121", "parent": "lian", "x": 341.05, "y": -88.42, "color": "002cffff"}, {"name": "target", "parent": "root", "x": -292.67, "y": 8.53, "color": "ff3f00ff", "icon": "ik"}, {"name": "target2", "parent": "root", "x": 338.29, "y": 6.51, "color": "ff3f00ff", "icon": "ik"}, {"name": "shenti5", "parent": "shenti2", "x": 292.14, "y": -298.02}, {"name": "shenti6", "parent": "shenti2", "x": 284.51, "y": 41.84}, {"name": "bone2", "parent": "bone", "x": 793.72, "y": 642.37, "color": "4dff00ff", "icon": "star"}, {"name": "lian3", "parent": "lian", "x": 151.24, "y": -105.96, "color": "0025ffff", "icon": "star"}, {"name": "target3", "parent": "root", "x": 577.39, "y": 467.29, "color": "ff3f00ff", "icon": "ik"}, {"name": "bone4", "parent": "root", "x": -121.37, "y": 33.42, "color": "ff0000ff", "icon": "arrowLeftRight"}, {"name": "meimao2", "parent": "lian", "x": 266.7, "y": 25.65, "color": "fff200ff"}, {"name": "lian4", "parent": "lian", "x": 273.25, "y": -163.38, "color": "e32323ff"}, {"name": "yaodai1", "parent": "ya<PERSON>i", "x": 13.9, "y": 20.3, "scaleX": 2.3, "scaleY": 2.3}, {"name": "shouyinying", "parent": "youshou5", "rotation": 104.16, "x": 48.08, "y": -111.22, "color": "ff3f00ff", "icon": "ik"}, {"name": "target4", "parent": "root", "x": 536.14, "y": 629.91, "color": "ff4000ff", "icon": "ik"}], "slots": [{"name": "toufa19", "bone": "toufa115", "attachment": "toufa19"}, {"name": "toufa18", "bone": "toufa41", "attachment": "toufa18"}, {"name": "toufa17", "bone": "toufa68", "attachment": "toufa17"}, {"name": "youshou3", "bone": "youshou3", "attachment": "youshou3"}, {"name": "youshou2", "bone": "youshou2", "attachment": "youshou2"}, {"name": "tun", "bone": "tun3", "attachment": "tun"}, {"name": "shenti", "bone": "shenti", "attachment": "shenti"}, {"name": "shouyinying", "bone": "shouyinying", "attachment": "shouyinying"}, {"name": "you<PERSON>ou", "bone": "you<PERSON>ou", "attachment": "you<PERSON>ou"}, {"name": "yaodai2", "bone": "yaodai2", "attachment": "yaodai2"}, {"name": "ya<PERSON>i", "bone": "yaodai3", "attachment": "ya<PERSON>i"}, {"name": "yaodai1", "bone": "yaodai1", "attachment": "yaodai1"}, {"name": "bozi", "bone": "shenti4", "attachment": "bozi"}, {"name": "toufa16", "bone": "toufa103", "attachment": "toufa16"}, {"name": "toufa15", "bone": "toufa96", "attachment": "toufa15"}, {"name": "toufa14", "bone": "toufa27", "attachment": "toufa14"}, {"name": "toufa13", "bone": "toufa21", "attachment": "toufa13"}, {"name": "toufa12", "bone": "toufa88", "attachment": "toufa12"}, {"name": "toufa11", "bone": "toufa82", "attachment": "toufa11"}, {"name": "toufa10", "bone": "toufa15", "attachment": "toufa10"}, {"name": "toufa9", "bone": "toufa9", "attachment": "toufa9"}, {"name": "mai", "bone": "mai", "attachment": "mai"}, {"name": "erji1", "bone": "erji1", "attachment": "erji1"}, {"name": "yan<PERSON>i", "bone": "yan<PERSON>i", "attachment": "yan<PERSON>i"}, {"name": "tongkong", "bone": "tongkong", "attachment": "tongkong"}, {"name": "lian", "bone": "lian", "attachment": "lian"}, {"name": "meimao", "bone": "meimao2", "attachment": "meimao"}, {"name": "biyan", "bone": "biyan"}, {"name": "toufa8", "bone": "toufa121", "attachment": "toufa8"}, {"name": "toufa7", "bone": "toufa31", "attachment": "toufa7"}, {"name": "toufa6", "bone": "toufa49", "attachment": "toufa6"}, {"name": "toufa5", "bone": "toufa76", "attachment": "toufa5"}, {"name": "toufa4", "bone": "toufa110", "attachment": "toufa4"}, {"name": "toufa3", "bone": "toufa54", "attachment": "toufa3"}, {"name": "toufa2", "bone": "toufa62", "attachment": "toufa2"}, {"name": "<PERSON><PERSON>", "bone": "<PERSON><PERSON>", "attachment": "<PERSON><PERSON>"}, {"name": "touying", "bone": "touying", "attachment": "touying"}, {"name": "toufa1", "bone": "toufa1", "attachment": "toufa1"}, {"name": "zuoshou2", "bone": "zuoshou2", "attachment": "zuoshou2"}, {"name": "zuoshou1", "bone": "zuoshou1", "attachment": "zuoshou1"}, {"name": "<PERSON><PERSON><PERSON>", "bone": "<PERSON><PERSON><PERSON>", "attachment": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "bone": "<PERSON><PERSON><PERSON><PERSON>", "attachment": "<PERSON><PERSON><PERSON><PERSON>"}], "ik": [{"name": "target", "bones": ["tun2"], "target": "target"}, {"name": "target2", "order": 1, "bones": ["tun4"], "target": "target2"}, {"name": "target3", "order": 3, "bones": ["you<PERSON>ou"], "target": "target3"}, {"name": "target4", "order": 2, "bones": ["youshou3"], "target": "target4"}], "physics": [{"name": "lian", "order": 4, "bone": "lian", "x": 0.5, "y": 0.5, "rotate": 1, "inertia": 0.3, "strength": 80, "damping": 0.85}], "skins": [{"name": "default", "attachments": {"biyan": {"biyan": {"type": "mesh", "uvs": [0.50054, 0.35556, 0.5045, 0.80473, 0.63369, 0.84929, 0.80872, 0.50917, 1, 0.50764, 1, 0.63128, 0.93078, 1, 0.82576, 1, 0.12967, 0.75539, 0.0085, 0.35128, 0.03992, 0.03097, 0.38104, 0.06686], "triangles": [3, 4, 5, 11, 9, 10, 8, 11, 0, 8, 9, 11, 8, 0, 1, 7, 3, 5, 2, 3, 7, 1, 2, 7, 8, 1, 7, 6, 7, 5], "vertices": [-0.24, 25.11, -70.96, -7.12, -60.71, -50, 16.71, -80.44, 42.6, -139.27, 22.99, -147.8, -44.78, -151.93, -58.87, -119.58, -113.39, 111.77, -65.53, 177, -10.51, 189.43, 29.53, 81.85], "hull": 12, "edges": [0, 22, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22], "width": 148, "height": 76}}, "bozi": {"bozi": {"type": "mesh", "uvs": [0.30017, 0, 0.54915, 0.09302, 0.93555, 0.35078, 1, 0.91624, 0.85446, 1, 0.5668, 1, 0.28116, 0.97343, 0, 0.83319, 0, 0.61267, 0.09655, 0.02308, 0.1703, 0], "triangles": [1, 7, 8, 9, 0, 1, 0, 9, 10, 2, 7, 1, 5, 6, 2, 9, 1, 8, 2, 6, 7, 3, 5, 2, 4, 5, 3], "vertices": [1, 13, 403.78, 55.76, 1, 1, 13, 359.71, -16.43, 1, 1, 13, 240.77, -126.66, 1, 1, 13, -11.94, -136.81, 1, 1, 13, -47.75, -92.43, 1, 1, 13, -44.74, -7.34, 1, 1, 13, -29.9, 76.74, 1, 1, 13, 35.55, 157.7, 1, 1, 13, 133.84, 154.22, 1, 1, 13, 395.62, 116.35, 1, 1, 13, 405.14, 94.17, 1], "hull": 11, "edges": [0, 20, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20], "width": 132, "height": 197}}, "erji": {"erji": {"x": -83.04, "y": -103.29, "scaleX": 2.3, "scaleY": 2.3, "rotation": -66.48, "width": 203, "height": 242}}, "erji1": {"erji1": {"x": -5.35, "y": 5.56, "scaleX": 2.2, "scaleY": 2.2, "rotation": -66.48, "width": 42, "height": 75}}, "lian": {"lian": {"type": "mesh", "uvs": [0.72118, 0, 0.96114, 0.08254, 1, 0.32151, 1, 0.3989, 0.88852, 0.74524, 0.66476, 0.97128, 0.61447, 1, 0.50769, 1, 0.23023, 0.84411, 0.00212, 0.57681, 0, 0.53607, 0, 0.35278, 0.06596, 0.16306, 0.43926, 0.00942, 0.56376, 0, 0.18873, 0.34231, 0.26784, 0.2182, 0.40076, 0.12863, 0.57956, 0.09537, 0.77894, 0.11968, 0.90869, 0.19261, 0.95141, 0.33336, 0.94192, 0.48434, 0.90869, 0.59054, 0.85173, 0.71593, 0.78527, 0.80677, 0.69824, 0.89122, 0.57798, 0.93728, 0.41817, 0.87971, 0.28525, 0.77223, 0.19348, 0.59949, 0.16974, 0.48946, 0.35171, 0.55471, 0.31531, 0.41396, 0.3612, 0.31288, 0.46247, 0.24379, 0.62703, 0.22716, 0.77894, 0.26554, 0.87388, 0.39477, 0.85964, 0.49585, 0.82166, 0.6238, 0.75204, 0.76583, 0.73305, 0.81957, 0.71248, 0.77479, 0.67292, 0.77862, 0.63336, 0.75431, 0.52893, 0.77507, 0.43991, 0.75014, 0.45024, 0.80215, 0.5495, 0.84004, 0.61596, 0.86819, 0.69033, 0.86435, 0.37042, 0.66189, 0.58155, 0.69815, 0.57545, 0.58973, 0.60745, 0.51088, 0.62116, 0.40861, 0.6105, 0.33099, 0.64402, 0.28787, 0.74915, 0.31867, 0.78572, 0.40861, 0.76286, 0.50471, 0.76591, 0.5848, 0.77048, 0.66488, 0.72477, 0.72279, 0.64707, 0.72402], "triangles": [57, 36, 58, 59, 56, 57, 59, 57, 58, 56, 59, 60, 61, 56, 60, 34, 57, 56, 55, 56, 61, 60, 59, 38, 54, 32, 55, 58, 36, 37, 63, 62, 40, 41, 64, 63, 47, 52, 53, 43, 64, 41, 44, 45, 65, 64, 43, 44, 49, 46, 45, 42, 44, 43, 50, 45, 44, 50, 49, 45, 50, 44, 51, 48, 47, 46, 42, 43, 41, 23, 40, 39, 40, 24, 41, 39, 38, 22, 29, 52, 47, 48, 46, 49, 42, 51, 44, 46, 53, 45, 46, 47, 53, 40, 62, 39, 41, 63, 40, 39, 60, 38, 52, 32, 54, 38, 59, 37, 32, 33, 55, 45, 53, 65, 64, 44, 65, 61, 60, 39, 56, 33, 34, 62, 61, 39, 52, 54, 53, 59, 58, 37, 57, 35, 36, 57, 34, 35, 55, 33, 56, 55, 61, 62, 54, 55, 62, 54, 65, 53, 63, 54, 62, 54, 63, 65, 64, 65, 63, 18, 14, 0, 19, 0, 1, 18, 0, 19, 18, 17, 13, 18, 13, 14, 17, 12, 13, 20, 19, 1, 16, 12, 17, 36, 18, 19, 35, 17, 18, 35, 18, 36, 16, 17, 35, 37, 36, 19, 37, 19, 20, 34, 16, 35, 20, 1, 2, 21, 37, 20, 2, 21, 20, 15, 12, 16, 15, 16, 34, 11, 12, 15, 21, 38, 37, 21, 2, 3, 22, 38, 21, 33, 15, 34, 3, 22, 21, 31, 11, 15, 31, 15, 33, 10, 11, 31, 31, 33, 32, 9, 10, 31, 23, 39, 22, 30, 31, 32, 9, 31, 30, 30, 32, 52, 24, 40, 23, 4, 24, 23, 3, 4, 23, 3, 23, 22, 29, 30, 52, 29, 47, 48, 25, 41, 24, 25, 24, 4, 42, 41, 25, 8, 30, 29, 9, 30, 8, 28, 29, 48, 28, 48, 49, 8, 29, 28, 26, 51, 42, 26, 42, 25, 27, 49, 50, 28, 49, 27, 26, 27, 50, 26, 50, 51, 5, 27, 26, 5, 26, 25, 5, 25, 4, 7, 28, 27, 8, 28, 7, 6, 27, 5, 7, 27, 6], "vertices": [2, 14, 528.9, 20.41, 0.80286, 168, 377.66, 126.37, 0.19714, 2, 14, 529.91, -101.88, 0.80286, 168, 378.67, 4.08, 0.19714, 2, 14, 410.09, -173.9, 0.80286, 168, 258.85, -67.94, 0.19714, 2, 14, 368.93, -191.81, 0.80286, 168, 217.69, -85.85, 0.19714, 2, 14, 163.88, -224.03, 0.80286, 168, 12.64, -118.07, 0.19714, 2, 14, 1.79, -180.12, 0.80286, 168, -149.45, -74.16, 0.19714, 2, 14, -22.89, -165.14, 0.80286, 168, -174.13, -59.18, 0.19714, 2, 14, -42.88, -119.21, 0.80286, 168, -194.12, -13.25, 0.19714, 2, 14, -11.9, 36.19, 0.80286, 168, -163.14, 142.15, 0.19714, 2, 14, 87.57, 196.14, 0.80286, 168, -63.67, 302.1, 0.19714, 2, 14, 108.84, 206.48, 0.80286, 168, -42.41, 312.44, 0.19714, 2, 14, 206.32, 248.91, 0.80286, 168, 55.07, 354.87, 0.19714, 2, 14, 319.56, 264.45, 0.80286, 168, 168.31, 370.41, 0.19714, 2, 14, 471.13, 139.47, 0.80286, 168, 319.89, 245.43, 0.19714, 2, 14, 499.44, 88.11, 0.80286, 168, 348.2, 194.07, 0.19714, 2, 14, 247.2, 170.17, 0.65594, 168, 95.96, 276.13, 0.34406, 2, 14, 328.02, 164.87, 0.67544, 168, 176.77, 270.83, 0.32456, 2, 14, 400.52, 128.44, 0.69317, 168, 249.28, 234.4, 0.30683, 2, 14, 451.68, 59.24, 0.69642, 168, 300.44, 165.2, 0.30358, 2, 14, 476.06, -32.12, 0.65313, 168, 324.82, 73.84, 0.34687, 2, 14, 461.56, -104.8, 0.62701, 168, 310.32, 1.16, 0.37299, 2, 14, 394.7, -155.75, 0.62052, 168, 243.46, -49.79, 0.37948, 2, 14, 312.63, -186.61, 0.61542, 168, 161.39, -80.65, 0.38458, 2, 14, 249.93, -196.9, 0.61382, 168, 98.69, -90.94, 0.38618, 2, 14, 172.58, -201.42, 0.61198, 168, 21.34, -95.46, 0.38802, 2, 14, 111.83, -193.87, 0.6189, 168, -39.41, -87.91, 0.3811, 2, 14, 50.64, -175.98, 0.64131, 168, -100.61, -70.02, 0.35869, 2, 14, 3.63, -134.93, 0.67493, 168, -147.61, -28.97, 0.32507, 2, 14, 4.34, -52.88, 0.6383, 168, -146.9, 53.08, 0.3617, 2, 14, 36.63, 29.16, 0.61676, 168, -114.61, 135.12, 0.38324, 2, 14, 111.32, 108.6, 0.62738, 168, -39.93, 214.56, 0.37262, 2, 14, 165.4, 144.28, 0.637, 168, 14.15, 250.24, 0.363, 2, 14, 164.75, 50.92, 0.44789, 168, 13.5, 156.88, 0.55211, 2, 14, 232.79, 99.15, 0.46655, 168, 81.55, 205.11, 0.53345, 2, 14, 295.13, 102.81, 0.48149, 168, 143.89, 208.77, 0.51851, 2, 14, 350.83, 75.25, 0.4892, 168, 199.59, 181.21, 0.5108, 2, 14, 390.47, 8.33, 0.47623, 168, 239.23, 114.29, 0.52377, 2, 14, 398.49, -65.88, 0.46076, 168, 247.25, 40.08, 0.53924, 2, 14, 347.53, -136.62, 0.44425, 168, 196.29, -30.66, 0.55575, 2, 14, 291.11, -153.89, 0.43824, 168, 139.86, -47.93, 0.56176, 2, 14, 215.95, -167.17, 0.43374, 168, 64.71, -61.21, 0.56626, 2, 14, 127.39, -170.1, 0.43681, 168, -23.85, -64.14, 0.56319, 2, 14, 95.26, -174.37, 0.44546, 168, -55.99, -68.41, 0.55454, 2, 14, 115.22, -155.16, 0.32744, 168, -36.02, -49.2, 0.67256, 2, 14, 105.78, -139.04, 0.32774, 168, -45.46, -33.08, 0.67226, 2, 14, 111.3, -116.4, 0.32862, 168, -39.94, -10.44, 0.67138, 2, 14, 80.72, -76.29, 0.3306, 168, -70.52, 29.67, 0.6694, 2, 14, 77.32, -32.24, 0.44044, 168, -73.92, 73.72, 0.55956, 2, 14, 51.59, -48.72, 0.44582, 168, -99.65, 57.24, 0.55418, 2, 14, 50.02, -100.18, 0.46032, 168, -101.22, 5.78, 0.53968, 2, 14, 47.48, -135.27, 0.46143, 168, -103.76, -29.31, 0.53857, 2, 14, 63.44, -166.36, 0.45518, 168, -87.8, -60.4, 0.54482, 2, 14, 111.25, 18.07, 0.44278, 168, -39.99, 124.03, 0.55722, 2, 14, 131.48, -81.12, 0.15118, 168, -19.77, 24.84, 0.84882, 2, 14, 188, -53.4, 0.15422, 168, 36.75, 52.56, 0.84578, 2, 14, 235.92, -48.91, 0.1604, 168, 84.68, 57.05, 0.8396, 2, 14, 292.87, -31.14, 0.17025, 168, 141.63, 74.82, 0.82975, 2, 14, 332.16, -8.59, 0.18084, 168, 180.91, 97.37, 0.81916, 2, 14, 361.36, -13.03, 0.18002, 168, 210.12, 92.93, 0.81998, 2, 14, 364.66, -65.37, 0.16927, 168, 213.41, 40.59, 0.83073, 2, 14, 323.67, -101.91, 0.15965, 168, 172.43, 4.05, 0.84035, 2, 14, 268.28, -114.32, 0.15317, 168, 117.04, -8.36, 0.84683, 2, 14, 226.26, -134.17, 0.14871, 168, 75.02, -28.21, 0.85129, 2, 14, 184.53, -154.67, 0.14787, 168, 33.28, -48.71, 0.85213, 2, 14, 145.18, -148.41, 0.14943, 168, -6.07, -42.45, 0.85057, 2, 14, 129.98, -115.28, 0.15067, 168, -21.26, -9.32, 0.84933], "hull": 15, "edges": [0, 28, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 30, 64, 66, 66, 68, 68, 70, 70, 72, 72, 74, 74, 76, 76, 78, 78, 80, 80, 82, 82, 84, 84, 86, 86, 88, 88, 90, 90, 92, 92, 94, 94, 96, 96, 98, 98, 100, 100, 102, 102, 84, 94, 104, 104, 64, 106, 108, 108, 110, 110, 112, 112, 114, 114, 116, 116, 118, 118, 120, 120, 122, 122, 124, 124, 126, 126, 128, 128, 130, 130, 106], "width": 207, "height": 256}}, "mai": {"mai": {"type": "mesh", "uvs": [0.17118, 0, 0.38336, 0.62521, 0.58292, 0.76079, 0.80028, 0.57532, 0.99988, 0.68287, 1, 0.72405, 1, 0.94976, 0.83993, 1, 0.60593, 1, 0.16729, 0.75744, 0, 0.27486, 0, 0.07288, 0.0983, 0], "triangles": [5, 3, 4, 7, 8, 2, 8, 9, 2, 5, 7, 3, 6, 7, 5, 7, 2, 3, 10, 11, 12, 10, 0, 1, 0, 10, 12, 9, 10, 1, 9, 1, 2], "vertices": [1, 115, -7.99, 17.01, 1, 2, 115, 67.28, 7.72, 0.9721, 116, -3.03, 20.83, 0.0279, 2, 115, 93.08, 23.26, 0.00451, 116, 23.28, 6.15, 0.99549, 1, 116, 51.02, 27.79, 1, 1, 116, 77.26, 16.27, 1, 1, 116, 77.39, 11.62, 1, 1, 116, 78.01, -13.88, 1, 1, 116, 57.34, -20.06, 1, 1, 116, 26.93, -20.8, 1, 1, 115, 67.08, -24.1, 1, 1, 115, 8.76, -17.32, 1, 1, 115, -11.32, -6.48, 1, 1, 115, -12.5, 8.68, 1], "hull": 13, "edges": [0, 24, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24], "width": 60, "height": 52}}, "meimao": {"meimao": {"type": "mesh", "uvs": [0.32479, 0.38161, 0.46037, 0.86551, 0.90637, 0.50233, 1, 0.67272, 1, 0.75638, 0.65943, 0.99393, 0.46075, 0.88209, 0.38814, 0.84121, 0.01771, 0.2814, 0, 0.12646, 0, 0.00425, 0.0327, 0.00837], "triangles": [5, 6, 1, 2, 3, 4, 2, 5, 1, 5, 2, 4, 9, 10, 11, 8, 9, 11, 8, 11, 0, 1, 7, 0, 8, 0, 7, 7, 1, 6], "vertices": [1, 171, 6.36, -20.69, 1, 2, 171, -35.61, -85.53, 0.53143, 161, -70.4, 95.43, 0.46857, 1, 161, 29.95, -14.11, 1, 1, 161, 20.94, -50.2, 1, 1, 161, 10.74, -54.64, 1, 1, 161, -61.04, 31.12, 1, 2, 171, -37.58, -86.52, 0.53143, 161, -72.38, 94.44, 0.46857, 1, 171, -41.73, -63.38, 1, 1, 171, -20.02, 73.33, 1, 1, 171, -3.35, 86.67, 1, 1, 171, 11.56, 93.15, 1, 1, 171, 15.16, 83.49, 1], "hull": 12, "edges": [0, 22, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 14, 16, 16, 18, 18, 20, 20, 22, 10, 12, 12, 14, 2, 12], "width": 141, "height": 60}}, "shenti": {"shenti": {"type": "mesh", "uvs": [0.5006, 0, 0.66854, 0.04784, 0.8092, 0.20391, 0.83548, 0.23031, 0.86398, 0.25507, 0.9326, 0.29342, 0.97907, 0.33548, 0.9964, 0.36655, 1, 0.39803, 1, 0.43192, 1, 0.4691, 0.89139, 0.57229, 0.89229, 0.59282, 0.82035, 0.95715, 0.80819, 1, 0.72116, 1, 0.52957, 0.97117, 0.36904, 0.9054, 0.27614, 0.81846, 0.3611, 0.7568, 0.31356, 0.64406, 0.03548, 0.38415, 0, 0.24405, 0, 0.16167, 0.02441, 0.06012, 0.2285, 0, 0.76617, 0.22558, 0.63421, 0.23047, 0.59605, 0.26597, 0.66546, 0.33025, 0.69762, 0.38822, 0.7085, 0.49871, 0.69217, 0.56043, 0.77134, 0.57884, 0.58425, 0.60871, 0.46127, 0.61659, 0.34252, 0.58373, 0.28032, 0.49171, 0.26477, 0.38128, 0.31142, 0.29584, 0.40755, 0.24983, 0.51216, 0.25246, 0.81396, 0.24115, 0.59891, 0.39717, 0.95395, 0.40875], "triangles": [42, 2, 3, 26, 2, 42, 42, 3, 4, 44, 5, 6, 44, 6, 7, 44, 7, 8, 9, 44, 8, 33, 31, 44, 26, 42, 29, 44, 30, 4, 44, 4, 5, 10, 11, 44, 10, 44, 9, 44, 31, 30, 4, 30, 42, 11, 33, 44, 28, 27, 29, 43, 28, 29, 43, 29, 30, 40, 37, 38, 43, 30, 31, 31, 34, 43, 43, 36, 37, 40, 41, 43, 43, 41, 28, 37, 40, 43, 34, 35, 43, 35, 36, 43, 15, 16, 34, 13, 15, 34, 14, 15, 13, 26, 1, 2, 27, 0, 1, 27, 1, 26, 25, 22, 23, 40, 25, 0, 41, 40, 0, 27, 41, 0, 28, 41, 27, 39, 25, 40, 25, 23, 24, 39, 22, 25, 38, 22, 39, 21, 22, 38, 39, 40, 38, 21, 38, 37, 21, 37, 20, 29, 27, 26, 42, 30, 29, 32, 31, 33, 33, 11, 12, 32, 34, 31, 20, 37, 36, 35, 20, 36, 13, 33, 12, 34, 32, 33, 34, 33, 13, 19, 20, 35, 16, 17, 19, 18, 19, 17, 34, 19, 35, 34, 16, 19], "vertices": [1, 12, 318.63, -113.41, 1, 2, 12, 168.14, -197.7, 0.90857, 167, -648.43, 392.66, 0.09143, 4, 11, 476.67, -169.26, 0.20265, 12, -46.44, -183.63, 0.31528, 167, -510.3, 227.84, 0.05313, 165, 184.53, 128.77, 0.42893, 4, 11, 447.36, -193.43, 0.38854, 12, -84.42, -183.02, 0.10911, 167, -484.49, 199.96, 0.13676, 165, 155.22, 104.59, 0.36559, 4, 11, 419.67, -219.88, 0.30895, 12, -122.66, -185.17, 0.05335, 167, -456.51, 173.82, 0.09669, 165, 127.53, 78.14, 0.54101, 4, 11, 375.41, -284.86, 0.23417, 12, -198.5, -205.89, 0.03557, 167, -389.12, 133.32, 0.11072, 165, 83.26, 13.16, 0.61954, 4, 11, 328.46, -327.89, 0.33077, 12, -262.14, -208.17, 0.00456, 167, -343.49, 88.91, 0.10078, 165, 36.32, -29.86, 0.56389, 4, 11, 294.74, -343.02, 0.33255, 12, -297.62, -197.8, 0.00293, 167, -326.47, 56.09, 0.10075, 165, 2.6, -44.99, 0.56376, 4, 11, 261.35, -344.65, 0.33433, 12, -324.09, -177.38, 0.00131, 167, -322.94, 22.85, 0.10073, 165, -30.8, -46.63, 0.56363, 4, 11, 225.62, -342.61, 0.30357, 12, -349.95, -152.65, 0.00065, 167, -322.94, -12.93, 0.10549, 165, -66.52, -44.59, 0.59028, 3, 11, 186.42, -340.38, 0.27281, 167, -322.94, -52.2, 0.11026, 165, -105.73, -42.36, 0.61693, 4, 10, 504.78, -173.67, 0.03559, 11, 83.7, -227.7, 0.48498, 167, -429.59, -161.17, 0.14295, 165, -208.45, 70.33, 0.33648, 3, 10, 484.82, -182.21, 0.09765, 11, 62, -227.34, 0.74569, 167, -428.71, -182.85, 0.15665, 3, 10, 100.13, -253.05, 0.30891, 2, 294.37, 74.79, 0.59966, 167, -499.35, -567.58, 0.09143, 3, 10, 53.59, -257.99, 0.30891, 2, 282.42, 29.54, 0.59966, 167, -511.3, -612.83, 0.09143, 2, 10, 23.19, -178.12, 0.34, 2, 196.96, 29.54, 0.66, 2, 10, -15.28, 8.55, 0.34, 2, 8.82, 59.98, 0.66, 4, 10, -6.46, 180.58, 0.33482, 11, -238.32, 304.43, 0.0045, 12, -283.12, 640.73, 0.00067, 2, -148.82, 129.43, 0.66, 5, 10, 46.9, 298.5, 0.29535, 11, -141.46, 390.29, 0.01133, 12, -153.71, 643.21, 0.00223, 2, -240.05, 221.25, 0.59966, 167, -1033.77, -421.12, 0.09143, 3, 10, 137.42, 243.69, 0.83104, 11, -81.2, 303.29, 0.13252, 12, -164.32, 537.91, 0.03644, 4, 10, 232.09, 329.68, 0.37088, 11, 40.32, 343.12, 0.35457, 12, -46, 489.37, 0.18313, 167, -997.02, -236.95, 0.09143, 5, 10, 391.45, 682.53, 0.01486, 11, 329.88, 600.14, 0.11119, 12, 341.1, 497.06, 0.56187, 167, -1270.1, 37.51, 0.06922, 15, 248.86, 6.77, 0.24286, 5, 10, 517.32, 767.72, 0.00315, 11, 479.56, 626.51, 0.0542, 12, 472.1, 419.99, 0.63057, 167, -1304.94, 185.45, 0.06922, 15, 115.33, -65.84, 0.24286, 5, 10, 598.62, 798.67, 0.00072, 11, 566.42, 621.56, 0.03362, 12, 534.97, 359.87, 0.7081, 167, -1304.94, 272.44, 0.07471, 15, 31.42, -88.77, 0.18286, 4, 11, 672.12, 591.52, 0.01676, 12, 595.92, 268.43, 0.72567, 167, -1280.97, 379.69, 0.07471, 15, -78.35, -93.92, 0.18286, 2, 11, 724.1, 387.82, 0.00277, 12, 503.3, 79.71, 0.99723, 4, 11, 456.22, -125.76, 0.17532, 12, -33.77, -137.27, 0.3411, 167, -552.56, 204.96, 0.07862, 165, 164.08, 172.26, 0.40496, 4, 11, 458.44, 3.9, 0.24, 12, 52.04, -40.05, 0.32071, 167, -682.14, 199.79, 0.11881, 165, 166.3, 301.92, 0.32048, 5, 10, 703.91, 212.46, 1e-05, 11, 423.15, 43.45, 0.01724, 12, 50.86, 12.95, 0.42786, 166, 138.64, 1.61, 0.43143, 165, 131.01, 341.48, 0.12346, 5, 10, 664.72, 124.61, 5e-05, 11, 351.5, -20.73, 0.4341, 12, -45.31, 10.59, 0.01096, 166, 66.99, -62.58, 0.43143, 165, 59.36, 277.29, 0.12346, 3, 11, 288.59, -48.78, 0.44511, 166, 4.08, -90.62, 0.43143, 165, -3.56, 249.24, 0.12346, 3, 11, 171.49, -52.81, 0.44511, 166, -113.02, -94.66, 0.43143, 165, -120.66, 245.21, 0.12346, 3, 11, 107.33, -33.1, 0.44511, 166, -177.18, -74.94, 0.43143, 165, -184.81, 264.93, 0.12346, 4, 10, 456.37, -65.95, 0.0292, 11, 83.5, -109.6, 0.55784, 167, -547.48, -168.08, 0.07448, 165, -208.64, 188.42, 0.33847, 4, 10, 361.54, 94.53, 0.12956, 11, 62.47, 75.61, 0.42284, 12, -202.73, 271.45, 0.01045, 166, -222.04, 33.77, 0.43714, 4, 10, 310.79, 204.43, 0.23195, 11, 61.02, 196.66, 0.27486, 12, -125.28, 364.49, 0.05605, 166, -223.49, 154.82, 0.43714, 4, 10, 301.74, 325.76, 0.17512, 11, 102.31, 311.1, 0.26097, 12, -19.61, 424.79, 0.15248, 166, -182.2, 269.26, 0.41143, 4, 10, 370.83, 417.41, 0.07685, 11, 202.8, 366.56, 0.22974, 12, 92.83, 401.78, 0.28198, 166, -81.71, 324.71, 0.41143, 4, 10, 474.38, 473.17, 0.02825, 11, 320.08, 375.17, 0.15897, 12, 187.67, 332.22, 0.40135, 166, 35.58, 333.32, 0.41143, 4, 10, 575, 462.45, 0.0117, 11, 407.56, 324.3, 0.1188, 12, 221.22, 236.75, 0.54094, 166, 123.06, 282.45, 0.32857, 4, 10, 653.99, 391.52, 0.00296, 11, 450.7, 227.29, 0.06412, 12, 191.1, 134.95, 0.49577, 166, 166.19, 185.44, 0.43714, 4, 10, 687.94, 294.52, 0.00062, 11, 442.08, 124.89, 0.04853, 12, 118.1, 62.62, 0.51371, 166, 157.57, 83.04, 0.43714, 4, 11, 437.14, -171.69, 0.26532, 12, -78.09, -159.83, 0.1638, 167, -505.62, 188.52, 0.07888, 165, 145, 126.34, 0.49199, 1, 166, 0.15, 6.69, 1, 1, 165, -39.52, -0.84, 1], "hull": 26, "edges": [0, 50, 0, 2, 2, 4, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 22, 64, 68, 68, 70, 70, 72, 72, 74, 74, 76, 76, 78, 78, 80, 80, 82, 82, 56, 8, 84, 84, 52, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20], "width": 430, "height": 463}}, "shoubiao": {"shoubiao": {"x": -1.14, "y": -4.02, "rotation": -66.96, "width": 103, "height": 81}}, "shouyinying": {"shouyinying": {"type": "mesh", "uvs": [0.37515, 0.24516, 0.46695, 0.41568, 0.55876, 0.5862, 0.77444, 0.51194, 0.76278, 0.77322, 1, 0.66321, 1, 1, 0.75112, 1, 0.71323, 0.78972, 0.61414, 0.86948, 0.41303, 0.78422, 0.50339, 0.61645, 0.39409, 0.48169, 0.2848, 0.34692, 0, 0.40743, 0, 0, 0.5, 0], "triangles": [11, 12, 2, 7, 4, 6, 4, 5, 6, 7, 8, 4, 10, 11, 9, 9, 11, 8, 8, 11, 2, 8, 2, 4, 4, 2, 3, 13, 0, 1, 14, 15, 13, 13, 15, 0, 0, 15, 16, 12, 1, 2, 12, 13, 1], "vertices": [1, 29, 52.62, 72.74, 1, 1, 174, -3.71, 28.94, 1, 1, 27, 68.01, -80.84, 1, 1, 27, 28.23, -22.33, 1, 1, 27, 111.84, -4.93, 1, 1, 27, 59.12, 57.17, 1, 1, 27, 165.77, 84.08, 1, 1, 27, 184.54, 9.71, 1, 1, 27, 120.81, -18.42, 1, 1, 27, 153.54, -41.66, 1, 1, 27, 141.7, -108.57, 1, 1, 27, 81.76, -94.97, 1, 1, 174, -26.17, 7.38, 1, 1, 29, 95.46, 66.02, 1, 1, 29, 157.64, 0.99, 1, 1, 29, 43.62, -67.61, 1, 1, 29, -35.82, 64.44, 1], "hull": 17, "edges": [28, 30, 28, 26, 26, 0, 30, 32, 0, 32, 22, 20, 20, 18, 18, 16, 12, 14, 16, 14, 12, 10, 10, 8, 8, 6, 6, 4, 22, 24, 24, 26, 0, 2, 2, 4, 24, 2], "width": 134, "height": 142}}, "tongkong": {"tongkong": {"type": "mesh", "uvs": [0.18528, 0, 0.24493, 0.14905, 0.24413, 0.36449, 0.53292, 0.53701, 0.82172, 0.70953, 0.85521, 0.49453, 1, 0.49439, 1, 0.92386, 0.93483, 1, 0.86615, 1, 0.82231, 0.92563, 0.8217, 0.71031, 0.24421, 0.36504, 0.21748, 0.59779, 0.05241, 0.59914, 0, 0.42109, 0, 0.20948, 0.02681, 0], "triangles": [11, 3, 4, 11, 12, 3, 4, 5, 6, 7, 4, 6, 11, 4, 7, 9, 10, 11, 11, 8, 9, 7, 8, 11, 2, 0, 1, 3, 12, 2, 2, 15, 16, 17, 0, 16, 13, 2, 12, 2, 16, 0, 13, 14, 2, 14, 15, 2], "vertices": [2, 157, 34.42, -4.8, 0.99999, 158, 3.29, 190.64, 1e-05, 2, 157, 25.55, -24.4, 0.99994, 158, -5.57, 171.04, 6e-05, 2, 157, 4.34, -33.42, 0.99948, 158, -26.78, 162.02, 0.00052, 2, 157, 15.3, -104.87, 0.44293, 158, -15.82, 90.57, 0.55707, 2, 157, 26.26, -176.32, 0.0005, 158, -4.86, 19.12, 0.9995, 2, 157, 50.59, -174.57, 1e-05, 158, 19.47, 20.87, 0.99999, 1, 158, 33.46, -11.26, 1, 1, 158, -8.67, -29.59, 1, 1, 158, -22.44, -18.38, 1, 2, 157, 2.05, -198.58, 0, 158, -29.07, -3.14, 1, 2, 157, 5.12, -185.68, 1e-05, 158, -26, 9.76, 0.99999, 2, 157, 26.18, -176.35, 0.00046, 158, -4.94, 19.09, 0.99954, 2, 157, 4.29, -33.46, 0.99933, 158, -26.83, 161.98, 0.00067, 2, 157, -21.13, -37.47, 0.99993, 158, -52.25, 157.97, 7e-05, 2, 157, -37.2, -0.89, 1, 158, -68.32, 194.54, 0, 1, 157, -24.79, 18.34, 1, 1, 157, -4.03, 27.37, 1, 1, 157, 19.11, 30.37, 1], "hull": 18, "edges": [0, 34, 0, 2, 2, 4, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 4, 6, 6, 8], "width": 109, "height": 49}}, "toufa1": {"toufa1": {"type": "mesh", "uvs": [0.83884, 0, 1, 0.0602, 1, 0.14931, 0.95415, 0.25176, 0.81571, 0.33089, 0.65958, 0.22308, 0.59407, 0.29606, 0.52857, 0.36904, 0.48474, 0.4489, 0.43967, 0.53102, 0.39459, 0.61315, 0.33474, 0.72221, 0.27876, 0.82419, 0.21906, 0.907, 0.13986, 0.95345, 0.06067, 0.9999, 0, 0.99885, 0, 0.99539, 0.07938, 0.92431, 0.15876, 0.85322, 0.22196, 0.70733, 0.24729, 0.57904, 0.26381, 0.49535, 0.28032, 0.41166, 0.29383, 0.34322, 0.35119, 0.21825, 0.37925, 0.15709, 0.56817, 0.01454, 0.69461, 0], "triangles": [15, 18, 14, 18, 15, 17, 15, 16, 17, 13, 14, 19, 14, 18, 19, 13, 19, 12, 19, 20, 12, 12, 20, 11, 10, 11, 21, 11, 20, 21, 9, 10, 22, 10, 21, 22, 8, 9, 23, 9, 22, 23, 23, 24, 8, 8, 24, 7, 24, 25, 7, 7, 25, 6, 25, 26, 6, 6, 26, 5, 26, 27, 5, 5, 28, 0, 5, 27, 28, 4, 5, 3, 3, 5, 0, 3, 0, 2, 0, 1, 2], "vertices": [2, 31, 184.95, -8.16, 0.13571, 32, -10.72, -71.37, 0.86429, 2, 31, 145.46, -97.45, 0.84763, 32, -98.53, -28.72, 0.15237, 2, 31, 87.01, -97.45, 0.97735, 32, -96.45, 29.7, 0.02265, 1, 31, 19.8, -72.04, 1, 2, 31, -32.11, 4.65, 0.99998, 33, 38.64, 173.98, 2e-05, 4, 31, 38.61, 91.14, 0.21886, 32, 93.75, 71.34, 0.2498, 33, 39.94, 62.26, 0.51693, 34, -51.71, 81.24, 0.01441, 4, 31, -9.26, 127.44, 0.00854, 32, 131.73, 117.89, 0.00911, 33, 99.94, 65.17, 0.54403, 34, 6.46, 66.23, 0.43831, 3, 33, 159.95, 68.08, 0.02993, 34, 64.62, 51.22, 0.96699, 35, -52.26, 54.97, 0.00308, 2, 34, 122.32, 49.03, 0.47471, 35, 5.15, 48.79, 0.52529, 1, 35, 64.19, 42.44, 1, 2, 35, 123.23, 36.09, 0.63463, 36, -5.7, 35.74, 0.36537, 1, 36, 73.13, 33.76, 1, 2, 36, 146.84, 31.91, 0.20102, 37, 8.54, 34.04, 0.79898, 1, 37, 72.07, 36.92, 1, 1, 37, 121.84, 17.54, 1, 2, 37, 171.62, -1.84, 0.02482, 38, 42.85, 18.18, 0.97518, 2, 37, 189.82, -30.11, 1e-05, 38, 70.92, -0.32, 0.99999, 2, 37, 187.94, -31.37, 1e-05, 38, 69.72, -2.23, 0.99999, 1, 38, 7.66, -18.27, 1, 1, 37, 61.45, -10.49, 1, 1, 36, 89.01, -27.45, 1, 2, 35, 128.24, -48.38, 0.51785, 36, 6.22, -48.04, 0.48215, 3, 34, 197.66, -52.13, 0.00876, 35, 73.31, -57.34, 0.98416, 36, -47.8, -61.47, 0.00708, 2, 34, 143.48, -64.87, 0.32399, 35, 18.37, -66.3, 0.67601, 2, 34, 99.17, -75.29, 0.8002, 35, -26.55, -73.63, 0.1998, 2, 33, 147.18, -70.77, 0.28653, 34, 11.28, -77.6, 0.71347, 2, 33, 106.44, -84.6, 0.68873, 34, -31.73, -78.74, 0.31127, 2, 32, 139.48, -67.19, 0.53912, 33, -32.45, -64.38, 0.46088, 2, 32, 69.13, -74.22, 0.99073, 33, -84.73, -16.8, 0.00927], "hull": 29, "edges": [0, 56, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 30, 32, 32, 34, 38, 40, 52, 54, 54, 56, 24, 26, 26, 28, 28, 30, 34, 36, 36, 38, 22, 24, 20, 22, 40, 42, 14, 16, 46, 48, 42, 44, 44, 46, 16, 18, 18, 20, 48, 50, 50, 52, 10, 12, 12, 14], "width": 243, "height": 288}}, "toufa2": {"toufa2": {"type": "mesh", "uvs": [0.93897, 0, 1, 0.02676, 1, 0.12228, 0.95604, 0.26435, 0.90894, 0.33131, 0.78268, 0.51082, 0.69715, 0.63241, 0.58118, 0.68774, 0.39544, 0.71837, 0.30257, 0.73369, 0.2097, 0.749, 0.09592, 0.84874, 0.09074, 1, 0.08536, 1, 0, 0.86029, 0, 0.79567, 0.21413, 0.63835, 0.32674, 0.5976, 0.43934, 0.55685, 0.51918, 0.51077, 0.61225, 0.38106, 0.74897, 0.19053, 0.81733, 0.09527, 0.88569, 0], "triangles": [12, 13, 11, 13, 14, 11, 14, 15, 11, 11, 15, 10, 10, 15, 16, 10, 16, 9, 9, 17, 8, 9, 16, 17, 8, 18, 7, 8, 17, 18, 18, 19, 7, 7, 19, 6, 6, 19, 5, 19, 20, 5, 5, 20, 4, 20, 21, 4, 4, 21, 3, 3, 21, 22, 2, 3, 23, 23, 3, 22, 2, 23, 0, 0, 1, 2], "vertices": [1, 95, -17.19, -14.55, 1, 1, 95, -18.32, 11.62, 1, 1, 95, 17.41, 30.72, 1, 2, 95, 78.55, 44.11, 0.89249, 96, -25.97, 41.88, 0.10751, 2, 95, 112.18, 41.42, 0.32835, 96, 7.76, 42.32, 0.67165, 2, 96, 98.2, 43.48, 0.5526, 97, -9.06, 42.54, 0.4474, 3, 96, 159.46, 44.27, 0.00091, 97, 50.33, 57.6, 0.99907, 98, -67.72, 29.01, 2e-05, 2, 97, 99.14, 44.09, 0.73473, 98, -18.33, 40.21, 0.26527, 1, 98, 54.48, 34.37, 1, 2, 98, 90.89, 31.45, 0.83289, 99, -1.72, 35.54, 0.16711, 2, 98, 127.29, 28.53, 0.01821, 99, 30.01, 17.44, 0.98179, 2, 99, 90.92, 21.6, 0.09366, 100, 19.45, 10.74, 0.90634, 1, 100, 83.61, 11.53, 1, 1, 100, 83.7, 9.45, 1, 1, 100, 25.95, -26.13, 1, 2, 99, 104.94, -19.48, 0.14056, 100, -1.42, -27.32, 0.85944, 2, 98, 113.63, -16.39, 0.39324, 99, -1.42, -17.43, 0.60676, 1, 98, 67.08, -21.94, 1, 2, 97, 101.33, -33.94, 0.18047, 98, 20.53, -27.49, 0.81953, 2, 97, 65.42, -27.11, 0.94434, 98, -14.33, -38.47, 0.05566, 2, 96, 88.64, -41.87, 0.63157, 97, 1.55, -42.69, 0.36843, 2, 95, 88.72, -41.32, 0.67325, 96, -7.93, -42.24, 0.32675, 1, 95, 40.63, -37.03, 1, 1, 95, -7.47, -32.74, 1], "hull": 24, "edges": [0, 46, 0, 2, 2, 4, 4, 6, 12, 14, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 36, 38, 14, 16, 32, 34, 34, 36, 16, 18, 18, 20, 10, 12, 38, 40, 40, 42, 6, 8, 8, 10, 42, 44, 44, 46], "width": 171, "height": 188}}, "toufa3": {"toufa3": {"type": "mesh", "uvs": [0.9526, 0, 1, 0.02845, 1, 0.09962, 0.99313, 0.15277, 0.90832, 0.20947, 0.8235, 0.26617, 0.65386, 0.37957, 0.50064, 0.48199, 0.34182, 0.58816, 0.19878, 0.68378, 0.12636, 0.73219, 0.10225, 0.79327, 0.12717, 0.94052, 0.12792, 1, 0.11652, 1, 0, 0.83865, 0, 0.76803, 0.04535, 0.67207, 0.12667, 0.60233, 0.23707, 0.50766, 0.34747, 0.41299, 0.3818, 0.37503, 0.46359, 0.27984, 0.54537, 0.18465, 0.66166, 0.10219, 0.77794, 0.01974, 0.88297, 0], "triangles": [14, 12, 13, 14, 15, 12, 15, 11, 12, 15, 16, 11, 11, 16, 10, 16, 17, 10, 10, 17, 9, 17, 18, 9, 9, 18, 8, 18, 19, 8, 8, 19, 20, 8, 20, 7, 20, 21, 7, 6, 7, 22, 7, 21, 22, 22, 23, 6, 6, 23, 5, 23, 24, 5, 5, 24, 4, 24, 25, 4, 25, 26, 4, 3, 4, 2, 2, 4, 26, 0, 2, 26, 0, 1, 2], "vertices": [1, 87, -25.66, -32.53, 1, 1, 87, -34.19, -2.7, 1, 1, 87, -6.06, 35.36, 1, 1, 87, 17.81, 61.67, 1, 2, 87, 75.62, 65.82, 0.93932, 88, -45.16, 73.33, 0.06068, 2, 87, 133.44, 69.98, 0.39974, 88, 12.63, 68.87, 0.60026, 2, 88, 128.21, 59.94, 0.36405, 89, 12.82, 59.28, 0.63595, 2, 89, 116.56, 45.1, 0.43756, 90, 1.01, 45.5, 0.56244, 2, 90, 109.51, 42.96, 0.28605, 91, 9.4, 42.85, 0.71395, 2, 91, 107.08, 39.3, 0.42195, 92, 9.64, 38.11, 0.57805, 2, 92, 57.51, 25.53, 0.93467, 93, -10.32, 45.6, 0.06533, 2, 92, 98.02, 38.36, 0.1408, 93, 27.69, 26.59, 0.8592, 1, 94, 55.64, 12.25, 1, 1, 94, 92.68, -1.65, 1, 1, 94, 90.55, -7.16, 1, 1, 93, 48.73, -30.72, 1, 2, 92, 114.43, -14.83, 0.08591, 93, 2.41, -23, 0.91409, 1, 92, 48.57, -31.8, 1, 2, 91, 97.9, -25.9, 0.80092, 92, -13.58, -23.49, 0.19908, 2, 90, 114.2, -33.19, 0.23235, 91, 13.1, -33.36, 0.76765, 2, 89, 135.07, -44.8, 0.10762, 90, 29.5, -41.75, 0.89238, 2, 89, 104.37, -48.25, 0.58456, 90, -0.62, -48.63, 0.41544, 2, 88, 151.15, -56.79, 0.15842, 89, 28.86, -58.58, 0.84158, 3, 87, 217.29, -59.41, 0.00124, 88, 76.37, -71.53, 0.93021, 89, -46.66, -68.91, 0.06855, 2, 87, 136.17, -67.64, 0.50607, 88, -5.08, -67.63, 0.49393, 2, 87, 55.04, -75.86, 0.99881, 88, -86.53, -63.73, 0.00119, 1, 87, 3.41, -54.02, 1], "hull": 27, "edges": [0, 52, 0, 2, 2, 4, 4, 6, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 40, 42, 50, 52, 18, 20, 34, 36, 16, 18, 36, 38, 38, 40, 14, 16, 42, 44, 44, 46, 12, 14, 10, 12, 46, 48, 48, 50, 6, 8, 8, 10], "width": 229, "height": 293}}, "toufa4": {"toufa4": {"type": "mesh", "uvs": [0.67031, 0, 0.92168, 0.02182, 0.80882, 0.14783, 0.69596, 0.27384, 0.57378, 0.41025, 0.54556, 0.4914, 0.51733, 0.57255, 0.56643, 0.68816, 0.61553, 0.80378, 0.70372, 0.88767, 1, 0.98219, 1, 0.99799, 0.97968, 1, 0.8205, 1, 0.3515, 0.92507, 0.08835, 0.72149, 0, 0.65315, 0, 0.49264, 0.02068, 0.38166, 0.04136, 0.27069, 0.16542, 0.19593, 0.32801, 0.09797, 0.49061, 0], "triangles": [14, 8, 9, 13, 9, 10, 10, 12, 13, 14, 9, 13, 11, 12, 10, 7, 15, 6, 15, 7, 8, 14, 15, 8, 6, 17, 5, 16, 17, 6, 15, 16, 6, 3, 19, 20, 4, 19, 3, 18, 19, 4, 5, 18, 4, 17, 18, 5, 2, 0, 1, 0, 21, 22, 0, 2, 21, 3, 21, 2, 3, 20, 21], "vertices": [1, 145, -13.02, -4.39, 1, 1, 145, -22.34, 40.46, 1, 2, 145, 37.62, 45.3, 0.99808, 146, -63.45, 59.92, 0.00192, 2, 145, 97.58, 50.14, 0.63545, 146, -3.8, 52.09, 0.36455, 3, 145, 162.5, 55.38, 0.01, 146, 60.77, 43.61, 0.90558, 147, -20.5, 51.74, 0.08442, 2, 146, 97.53, 46.31, 0.36189, 147, 15.48, 43.7, 0.63811, 3, 146, 134.29, 49.01, 0.01559, 147, 51.45, 35.66, 0.97212, 148, -26.34, 47.17, 0.01229, 2, 147, 104.02, 40.05, 0.13352, 148, 24.68, 33.75, 0.86648, 2, 148, 75.7, 20.33, 0.75563, 149, -0.49, 22.57, 0.24437, 1, 149, 36.18, 4.5, 1, 1, 149, 103.88, 7.22, 1, 1, 149, 108.56, 1.87, 1, 1, 149, 106.43, -1.2, 1, 1, 149, 85.11, -19.86, 1, 2, 148, 106.13, -44.95, 0.3555, 149, 0.08, -49.45, 0.6445, 2, 147, 111.9, -46, 0.37403, 148, 3.36, -49.99, 0.62597, 2, 147, 79.95, -59.11, 0.84895, 148, -31.14, -51.68, 0.15105, 2, 146, 118.31, -48.55, 0.24851, 147, 7.97, -53.12, 0.75149, 2, 146, 68.7, -55.36, 0.91875, 147, -41.49, -45.3, 0.08125, 2, 145, 143.91, -56.79, 0.10944, 146, 19.09, -62.17, 0.89056, 2, 145, 104.18, -50.38, 0.61414, 146, -18.41, -47.58, 0.38586, 2, 145, 52.12, -41.98, 0.99956, 146, -67.56, -28.46, 0.00044, 1, 145, 0.05, -33.58, 1], "hull": 23, "edges": [0, 44, 0, 2, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 32, 34, 28, 30, 30, 32, 12, 14, 14, 16, 8, 10, 10, 12, 34, 36, 36, 38, 6, 8, 38, 40, 40, 42, 42, 44, 2, 4, 4, 6], "width": 81, "height": 199}}, "toufa5": {"toufa5": {"type": "mesh", "uvs": [0.92348, 0, 0.95661, 0.00529, 0.87909, 0.15059, 0.80157, 0.29589, 0.74686, 0.39843, 0.59559, 0.48052, 0.44432, 0.56262, 0.40201, 0.71852, 0.56132, 0.83052, 0.76866, 0.87487, 1, 0.87325, 1, 0.88254, 0.90521, 0.96838, 0.74206, 1, 0.47477, 1, 0.2896, 0.9666, 0.10444, 0.93319, 0, 0.79048, 0, 0.65929, 0.00015, 0.62791, 0.11962, 0.53061, 0.24973, 0.42465, 0.36683, 0.32928, 0.46541, 0.249, 0.6456, 0.10224, 0.87463, 0], "triangles": [3, 23, 2, 23, 24, 2, 24, 25, 2, 1, 25, 0, 1, 2, 25, 21, 22, 5, 5, 22, 4, 4, 22, 3, 3, 22, 23, 7, 19, 6, 6, 19, 20, 20, 21, 6, 6, 21, 5, 8, 17, 7, 8, 16, 17, 17, 18, 7, 18, 19, 7, 14, 15, 8, 15, 16, 8, 12, 13, 9, 13, 14, 9, 14, 8, 9, 12, 9, 11, 9, 10, 11], "vertices": [1, 109, -52.33, -1.25, 1, 1, 109, -52.39, 6.54, 1, 1, 109, 28.33, 21.61, 1, 2, 110, -15.2, 36.61, 0.23118, 109, 109.04, 36.69, 0.76882, 2, 110, 41.7, 47.54, 0.99148, 109, 166, 47.33, 0.00852, 2, 111, -10.45, 33.7, 0.27675, 110, 96.32, 34.69, 0.72325, 2, 112, -32.44, 61.07, 0.00671, 111, 45.3, 27.3, 0.99329, 3, 113, -17.5, 58.46, 0.22703, 112, 51.14, 38, 0.72591, 111, 124.91, 61.65, 0.04706, 2, 114, 7.13, 36.99, 0.2305, 113, 48.31, 31.79, 0.7695, 2, 114, 50.17, 8.85, 0.99879, 113, 98.93, 40.88, 0.00121, 1, 114, 100.51, 5.63, 1, 1, 114, 100.09, 0.51, 1, 1, 114, 75.62, -45.12, 1, 2, 114, 38.74, -59.64, 0.96279, 113, 137.62, -16.78, 0.03721, 3, 114, -19.33, -54.88, 0.40865, 113, 92.13, -53.18, 0.53394, 112, 207.31, 28.44, 0.05741, 3, 114, -58.05, -33.17, 0.03009, 113, 49.07, -63.97, 0.64336, 112, 182.54, -8.4, 0.32655, 2, 113, 6.01, -74.76, 0.26091, 112, 157.78, -45.24, 0.73909, 1, 112, 76.21, -54.93, 1, 2, 112, 4.62, -43.17, 0.78558, 111, 139.47, -30.77, 0.21442, 2, 112, -12.5, -40.33, 0.52894, 111, 124.34, -39.27, 0.47106, 2, 111, 64.68, -43.04, 0.99979, 110, 161.99, -50.29, 0.00021, 2, 111, -0.29, -47.15, 0.45914, 110, 96.98, -46.79, 0.54086, 3, 111, -58.76, -50.84, 0.00143, 110, 38.48, -43.65, 0.9798, 109, 162.31, -43.84, 0.01877, 2, 110, -10.78, -41, 0.28999, 109, 113.07, -40.94, 0.71001, 1, 109, 23.06, -35.64, 1, 1, 109, -48.26, -11.1, 1], "hull": 26, "edges": [0, 50, 0, 2, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 32, 34, 34, 36, 36, 38, 48, 50, 38, 40, 40, 42, 8, 10, 10, 12, 42, 44, 44, 46, 46, 48, 6, 8, 2, 4, 4, 6, 28, 30, 30, 32], "width": 98, "height": 244}}, "toufa6": {"toufa6": {"type": "mesh", "uvs": [1, 0, 1, 0.02963, 0.89548, 0.14925, 0.79096, 0.26888, 0.61388, 0.36713, 0.43838, 0.46451, 0.26287, 0.56189, 0.13602, 0.7107, 0.20498, 0.85535, 0.27394, 1, 0.21815, 1, 0.05186, 0.94266, 0, 0.86208, 0, 0.72166, 0.08192, 0.58906, 0.32277, 0.42862, 0.56362, 0.26819, 0.72647, 0.1597, 0.96622, 0], "triangles": [3, 17, 2, 2, 17, 18, 2, 18, 1, 18, 0, 1, 11, 8, 10, 9, 10, 8, 11, 12, 8, 8, 13, 7, 8, 12, 13, 13, 14, 7, 7, 14, 6, 6, 14, 15, 6, 15, 5, 5, 15, 4, 15, 16, 4, 4, 16, 3, 16, 17, 3], "vertices": [2, 82, -19.97, 4.67, 0.22286, 62, 23.14, -109.58, 0.77714, 2, 82, -10.2, 9.97, 0.22286, 62, 32.06, -102.95, 0.77714, 2, 82, 37.7, 15.74, 0.22286, 62, 78.66, -90.43, 0.77714, 3, 82, 85.6, 21.51, 0.2203, 83, -19.95, 18.79, 0.00255, 62, 125.26, -77.91, 0.77714, 1, 83, 27.58, 16.82, 1, 2, 83, 74.7, 14.86, 0.89757, 84, -7.42, 15.93, 0.10243, 1, 84, 39.07, 8.04, 1, 2, 84, 98.23, 16.88, 0.03437, 85, 16.15, 9.86, 0.96563, 2, 85, 70.78, 19.61, 0.79728, 86, -1.79, 23.65, 0.20272, 1, 86, 50.42, 4.84, 1, 1, 86, 45.4, -3.2, 1, 1, 86, 12.18, -15.78, 1, 2, 85, 72.04, -15.31, 0.99518, 86, -18.11, -7.24, 0.00482, 1, 85, 19.42, -13.39, 1, 1, 84, 63.13, -13.67, 1, 2, 83, 75.72, -8.94, 0.98698, 84, -9.41, -7.81, 0.01302, 2, 82, 103.8, -12.59, 0.34358, 83, 3.03, -12.28, 0.65642, 1, 82, 54.84, -7.65, 1, 2, 82, -17.23, -0.37, 0.22286, 62, 26.57, -114.19, 0.77714], "hull": 19, "edges": [0, 36, 0, 2, 12, 14, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 34, 36, 2, 4, 4, 6, 32, 34, 6, 8, 28, 30, 30, 32, 8, 10, 10, 12, 14, 16, 16, 18], "width": 77, "height": 166}}, "toufa7": {"toufa7": {"type": "mesh", "uvs": [0.80944, 0, 0.94766, 0, 1, 0.02484, 1, 0.03424, 0.89123, 0.13307, 0.78246, 0.23191, 0.69426, 0.31205, 0.66002, 0.33883, 0.56896, 0.46075, 0.54512, 0.50753, 0.48659, 0.62666, 0.42807, 0.74578, 0.37449, 0.85485, 0.33749, 0.93017, 0.22104, 0.98184, 0.18058, 0.9998, 0.14823, 1, 0.09544, 1, 0.02025, 0.92944, 0, 0.8911, 0, 0.88034, 0.08845, 0.8617, 0.12851, 0.95084, 0.17888, 0.92417, 0.22926, 0.8975, 0.2548, 0.84269, 0.31398, 0.71571, 0.34477, 0.64963, 0.35863, 0.60908, 0.37822, 0.48435, 0.38538, 0.43882, 0.44436, 0.29732, 0.50335, 0.15581, 0.57973, 0.08622, 0.65611, 0.01662, 0.79842, 9e-05], "triangles": [18, 21, 22, 18, 19, 21, 19, 20, 21, 15, 16, 22, 16, 17, 22, 17, 18, 22, 14, 15, 23, 15, 22, 23, 14, 24, 13, 14, 23, 24, 24, 25, 13, 13, 25, 12, 25, 26, 12, 12, 26, 11, 26, 27, 11, 11, 27, 10, 10, 27, 28, 9, 10, 29, 10, 28, 29, 9, 29, 30, 8, 9, 30, 30, 31, 8, 8, 31, 7, 6, 7, 32, 7, 31, 32, 32, 33, 6, 6, 33, 5, 5, 35, 4, 35, 0, 4, 33, 34, 5, 5, 34, 35, 4, 1, 3, 4, 0, 1, 1, 2, 3], "vertices": [1, 61, -20.25, -86.88, 1, 2, 61, -116.75, -27.42, 1, 70, 810.42, -831.66, 0, 2, 61, -140.55, 15.77, 1, 70, 781.84, -871.84, 0, 2, 61, -135.72, 23.6, 1, 70, 772.69, -870.9, 0, 1, 61, -9.08, 59.11, 1, 3, 61, 117.56, 94.61, 0.76867, 62, -20.92, 110.17, 0.23133, 70, 598.76, -673.57, 0, 4, 61, 220.25, 123.4, 0.09571, 62, 85.13, 98.92, 0.82469, 63, -39.66, 115.18, 0.0796, 70, 528.24, -593.56, 0, 4, 61, 257.89, 130.97, 0.01755, 62, 122.9, 92.03, 0.69316, 63, -5.47, 97.72, 0.28928, 70, 505.08, -562.94, 0, 2, 62, 263.14, 103.28, 0.00066, 63, 132.09, 68.2, 0.99934, 2, 63, 181.77, 65.43, 0.98164, 64, 4.43, 68.22, 0.01836, 3, 63, 307.66, 60.05, 0.01222, 64, 128.99, 49.17, 0.46759, 65, -1.49, 49.54, 0.52019, 2, 65, 124.51, 50.17, 0.61305, 66, -8.66, 49.82, 0.38695, 2, 66, 106.55, 55.71, 0.80461, 67, -30.25, 50.34, 0.19539, 2, 66, 186.11, 59.78, 0.02984, 67, 46.52, 71.61, 0.97016, 2, 67, 145.03, 27.27, 0.32758, 68, -3.6, 35.51, 0.67242, 1, 68, 31.27, 49.42, 1, 1, 68, 57.67, 46.77, 1, 2, 68, 100.7, 42.14, 0.9032, 69, -33.27, 25.86, 0.0968, 3, 64, 507.2, -252.29, 0, 69, 59.24, 23.96, 0.66495, 70, -15.49, 18.32, 0.33505, 5, 64, 474.7, -277.29, 0, 67, 186.26, -170.29, 0, 68, 167.11, -72.13, 2e-05, 69, 98, 10.56, 0.00137, 70, 23.51, 30.98, 0.99862, 3, 64, 464.47, -279.78, 0, 68, 165.98, -82.6, 2e-05, 70, 33.98, 29.9, 0.99998, 4, 67, 119.03, -130.69, 3e-05, 68, 91.91, -92.96, 0.00011, 69, 69.62, -62.12, 0.12868, 70, 44.65, -44.12, 0.87118, 3, 68, 68.59, -2.76, 0.99068, 69, -16.57, -26.75, 0.00794, 70, -45.45, -67.82, 0.00137, 5, 64, 471.4, -127.12, 0, 67, 121.73, -34.65, 0.191, 68, 24.73, -24.27, 0.80879, 69, -25.62, -74.76, 0.00018, 70, -23.75, -111.59, 3e-05, 4, 67, 75.78, -18.06, 0.98787, 68, -19.13, -45.79, 0.01212, 69, -34.68, -122.76, 1e-05, 70, -2.05, -155.37, 0, 2, 66, 137.77, -38.1, 0.22514, 67, 20.62, -34.44, 0.77486, 3, 64, 247.1, -67.74, 1e-05, 65, 133.43, -47.5, 0.41931, 66, 4.75, -47.33, 0.58068, 3, 64, 178.34, -58.5, 0.00837, 65, 64.07, -49.1, 0.99153, 66, -64.47, -52.13, 0.0001, 3, 64, 137.12, -56.85, 0.27984, 65, 23.1, -53.91, 0.72016, 70, 267.6, -289.91, 0, 2, 64, 14.79, -70.11, 1, 70, 387.29, -318.45, 0, 3, 63, 163.28, -80.63, 0.122, 64, -29.86, -74.95, 0.878, 70, 430.98, -328.87, 0, 3, 62, 195.88, -74.09, 0.26217, 63, 16.69, -82.36, 0.72216, 64, -175.76, -60.71, 0.01567, 3, 61, 273.37, -88.81, 0.07391, 62, 55.97, -117.88, 0.926, 63, -129.9, -84.09, 9e-05, 2, 61, 184.34, -113.9, 0.63735, 62, -36.02, -108.26, 0.36265, 2, 61, 95.32, -138.99, 0.98497, 62, -128.01, -98.63, 0.01503, 1, 61, -12.52, -91.54, 1], "hull": 36, "edges": [0, 70, 0, 2, 2, 4, 4, 6, 12, 14, 14, 16, 16, 18, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 54, 56, 68, 70, 24, 26, 48, 50, 50, 52, 52, 54, 22, 24, 18, 20, 20, 22, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 10, 12, 6, 8, 8, 10, 26, 28, 28, 30, 44, 46, 46, 48], "width": 360, "height": 429}}, "toufa8": {"toufa8": {"x": -16.8, "y": 39.05, "scaleX": 2.2, "scaleY": 2.2, "rotation": -66.48, "width": 139, "height": 50}}, "toufa9": {"toufa9": {"type": "mesh", "uvs": [0.35958, 0, 0.56907, 0.06231, 0.68827, 0.17854, 0.80747, 0.29477, 0.94143, 0.42539, 0.97071, 0.56978, 1, 0.71417, 1, 0.82146, 0.9282, 0.96796, 0.86584, 1, 0.84978, 1, 0.94597, 0.87786, 0.9704, 0.7465, 0.9082, 0.58678, 0.846, 0.42705, 0.723, 0.32592, 0.6319, 0.24777, 0.38793, 0.19133, 0, 0.22428, 0, 0.11335, 0.2287, 0], "triangles": [17, 20, 0, 20, 17, 19, 17, 18, 19, 16, 17, 1, 17, 0, 1, 15, 3, 14, 15, 16, 3, 16, 2, 3, 2, 16, 1, 13, 5, 6, 13, 4, 5, 13, 14, 4, 14, 3, 4, 8, 11, 7, 11, 12, 7, 12, 6, 7, 12, 13, 6, 9, 10, 8, 8, 10, 11], "vertices": [2, 40, -5.61, 27.6, 0.59794, 39, 89.02, 24.33, 0.40206, 1, 40, 48.56, 27.97, 1, 2, 41, 3.76, 6.24, 0.927, 40, 89.79, 3.46, 0.073, 1, 41, 51.73, 6.09, 1, 1, 42, 13.73, 12.31, 1, 1, 42, 61.98, 7.93, 1, 1, 43, 5.24, 4.9, 1, 1, 43, 40.86, 4.9, 1, 3, 44, 32.22, 7.96, 0.99999, 43, 89.49, -12.26, 1e-05, 42, 188.15, -32.8, 0, 2, 44, 49.54, 2.01, 1, 42, 195.01, -49.78, 0, 2, 44, 51.77, -1.11, 1, 42, 194.12, -53.51, 0, 2, 44, 5.42, -5.97, 0.41942, 43, 59.58, -8.01, 0.58058, 2, 43, 15.97, -2.17, 1, 42, 119.01, -5.84, 0, 1, 42, 63.98, -7.92, 1, 2, 42, 8.94, -10, 0.83075, 41, 92.47, -12.7, 0.16925, 1, 41, 47.99, -16.28, 1, 2, 41, 14.18, -18.29, 0.72979, 40, 86.29, -22.95, 0.27021, 2, 40, 25.28, -28.31, 0.79489, 39, 74.83, -37.96, 0.20511, 1, 39, -16.42, -18.24, 1, 1, 39, -4.48, 16.6, 1, 2, 40, -34.44, 15.46, 0.02618, 39, 59.43, 34.47, 0.97382], "hull": 21, "edges": [0, 40, 0, 2, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 6, 8, 2, 4, 4, 6, 8, 10, 10, 12, 24, 26, 26, 28], "width": 107, "height": 148}}, "toufa10": {"toufa10": {"type": "mesh", "uvs": [0.08467, 0, 0.39764, 0.05351, 0.62848, 0.22792, 0.81424, 0.41178, 1, 0.59564, 1, 0.72747, 0.85765, 0.89388, 0.61179, 0.98361, 0.43437, 1, 0.35233, 1, 0.24699, 0.99148, 0.05455, 0.95627, 0.28355, 0.95914, 0.51255, 0.96201, 0.79164, 0.8728, 0.84545, 0.70946, 0.79723, 0.66043, 0.54695, 0.45758, 0.29666, 0.25474, 0, 0.01431, 0, 0], "triangles": [18, 19, 1, 18, 1, 2, 1, 19, 0, 19, 20, 0, 17, 2, 3, 17, 18, 2, 5, 16, 4, 16, 17, 4, 17, 3, 4, 6, 14, 5, 14, 15, 5, 5, 15, 16, 8, 13, 7, 7, 14, 6, 7, 13, 14, 13, 8, 12, 10, 12, 9, 8, 9, 12, 10, 11, 12], "vertices": [1, 45, -17.79, 10.53, 1, 2, 50, 37.2, -471.39, 0, 45, 39.77, 39.91, 1, 2, 46, 14.43, 24.89, 0.93651, 45, 136.75, 19.16, 0.06349, 3, 50, -46.98, -290.75, 0, 47, 1.92, 25.77, 0.54198, 46, 114.77, 25.82, 0.45802, 1, 47, 102.22, 22.76, 1, 2, 50, -87.27, -130.41, 0, 48, 36.75, 17.21, 1, 3, 50, -63.6, -44.35, 0, 49, 1.03, 2.16, 0.66062, 48, 124.46, 0.65, 0.33938, 1, 49, 64.94, 13.97, 1, 1, 50, 13.64, 12.69, 1, 1, 50, 28.97, 13.21, 1, 1, 50, 48.8, 9.54, 1, 1, 50, 85.39, -7.24, 1, 1, 50, 42.54, -7.25, 1, 2, 50, -0.31, -7.26, 0.19734, 49, 73.97, -5.65, 0.80266, 2, 49, 5.09, -13.72, 0.44507, 48, 115.18, -12.86, 0.55493, 2, 48, 30.94, -12.56, 0.99135, 47, 145.44, -25.85, 0.00865, 2, 48, 7.05, -24.42, 0.44756, 47, 118.79, -24.82, 0.55244, 2, 47, 4.96, -29.36, 0.67715, 46, 119.97, -29.14, 0.32285, 2, 46, 6.42, -38.15, 0.5254, 45, 109.5, -38.25, 0.4746, 1, 45, -21.73, -6.47, 1, 1, 45, -27.51, -1.97, 1], "hull": 21, "edges": [0, 40, 0, 2, 2, 4, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 26, 28, 28, 30, 30, 32, 38, 40, 36, 38, 32, 34, 34, 36, 4, 6, 6, 8, 22, 24, 24, 26], "width": 84, "height": 226}}, "toufa11": {"toufa11": {"type": "mesh", "uvs": [0.82194, 0, 0.9307, 0.11602, 0.96535, 0.23243, 1, 0.34884, 1, 0.47999, 0.89842, 0.56197, 0.79685, 0.64395, 0.48663, 0.712, 0.17641, 0.78004, 0.13844, 0.88484, 0.60785, 1, 0.52247, 1, 0.112, 0.9312, 0, 0.8556, 0, 0.8149, 0.05549, 0.73596, 0.26022, 0.66812, 0.44167, 0.58546, 0.54293, 0.47333, 0.64419, 0.3612, 0.68976, 0.29044, 0.70138, 0.18902, 0.71299, 0.08759, 0.80408, 0], "triangles": [10, 11, 9, 12, 9, 11, 12, 13, 9, 8, 9, 14, 9, 13, 14, 14, 15, 8, 8, 15, 16, 8, 16, 7, 16, 17, 7, 7, 17, 6, 6, 17, 5, 17, 18, 5, 5, 18, 4, 18, 19, 4, 19, 3, 4, 19, 20, 3, 20, 2, 3, 20, 21, 2, 21, 1, 2, 21, 22, 1, 1, 22, 0, 22, 23, 0], "vertices": [1, 117, -59.75, -9.52, 1, 1, 117, -0.99, 16.61, 1, 1, 117, 58.69, 26.75, 1, 2, 117, 118.37, 36.9, 0.93248, 118, -32.53, 26.03, 0.06752, 2, 117, 185.97, 39.9, 0.01748, 118, 28.19, 55.92, 0.98252, 2, 118, 75.83, 54.92, 0.97462, 119, -52.9, 37.9, 0.02538, 2, 118, 123.47, 53.92, 0.53487, 119, -8.34, 54.77, 0.46513, 1, 119, 65, 36.21, 1, 3, 119, 138.34, 17.65, 0.01588, 120, 37.56, 13.66, 0.95227, 121, 2.3, 35.98, 0.03185, 1, 121, 42.37, -1.26, 1, 2, 120, 71.83, 156.46, 0, 122, 83.17, -0.12, 1, 1, 122, 65.79, -6.27, 1, 2, 120, 108.15, 49.63, 0.00061, 121, 58.91, -19.45, 0.99939, 1, 121, 13.05, -17.46, 1, 2, 120, 74.9, -5.87, 0.49693, 121, -4.3, -5.63, 0.50307, 1, 120, 35.18, -20.89, 1, 2, 119, 86.32, -13.29, 0.82525, 120, -19.41, -6.76, 0.17475, 2, 118, 130.28, -28.24, 0.04605, 119, 28.67, -18.9, 0.95395, 2, 118, 68.71, -34.17, 0.99535, 119, -26.23, -47.4, 0.00465, 2, 117, 128.15, -39.6, 0.5082, 118, 7.14, -40.11, 0.4918, 2, 117, 91.23, -31.39, 0.98336, 118, -29.97, -47.4, 0.01664, 1, 117, 38.84, -31.21, 1, 1, 117, -13.55, -31.02, 1, 1, 117, -59.58, -13.37, 1], "hull": 24, "edges": [0, 46, 0, 2, 6, 8, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 38, 40, 44, 46, 34, 36, 36, 38, 8, 10, 10, 12, 2, 4, 4, 6, 40, 42, 42, 44, 12, 14, 14, 16], "width": 97, "height": 228}}, "toufa12": {"toufa12": {"type": "mesh", "uvs": [0.55566, 0, 0.77101, 0.05213, 0.82181, 0.09207, 0.95202, 0.19444, 1, 0.2984, 1, 0.38613, 0.93672, 0.46902, 0.73598, 0.51712, 0.53524, 0.56521, 0.4063, 0.63596, 0.40786, 0.69507, 0.4793, 0.76228, 0.79866, 0.87897, 0.83118, 0.96689, 0.70782, 1, 0.69042, 1, 0.72177, 0.9073, 0.42595, 0.83505, 0, 0.69453, 0, 0.60846, 0.31881, 0.45779, 0.46029, 0.39091, 0.46367, 0.35552, 0.37749, 0.23655, 0.31529, 0.15068, 0.33226, 0.1034, 0.36935, 1e-05, 0.37663, 0], "triangles": [14, 15, 13, 15, 16, 13, 16, 12, 13, 16, 17, 12, 17, 11, 12, 17, 18, 11, 11, 18, 10, 18, 9, 10, 18, 19, 9, 9, 19, 8, 19, 20, 8, 8, 20, 7, 7, 20, 21, 6, 7, 21, 6, 21, 5, 21, 22, 5, 22, 4, 5, 22, 23, 4, 23, 3, 4, 23, 24, 3, 24, 2, 3, 25, 1, 2, 2, 24, 25, 0, 1, 25, 0, 25, 27, 25, 26, 27], "vertices": [1, 123, -70.96, 37.63, 1, 1, 123, -15.79, 65.3, 1, 1, 123, 19.29, 64.58, 1, 2, 123, 109.2, 62.74, 0.59982, 124, -11.86, 61.63, 0.40018, 2, 124, 76, 62.13, 0.98301, 125, -61.88, 41.19, 0.01699, 2, 124, 149.29, 54.41, 0.22032, 125, 8.58, 62.78, 0.77968, 2, 125, 78.9, 70.96, 0.8931, 126, -37.87, 63.33, 0.1069, 2, 125, 129.4, 44.03, 0.23128, 126, 17.88, 50.36, 0.76872, 1, 126, 73.63, 37.38, 1, 3, 126, 137.83, 46.79, 0.21099, 127, 46.48, 32.31, 0.77554, 128, -23.79, 54.59, 0.01347, 2, 127, 96.01, 35.77, 0.25382, 128, 19.39, 30.07, 0.74618, 1, 128, 75.51, 14.38, 1, 2, 129, 90.7, 8.4, 0.69928, 130, -5.55, 6.34, 0.30072, 1, 130, 68.49, 10.27, 1, 1, 130, 95.4, -15.62, 1, 1, 130, 95.27, -19.14, 1, 2, 129, 99.79, -18.53, 0.10676, 130, 17.68, -10.03, 0.89324, 2, 128, 123.09, -25.48, 0.09638, 129, 15.14, -27.94, 0.90362, 2, 127, 100.78, -46.48, 0.69216, 128, -22.14, -41.09, 0.30784, 1, 127, 28.63, -51.06, 1, 3, 125, 106.45, -51.14, 0.25572, 126, 20.28, -47.52, 0.73228, 127, -101.76, 5.19, 0.01201, 3, 124, 141.87, -54.43, 0.05131, 125, 44.36, -40.28, 0.93802, 126, -42.5, -53.05, 0.01067, 2, 124, 112.38, -50.64, 0.42327, 125, 15.74, -48.34, 0.57673, 2, 123, 106.59, -58.55, 0.45125, 124, 11.17, -57.49, 0.54875, 2, 123, 34.15, -47.99, 0.99906, 124, -61.88, -62.43, 0.00094, 1, 123, -2.52, -32.34, 1, 1, 123, -82.7, 1.87, 1, 1, 123, -82.24, 3.27, 1], "hull": 28, "edges": [0, 54, 0, 2, 6, 8, 8, 10, 10, 12, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 42, 44, 52, 54, 38, 40, 40, 42, 12, 14, 14, 16, 44, 46, 46, 48, 48, 50, 50, 52, 2, 4, 4, 6], "width": 91, "height": 368}}, "toufa13": {"toufa13": {"type": "mesh", "uvs": [0.67198, 0, 0.83474, 0.08064, 1, 0.23738, 1, 0.37896, 1, 0.47233, 1, 0.59626, 0.81275, 0.69618, 0.58145, 0.75492, 0.35016, 0.81367, 0.25615, 0.84707, 0.19241, 0.89995, 0.21227, 0.93979, 0.27456, 1, 0.25992, 1, 0.11151, 0.9537, 0.02198, 0.83941, 0.5077, 0.70894, 0.50856, 0.70884, 0.59453, 0.66154, 0.69563, 0.55346, 0.70582, 0.53262, 0.69196, 0.46276, 0.67809, 0.3929, 0.66479, 0.32594, 0.60028, 0.26934, 0.40302, 0.13509, 0.30119, 0.09483, 0, 0.04558, 0, 0.02045, 0.00017, 0.0192, 0.61307, 0], "triangles": [25, 1, 2, 1, 26, 30, 30, 0, 1, 1, 25, 26, 26, 28, 29, 26, 29, 30, 26, 27, 28, 22, 23, 3, 23, 2, 3, 23, 24, 2, 24, 25, 2, 19, 20, 5, 20, 4, 5, 20, 21, 4, 21, 3, 4, 21, 22, 3, 16, 17, 7, 7, 17, 6, 17, 18, 6, 6, 18, 5, 18, 19, 5, 9, 15, 8, 8, 15, 16, 8, 16, 7, 14, 11, 13, 12, 13, 11, 14, 10, 11, 14, 15, 10, 10, 15, 9], "vertices": [1, 51, 32.22, 65.22, 1, 1, 51, 94.79, 54.72, 1, 1, 52, 51.41, 41.14, 1, 2, 53, -8.97, 24.2, 0.29517, 52, 149.42, 25.46, 0.70483, 1, 53, 56.45, 26.38, 1, 2, 54, -0.01, 34.85, 0.74594, 53, 143.27, 29.27, 0.25406, 1, 54, 77.34, 39.5, 1, 2, 55, 12.03, 25.39, 0.79428, 54, 133.14, 23.44, 0.20572, 1, 55, 69.54, 17.34, 1, 2, 56, 9.13, 15.87, 0.61299, 55, 98.25, 18.08, 0.38701, 1, 56, 45.77, 3.27, 1, 2, 56, 73.81, 5.78, 1, 55, 154.91, 50.86, 0, 2, 56, 116.38, 15.3, 1, 55, 182.06, 85.01, 0, 2, 56, 116.29, 12.71, 1, 55, 183.61, 82.94, 0, 1, 56, 82.92, -12.39, 1, 2, 56, 2.28, -25.36, 0.848, 55, 118.82, -18.3, 0.152, 2, 55, -5.93, -4.4, 0.04767, 54, 111.17, -3.52, 0.95233, 2, 55, -6.08, -4.32, 0.03857, 54, 111.03, -3.42, 0.96143, 1, 54, 74.65, -6.04, 1, 2, 54, -0.38, -26.81, 0.39775, 53, 115.09, -25.57, 0.60225, 2, 54, -14.05, -32.26, 0.07887, 53, 100.43, -24.25, 0.92113, 1, 53, 51.56, -28.34, 1, 2, 53, 2.7, -32.42, 0.51786, 52, 150.06, -32.34, 0.48214, 1, 52, 103.34, -27.25, 1, 2, 52, 62.36, -32.26, 0.99936, 51, 176.38, -57.36, 0.00064, 2, 52, -36.08, -51.86, 0.01039, 51, 79.98, -29.4, 0.98961, 1, 51, 46.57, -27.09, 1, 1, 51, -12.91, -49.37, 1, 1, 51, -27.06, -38.89, 1, 1, 51, -27.75, -38.34, 1, 1, 51, 26.01, 56.84, 1], "hull": 31, "edges": [0, 60, 0, 2, 2, 4, 10, 12, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 34, 36, 36, 38, 38, 40, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 32, 34, 12, 14, 14, 16, 8, 10, 44, 46, 4, 6, 6, 8, 40, 42, 42, 44], "width": 80, "height": 308}}, "toufa14": {"toufa14": {"type": "mesh", "uvs": [0.75082, 0, 0.70107, 0.16597, 0.47466, 0.44951, 0.24825, 0.73305, 0.63872, 0.86007, 0.84461, 0.63458, 0.98468, 0.70863, 1, 0.78668, 1, 0.86368, 0.80136, 0.97201, 0.62504, 1, 0.39955, 1, 0.00961, 0.86385, 0.00467, 0.44105, 0.22536, 0.13641, 0.69062, 0.00455], "triangles": [3, 13, 2, 13, 14, 2, 2, 14, 1, 14, 15, 1, 1, 15, 0, 12, 13, 3, 12, 3, 11, 10, 11, 4, 11, 3, 4, 9, 10, 4, 9, 4, 8, 4, 7, 8, 4, 6, 7, 4, 5, 6], "vertices": [1, 57, -38.68, 19.27, 1, 1, 57, 1.02, 29.54, 1, 2, 58, -14.94, 36.59, 0.0835, 57, 76.11, 30.34, 0.9165, 2, 59, 7.05, 25.81, 0.21307, 58, 56.86, 14.59, 0.78693, 2, 60, 12.49, 13.38, 0.66325, 59, 65.62, 17.6, 0.33675, 1, 60, 65.97, 42.92, 1, 1, 60, 70.27, 17.74, 1, 1, 60, 60.87, 1.05, 1, 1, 60, 50.01, -14.28, 1, 2, 60, 13.82, -21.04, 0.99417, 59, 95.79, 0.99, 0.00583, 2, 60, -8.68, -13.46, 0.31801, 59, 77.69, -14.38, 0.68199, 1, 59, 51.04, -26.04, 1, 2, 59, -8.36, -15.77, 0.48881, 58, 91.75, -12.78, 0.51119, 2, 58, -10.81, -23.94, 0.2613, 57, 98.39, -26.09, 0.7387, 1, 57, 18.87, -29.62, 1, 1, 57, -34.57, 12.6, 1], "hull": 16, "edges": [0, 30, 0, 2, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 2, 4, 4, 6], "width": 60, "height": 110}}, "toufa15": {"toufa15": {"type": "mesh", "uvs": [0.10199, 0, 0.22184, 0.22159, 0.301, 0.31452, 0.49025, 0.40943, 0.63443, 0.4155, 0.7547, 0.36158, 0.80571, 0.28653, 0.65758, 0.08681, 0.82509, 0.15155, 0.99261, 0.21629, 1, 0.25118, 1, 0.34356, 0.9224, 0.46541, 0.84479, 0.58727, 0.77317, 0.69973, 0.8563, 0.75403, 0.90261, 0.90179, 0.87708, 1, 0.86852, 1, 0.87786, 0.9057, 0.81002, 0.76282, 0.70095, 0.7243, 0.4931, 0.64946, 0.28526, 0.57462, 0.05862, 0.34812, 0, 0.16274, 0, 0.05291, 0.04536, 0], "triangles": [0, 25, 26, 25, 0, 1, 27, 0, 26, 24, 25, 1, 2, 24, 1, 23, 2, 3, 23, 24, 2, 23, 3, 22, 22, 3, 4, 21, 22, 4, 6, 7, 8, 6, 8, 9, 6, 9, 10, 6, 10, 11, 12, 6, 11, 5, 6, 12, 13, 5, 12, 13, 4, 5, 14, 4, 13, 14, 21, 4, 20, 14, 15, 21, 14, 20, 20, 15, 19, 19, 15, 16, 17, 18, 19, 17, 19, 16], "vertices": [1, 131, -20.48, 15.24, 1, 1, 131, 54.56, 14.51, 1, 2, 132, 8.19, 21.11, 0.59671, 131, 90.66, 22.02, 0.40329, 2, 133, 0.24, 21.72, 0.36431, 132, 73.8, 20.59, 0.63569, 3, 137, -116.12, -76.25, 0, 134, 2.38, 30.72, 0.2231, 133, 45.47, 24.24, 0.7769, 4, 137, -131.92, -38.49, 0, 134, 42.03, 20.53, 0.94299, 135, -9.8, 34.06, 0.0472, 133, 81.57, 43.55, 0.00981, 2, 134, 68.07, 28.4, 0.23061, 135, 15.47, 23.99, 0.76939, 1, 135, 60.66, 83.53, 1, 1, 135, 55.29, 27.87, 1, 2, 137, -174.49, 36.22, 0, 135, 49.93, -27.79, 1, 2, 137, -164.27, 38.54, 0, 135, 40.59, -32.56, 1, 3, 137, -137.2, 38.54, 0, 134, 106.64, -21.74, 0.05529, 135, 14.37, -39.26, 0.94471, 3, 136, 14.34, 88.29, 0.02603, 134, 65.66, -35.51, 0.83824, 135, -26.25, -24.48, 0.13573, 2, 136, 17.46, 45.18, 0.32469, 134, 24.68, -49.27, 0.67531, 2, 136, 20.35, 5.39, 0.97774, 134, -13.13, -61.98, 0.02226, 2, 137, -16.93, -6.59, 0.0001, 136, 50.68, 9.17, 0.9999, 2, 137, 26.36, 7.96, 1, 136, 88.99, -15.7, 0, 1, 137, 55.14, -0.06, 1, 1, 137, 55.14, -2.75, 1, 1, 137, 27.51, 0.18, 1, 2, 137, -14.36, -21.12, 5e-05, 136, 40.9, -1.89, 0.99995, 3, 137, -25.64, -55.36, 0, 136, 7.06, -14.35, 0.98777, 133, 74.84, -63.85, 0.01223, 3, 136, -57.68, -37.76, 0.13928, 133, 7.79, -48.21, 0.64717, 132, 103.92, -42.97, 0.21354, 4, 136, -122.43, -61.18, 4e-05, 133, -59.25, -32.57, 0.00288, 132, 35.45, -50.23, 0.99703, 131, 153.7, -21.09, 5e-05, 2, 132, -56.89, -19.55, 0, 131, 60.33, -48.48, 1, 1, 131, 4.22, -36.62, 1, 1, 131, -23.47, -20.22, 1, 1, 131, -29.55, -0.06, 1], "hull": 28, "edges": [0, 54, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 18, 20, 20, 22, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 46, 48, 48, 50, 50, 52, 52, 54, 26, 28, 22, 24, 24, 26, 14, 16, 16, 18, 42, 44, 44, 46], "width": 140, "height": 131}}, "toufa16": {"toufa16": {"type": "mesh", "uvs": [0.47808, 0, 0.47442, 0.13946, 0.35024, 0.2959, 0.31957, 0.36994, 0.35141, 0.4868, 0.45043, 0.55791, 0.70252, 0.64498, 0.95461, 0.73205, 0.96962, 0.8146, 1, 0.98154, 1, 1, 0.99879, 1, 0.92699, 0.97621, 0.8698, 0.82724, 0.82933, 0.80043, 0.6186, 0.75659, 0.40786, 0.71276, 0.08909, 0.56919, 0, 0.4462, 0, 0.37298, 0.04743, 0.23751, 0.45415, 0], "triangles": [11, 9, 10, 11, 12, 9, 12, 8, 9, 12, 13, 8, 13, 14, 8, 14, 7, 8, 7, 14, 6, 14, 15, 6, 15, 16, 6, 16, 5, 6, 16, 17, 5, 5, 17, 4, 17, 18, 4, 18, 3, 4, 18, 19, 3, 19, 20, 3, 3, 20, 2, 2, 20, 1, 20, 21, 1, 1, 21, 0], "vertices": [1, 138, -65.9, 8.36, 1, 1, 138, 12.48, 41.47, 1, 3, 138, 116.43, 42.12, 0.24397, 139, 15.7, 39.57, 0.75593, 140, -47.12, 63.27, 0.0001, 2, 139, 61.96, 37.79, 0.7685, 140, -5.31, 43.39, 0.2315, 3, 139, 130.13, 61.16, 0.00209, 140, 66.56, 38, 0.80377, 141, 0.83, 48.79, 0.19414, 3, 140, 116.11, 60.51, 0.04091, 141, 53.31, 34.34, 0.94985, 142, -28.16, 42.07, 0.00924, 2, 142, 69.9, 27.52, 0.97312, 143, -29.18, 17.99, 0.02688, 1, 143, 67.29, 40.81, 1, 2, 143, 106.81, 9.22, 0.0428, 144, 11.72, 17.84, 0.9572, 1, 144, 113.17, 4.42, 1, 1, 144, 124.13, 1.85, 1, 1, 144, 124.04, 1.46, 1, 1, 144, 104.48, -18.44, 1, 2, 143, 89.18, -19.89, 0.20365, 144, 11.67, -16.19, 0.79635, 3, 142, 146.88, -42.03, 0.00276, 143, 68.1, -18.09, 0.95923, 144, -7.32, -25.54, 0.03801, 2, 142, 72.09, -46.01, 0.76239, 143, 0.1, -49.49, 0.23761, 2, 141, 99.53, -49.24, 0.4669, 142, -2.71, -49.99, 0.5331, 2, 140, 96.39, -58.02, 0.75207, 141, -37.75, -44.07, 0.24793, 2, 139, 127.02, -58.07, 0.23951, 140, 16.7, -70.35, 0.76049, 2, 139, 83.11, -66.2, 0.7152, 140, -26.87, -60.51, 0.2848, 2, 138, 124.26, -64.25, 0.29244, 139, -1.01, -65.76, 0.70756, 1, 138, -62.7, 1.09, 1], "hull": 22, "edges": [0, 42, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 28, 30, 30, 32, 10, 12, 12, 14, 14, 16, 16, 18], "width": 147, "height": 269}}, "toufa17": {"toufa17": {"type": "mesh", "uvs": [1, 0, 1, 0.08032, 0.98874, 0.15245, 0.97749, 0.22458, 0.96951, 0.2757, 0.8873, 0.42972, 0.69142, 0.51061, 0.49553, 0.5915, 0.40953, 0.64087, 0.36324, 0.7325, 0.40498, 0.80385, 0.52585, 0.87428, 0.64673, 0.94472, 0.48717, 1, 0.44614, 1, 0.4467, 0.92819, 0.28796, 0.87792, 0.12921, 0.82765, 0.00031, 0.74532, 0, 0.72936, 0, 0.61329, 0.36387, 0.48675, 0.4607, 0.4273, 0.54798, 0.33529, 0.66743, 0.20938, 0.75951, 0.11231, 0.85159, 0.01525, 0.93109, 0], "triangles": [3, 24, 2, 24, 25, 2, 2, 25, 1, 25, 26, 1, 26, 27, 1, 27, 0, 1, 5, 23, 4, 23, 24, 4, 4, 24, 3, 21, 22, 6, 6, 22, 5, 22, 23, 5, 7, 8, 21, 7, 21, 6, 19, 20, 8, 8, 20, 21, 17, 9, 10, 17, 18, 9, 18, 19, 9, 9, 19, 8, 16, 10, 11, 16, 17, 10, 12, 13, 15, 13, 14, 15, 15, 11, 12, 15, 16, 11], "vertices": [1, 101, -51, 6.06, 1, 1, 101, 17.53, 22.35, 1, 1, 101, 79.95, 33.29, 1, 2, 102, -17.77, 43.67, 0.22263, 101, 142.37, 44.24, 0.77737, 2, 102, 26.19, 52.88, 0.85899, 101, 186.61, 51.99, 0.14101, 2, 103, 21.28, 71.47, 0.85987, 102, 163.77, 61.77, 0.14013, 2, 104, -25.26, 49.42, 0.20629, 103, 116.55, 54.47, 0.79371, 3, 106, -44.53, 128.38, 0.00029, 105, -11.6, 76, 0.02814, 104, 71, 59.34, 0.97157, 3, 106, -13.18, 86.82, 0.07731, 105, 38.41, 61.56, 0.46845, 104, 121.27, 72.84, 0.45424, 4, 107, -24.58, 58.77, 0.03144, 106, 57.57, 45.64, 0.87497, 105, 119.72, 71.05, 0.08494, 104, 185.96, 123, 0.00866, 3, 108, -70.83, -16.53, 0.0049, 107, 31.96, 28.52, 0.91356, 106, 121.26, 38.26, 0.08154, 1, 108, -3.96, 15, 1, 1, 108, 62.9, 46.52, 1, 1, 108, 103.37, -13.37, 1, 1, 108, 101.43, -27.02, 1, 2, 108, 39.11, -17.98, 0.85951, 107, 123.68, -32.1, 0.14049, 1, 107, 55.46, -43.69, 1, 2, 107, -12.77, -55.27, 0.40873, 106, 110.42, -56.1, 0.59127, 2, 106, 27.97, -73.19, 0.96431, 105, 167.33, -41.78, 0.03569, 2, 106, 14.73, -68.67, 0.90908, 105, 154.02, -46.11, 0.09092, 2, 106, -81.37, -35.11, 0.00912, 105, 57, -76.91, 0.99088, 2, 104, 43.67, -38.89, 0.91903, 103, 158.46, -49.42, 0.08097, 3, 104, -15.17, -56.63, 0.10716, 103, 97.01, -50.25, 0.89209, 102, 199.44, -77.07, 0.00074, 2, 103, 13.28, -69.22, 0.53318, 102, 113.88, -70.01, 0.46682, 2, 102, -3.22, -60.34, 0.50768, 101, 153.48, -60.2, 0.49232, 1, 101, 63.51, -49.78, 1, 1, 101, -26.46, -39.36, 1, 1, 101, -45.65, -16.46, 1], "hull": 28, "edges": [0, 54, 0, 2, 8, 10, 14, 16, 16, 18, 18, 20, 24, 26, 26, 28, 28, 30, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 52, 54, 44, 46, 46, 48, 6, 8, 48, 50, 50, 52, 2, 4, 4, 6, 10, 12, 12, 14, 30, 32, 32, 34, 20, 22, 22, 24], "width": 149, "height": 385}}, "toufa18": {"toufa18": {"type": "mesh", "uvs": [1, 0, 1, 0.05005, 0.95757, 0.20841, 0.67726, 0.32272, 0.39696, 0.43704, 0.27142, 0.48692, 0.22035, 0.58277, 0.26891, 0.63851, 0.5195, 0.68963, 0.77009, 0.74075, 0.80481, 0.92301, 0.58589, 0.99109, 0.44971, 1, 0.2915, 1, 0.1373, 0.9619, 0.02734, 0.90544, 0.42913, 0.92409, 0.51641, 0.80571, 0.29777, 0.73295, 0.07914, 0.66019, 0, 0.57268, 0, 0.4667, 0.05598, 0.393, 0.52376, 0.1965, 0.99154, 0], "triangles": [2, 23, 1, 23, 24, 1, 24, 0, 1, 3, 4, 23, 3, 23, 2, 4, 5, 22, 5, 21, 22, 4, 22, 23, 19, 20, 6, 6, 20, 5, 20, 21, 5, 17, 18, 8, 19, 7, 18, 18, 7, 8, 19, 6, 7, 17, 9, 10, 17, 8, 9, 11, 12, 16, 12, 13, 16, 11, 16, 10, 16, 17, 10, 13, 14, 16, 14, 15, 16], "vertices": [1, 71, -67.22, -33.79, 1, 1, 71, -42.36, -14.07, 1, 1, 71, 43.14, 39.71, 1, 2, 72, 22.99, 35.55, 0.97836, 71, 145.03, 27.86, 0.02164, 3, 74, -20.29, 72.44, 0.00107, 73, 40.19, 41.3, 0.92769, 72, 124.23, 52.14, 0.07125, 2, 74, 7.94, 36.94, 0.33711, 73, 84.8, 33.07, 0.66289, 2, 75, -5.17, 30.28, 0.06968, 74, 67.09, 17.73, 0.93032, 2, 75, 26.92, 10.86, 0.97131, 74, 103.51, 26.73, 0.02869, 2, 76, -31.65, 12.1, 0.03328, 75, 97.69, 26.81, 0.96672, 2, 76, 20.46, 62.57, 0.99231, 75, 168.46, 42.76, 0.00769, 2, 77, -16.92, 52.94, 0.76535, 76, 132.45, 32.7, 0.23465, 1, 77, 54.34, 52.43, 1, 2, 78, -29.94, 18.76, 0.1063, 77, 85.7, 35.33, 0.8937, 2, 78, 8.22, 33.69, 0.89672, 77, 118.12, 10.27, 0.10328, 1, 78, 54.21, 25.75, 1, 1, 78, 93.77, 2.8, 1, 3, 78, -7.44, -24.11, 0.02129, 77, 60.49, -6.01, 0.9781, 76, 100.8, -59.32, 0.00061, 3, 77, -3.29, -51.58, 0.00674, 76, 37.51, -13.08, 0.98855, 75, 143.63, -30.7, 0.00471, 1, 75, 70.59, -30.79, 1, 2, 75, -2.45, -30.88, 0.70962, 74, 112.3, -23.55, 0.29038, 1, 74, 55.05, -38.42, 1, 2, 74, -11.81, -31.74, 0.49688, 73, 115, -31.69, 0.50312, 1, 73, 68.5, -46.89, 1, 2, 72, 8.67, -52.66, 0.53425, 71, 107.04, -53.01, 0.46575, 1, 71, -65.86, -35.5, 1], "hull": 25, "edges": [0, 48, 0, 2, 2, 4, 8, 10, 10, 12, 12, 14, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 38, 40, 40, 42, 42, 44, 4, 6, 6, 8, 44, 46, 46, 48, 14, 16, 16, 18, 34, 36, 36, 38], "width": 116, "height": 279}}, "toufa19": {"toufa19": {"type": "mesh", "uvs": [0.66366, 0, 0.77495, 0.02614, 0.85584, 0.09972, 0.93672, 0.17331, 0.9204, 0.26258, 0.90407, 0.35186, 0.78562, 0.43197, 0.66716, 0.51209, 0.70092, 0.60774, 0.84483, 0.67857, 0.98874, 0.7494, 1, 0.80164, 1, 0.85389, 0.84512, 0.96887, 0.44844, 1, 0.32028, 1, 0.3279, 0.86059, 0.24045, 0.79205, 0.17241, 0.75078, 0.02721, 0.6627, 0, 0.60997, 0, 0.56472, 0.04199, 0.51463, 0.18379, 0.4661, 0.32559, 0.41758, 0.4005, 0.37504, 0.466, 0.23815, 0.51759, 0.13031, 0.54339, 0.07639, 0.56919, 0.02248, 0.61319, 0], "triangles": [13, 16, 12, 14, 15, 16, 13, 14, 16, 16, 17, 9, 11, 16, 9, 17, 18, 8, 11, 9, 10, 12, 16, 11, 8, 18, 19, 17, 8, 9, 24, 25, 7, 8, 23, 7, 7, 25, 6, 23, 24, 7, 23, 20, 21, 21, 22, 23, 23, 19, 20, 19, 23, 8, 5, 26, 4, 5, 25, 26, 6, 25, 5, 29, 30, 0, 1, 29, 0, 3, 27, 2, 3, 4, 27, 28, 29, 1, 28, 1, 2, 27, 28, 2, 4, 26, 27], "vertices": [1, 150, -75.12, -51.6, 1, 1, 150, -55.02, 2.61, 1, 1, 150, 19.45, 52.42, 1, 1, 150, 93.91, 102.23, 1, 2, 150, 193.06, 112.04, 0.49143, 151, -28.65, 108.91, 0.50857, 1, 151, 67.77, 134.03, 1, 1, 152, -26.29, 114.56, 1, 1, 152, 73.52, 86, 1, 2, 152, 172.54, 127.92, 0.50857, 153, 63.99, 111.83, 0.49143, 1, 153, 161.19, 142.55, 1, 2, 153, 258.38, 173.26, 0.48, 154, 117.2, 158.11, 0.52, 1, 154, 170.07, 133.39, 1, 2, 154, 220.42, 104.28, 0.51143, 155, -34.26, 114.61, 0.48857, 1, 155, 107.89, 82.9, 1, 1, 155, 190.13, -78.84, 1, 1, 155, 205.94, -134.17, 1, 2, 154, 75.84, -160.71, 0.51143, 155, 55.81, -173.5, 0.48857, 1, 154, -9.87, -156.51, 1, 2, 153, 123.68, -167.62, 0.48, 154, -64.92, -159.97, 0.52, 1, 153, 8.44, -191.74, 1, 2, 152, 255.58, -175.65, 0.50857, 153, -50.58, -181.29, 0.49143, 2, 152, 206.9, -188.55, 0.50857, 153, -97.34, -162.59, 0.49143, 1, 152, 148.18, -184.61, 1, 2, 150, 472.9, -174.99, 0.01143, 152, 79.66, -136.9, 0.98857, 2, 151, 220.81, -88.21, 0.49143, 152, 11.14, -89.2, 0.50857, 1, 151, 165.15, -71.65, 1, 2, 150, 201.22, -93.63, 0.49143, 151, 11.44, -92.98, 0.50857, 1, 150, 79.01, -91.37, 1, 1, 150, 17.9, -90.24, 1, 1, 150, -43.2, -89.11, 1, 1, 150, -71.24, -73.93, 1], "hull": 31, "edges": [0, 60, 0, 2, 14, 16, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 38, 40, 40, 42, 42, 44, 48, 50, 58, 60, 16, 18, 18, 20, 34, 36, 36, 38, 44, 46, 46, 48, 10, 12, 12, 14, 6, 8, 8, 10, 50, 52, 52, 54, 2, 4, 4, 6, 54, 56, 56, 58], "width": 198, "height": 488}}, "touying": {"touying": {"type": "mesh", "uvs": [0.70886, 0.0292, 0.96779, 0.11861, 0.97671, 0.14266, 0.95425, 0.1504, 0.73459, 0.17539, 0.57644, 0.30018, 0.41829, 0.42497, 0.26547, 0.71248, 0.11264, 1, 0.08702, 1, 0.00472, 0.86502, 0.04864, 0.77184, 0.10112, 0.62599, 0.22383, 0.28493, 0.3168, 0.17671, 0.47835, 0.05812], "triangles": [1, 4, 0, 3, 1, 2, 3, 4, 1, 15, 0, 4, 5, 15, 4, 14, 15, 5, 13, 14, 5, 6, 13, 5, 12, 13, 6, 7, 12, 6, 11, 12, 7, 11, 8, 10, 8, 9, 10, 11, 7, 8], "vertices": [-10.14, -24.48, -48.96, 58.58, -44.44, 68.53, -36.73, 66.57, 24.42, 30.22, 98.34, 40.18, 172.25, 50.14, 290.38, 116.23, 408.51, 182.32, 414.83, 177.09, 397.36, 114.64, 360.46, 92.08, 306.71, 53.45, 181.01, -36.9, 127.81, -54.54, 54.79, -61.71], "hull": 16, "edges": [0, 30, 0, 2, 2, 4, 4, 6, 6, 8, 16, 18, 18, 20, 20, 22, 26, 28, 28, 30, 12, 14, 14, 16, 22, 24, 24, 26, 8, 10, 10, 12], "width": 141, "height": 191}}, "tun": {"tun": {"type": "mesh", "uvs": [0.36944, 0, 0.39513, 0, 0.74297, 0.15761, 0.76173, 0.20193, 0.76151, 0.25648, 0.79231, 0.51574, 0.93283, 0.76026, 1, 0.93935, 1, 0.97981, 0.99957, 1, 0.77837, 1, 0.49978, 1, 0.27814, 1, 0, 1, 0, 0.94611, 0.00033, 0.91054, 0.00374, 0.91149, 0.0568, 0.84724, 0.04926, 0.67194, 0.04352, 0.7125, 0.06799, 0.52129, 0.11663, 0.38118, 0.22415, 0.15633, 0.33629, 0.03153, 0.47726, 0.85835, 0.74923, 0.81773, 0.27167, 0.81519, 0.2571, 0.56385, 0.39308, 0.30489, 0.64563, 0.33282, 0.67153, 0.59939, 0.47402, 0.54608], "triangles": [12, 13, 17, 9, 10, 8, 12, 24, 11, 11, 25, 10, 27, 28, 31, 31, 28, 29, 2, 29, 1, 28, 23, 1, 29, 28, 1, 1, 23, 0, 4, 2, 3, 8, 10, 7, 10, 25, 6, 11, 24, 25, 10, 6, 7, 24, 30, 25, 24, 31, 30, 25, 5, 6, 25, 30, 5, 31, 29, 30, 30, 29, 5, 29, 4, 5, 29, 2, 4, 17, 14, 16, 17, 26, 12, 12, 26, 24, 17, 13, 14, 14, 15, 16, 26, 31, 24, 26, 17, 18, 26, 18, 27, 27, 18, 20, 26, 27, 31, 18, 19, 20, 20, 21, 27, 21, 22, 27, 27, 22, 28, 22, 23, 28], "vertices": [2, 2, -253.2, 159.53, 0.93143, 167, -1046.92, -482.83, 0.06857, 1, 2, -216.9, 159.53, 1, 2, 2, 274.59, 17.53, 0.93143, 167, -519.12, -624.84, 0.06857, 2, 2, 301.11, -22.4, 0.93143, 167, -492.61, -664.77, 0.06857, 3, 5, 30.53, 184.2, 0.92736, 6, -225.16, 252.01, 0.00407, 167, -492.92, -713.92, 0.06857, 3, 5, 266.06, 215.62, 0.46667, 6, 10.37, 220.62, 0.46476, 167, -449.4, -947.52, 0.06857, 3, 5, 496.31, 402.55, 0.00776, 6, 281.56, 340.67, 0.92367, 167, -250.85, -1167.83, 0.06857, 2, 6, 464.46, 380.6, 0.93143, 167, -155.93, -1329.18, 0.06857, 2, 6, 499.1, 369.24, 0.93143, 167, -155.93, -1365.64, 0.06857, 1, 170, 846.06, -40.36, 1, 1, 170, 533.52, -40.36, 1, 1, 170, 139.87, -40.36, 1, 1, 170, -173.31, -40.36, 1, 1, 170, -566.33, -40.36, 1, 4, 3, 961.86, -95.46, 0, 4, 391.29, -395.47, 0.93143, 5, 595.6, -922.41, 0, 167, -1568.93, -1335.27, 0.06857, 4, 3, 935.75, -114.04, 0, 4, 359.24, -395.47, 0.93143, 5, 563.62, -920.3, 0, 167, -1568.47, -1303.23, 0.06857, 4, 3, 933.58, -109.64, 0, 4, 360.03, -390.63, 0.93143, 5, 564.72, -915.52, 0, 167, -1563.64, -1304.09, 0.06857, 4, 3, 842.56, -83.41, 0, 4, 301.06, -316.5, 0.93143, 5, 510.77, -837.66, 0, 167, -1488.67, -1246.2, 0.06857, 3, 3, 721.49, -185.41, 0.00012, 4, 143.28, -329.44, 0.9313, 167, -1499.32, -1088.25, 0.06857, 3, 4, 179.94, -337.03, 0.93143, 5, 388.56, -850.16, 0, 167, -1507.44, -1124.79, 0.06857, 3, 3, 596.39, -244.34, 0.19442, 4, 7.18, -304.95, 0.73701, 167, -1472.86, -952.52, 0.06857, 3, 3, 453.95, -263.57, 0.59433, 4, -120.04, -238.06, 0.3371, 167, -1404.14, -826.28, 0.06857, 3, 3, 200.74, -260.86, 0.93125, 4, -324.81, -89.09, 0.00017, 167, -1252.21, -623.69, 0.06857, 3, 3, 16.35, -199.58, 0.93143, 5, -202.85, -405.39, 0, 167, -1093.75, -511.24, 0.06857, 5, 3, 499.3, 401.6, 0.1345, 4, 302.46, 277.69, 0.39673, 5, 551.39, -244.86, 0.00173, 6, 165.05, -298.55, 0.39846, 167, -894.56, -1256.21, 0.06857, 5, 3, 242.53, 689.85, 0, 4, 260.3, 661.41, 0.17674, 5, 534.65, 140.81, 0.0677, 6, 249.97, 78.02, 0.68699, 167, -510.27, -1219.61, 0.06857, 5, 3, 639.74, 144.33, 0.06928, 4, 267.79, -13.35, 0.6561, 5, 497.59, -532.98, 0.00089, 6, 37.61, -562.5, 0.20516, 167, -1185.07, -1217.32, 0.06857, 5, 3, 469.3, -6.19, 0.40024, 4, 41.65, -37.22, 0.40057, 5, 270.37, -541.87, 0.00057, 6, -184, -511.53, 0.13005, 167, -1205.66, -990.86, 0.06857, 6, 3, 167.52, 10.78, 0.03872, 4, -194.43, 151.53, 0.18421, 5, 47.26, -337.95, 0.0005, 6, -345.86, -256.26, 0.11468, 2, -219.79, -115.17, 0.637, 167, -1013.51, -757.54, 0.02489, 6, 3, -23.22, 313.42, 0.01293, 4, -174.44, 508.7, 0.06151, 5, 90.78, 17.12, 0.51092, 6, -210.81, 74.99, 0.14793, 2, 137.05, -140.33, 0.21272, 167, -656.67, -782.7, 0.05398, 6, 3, 148.82, 484.97, 0.02082, 4, 65.18, 548.78, 0.14813, 5, 332.53, 41.3, 0.26408, 6, 28.83, 34.96, 0.40735, 2, 173.65, -380.52, 0.09775, 167, -620.07, -1022.88, 0.06187, 6, 3, 275.12, 231.52, 0.06741, 4, 21.2, 269.04, 0.24857, 5, 270.17, -234.93, 0.05084, 6, -103.74, -215.27, 0.26518, 2, -105.42, -332.48, 0.32147, 167, -899.14, -974.85, 0.04653], "hull": 24, "edges": [0, 46, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 18, 20, 20, 22, 22, 24, 24, 26], "width": 618, "height": 395}}, "yanbai": {"yanbai": {"type": "mesh", "uvs": [0.44961, 0.67391, 0.58464, 0.73837, 0.70684, 0.64866, 1, 0.43344, 1, 0.74893, 0.98463, 1, 0.86571, 1, 0.72691, 0.93946, 0.58812, 0.87892, 0.45108, 0.81916, 0.1274, 0.67798, 0, 0.46877, 0, 0.204, 0.01865, 0, 0.12548, 0, 0.25657, 0.06497], "triangles": [5, 6, 4, 4, 6, 2, 8, 2, 7, 6, 7, 2, 8, 1, 2, 2, 3, 4, 9, 1, 8, 10, 0, 9, 9, 0, 1, 10, 11, 14, 14, 12, 13, 10, 15, 0, 10, 14, 15, 14, 11, 12], "vertices": [1, 156, 2.56, -40.16, 1, 2, 156, 10.17, -77.35, 0.40256, 172, -29.1, 81.09, 0.59744, 1, 172, -5.66, 54.65, 1, 1, 172, 50.59, -8.8, 1, 1, 172, 15.3, -24.16, 1, 1, 172, -14.48, -32.5, 1, 1, 172, -27.53, -2.52, 1, 1, 172, -35.99, 35.43, 1, 2, 156, -5.17, -85.07, 0.38038, 172, -44.45, 73.38, 0.61962, 1, 156, -13.52, -47.6, 1, 1, 156, -33.25, 40.89, 1, 1, 156, -23.83, 83.2, 1, 1, 156, 5.79, 96.09, 1, 1, 156, 30.66, 101.31, 1, 1, 156, 42.38, 74.38, 1, 1, 156, 49.5, 38.16, 1], "hull": 16, "edges": [0, 30, 0, 2, 6, 8, 8, 10, 10, 12, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 18, 20, 0, 18, 16, 18, 2, 16, 2, 4, 4, 6, 12, 14, 14, 16, 4, 14], "width": 123, "height": 56}}, "yaodai": {"yaodai": {"type": "mesh", "uvs": [1, 0.10004, 1, 0.35857, 0.31387, 1, 0, 0.92409, 0, 0.66961, 0.5, 0, 0.75, 0], "triangles": [6, 0, 1, 2, 3, 4, 2, 4, 1, 5, 1, 4, 6, 1, 5], "vertices": [-47.1, 43.22, 26.97, 87.81, 299.48, 51.02, 318.34, -29.51, 245.43, -73.41, -11.08, -81.46, -43.42, -27.75], "hull": 7, "edges": [10, 8, 6, 8, 6, 4, 4, 2, 2, 0, 10, 12, 0, 12], "width": 114, "height": 152}}, "yaodai1": {"yaodai1": {"type": "mesh", "uvs": [0.18314, 0, 1, 0.46917, 1, 0.73054, 0.96362, 1, 0.87528, 0.9956, 0.27483, 0.72415, 0, 0.39348, 0, 0.3123, 0.13587, 0], "triangles": [0, 6, 7, 5, 0, 1, 0, 7, 8, 5, 6, 0, 4, 5, 1, 2, 4, 1, 3, 4, 2], "vertices": [-95.52, 69.25, 169.14, -8.63, 169.14, -52.02, 157.35, -96.75, 128.73, -96.02, -65.82, -50.96, -154.86, 3.93, -154.86, 17.41, -110.84, 69.25], "hull": 9, "edges": [0, 16, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16], "width": 324, "height": 166}}, "yaodai2": {"yaodai2": {"type": "mesh", "uvs": [1, 0.5, 0.82583, 1, 0, 0.80155, 0, 0.31874, 0.60214, 0.38696, 0.64779, 0, 1, 0], "triangles": [5, 6, 0, 4, 5, 0, 2, 3, 4, 1, 4, 0, 2, 4, 1], "vertices": [-109.01, -10.64, -0.78, 113.75, 485.46, 42.78, 480.39, -81.39, 125.06, -49.31, 94, -147.73, -114.26, -139.23], "hull": 7, "edges": [10, 12, 10, 8, 8, 6, 4, 6, 4, 2, 0, 12, 2, 0], "width": 269, "height": 117}}, "youshou": {"youshou": {"type": "mesh", "uvs": [0.82008, 0.07965, 0.89283, 0.22194, 1, 0.43155, 1, 0.58904, 0.94085, 0.70449, 0.87978, 0.8237, 0.81871, 0.94291, 0.73345, 1, 0.71169, 1, 0.43226, 0.83304, 0.44417, 0.72205, 0.45609, 0.61107, 0.468, 0.50009, 0.47991, 0.38911, 0.3832, 0.35381, 0.32363, 0.36916, 0.2766, 0.48981, 0.15255, 0.56691, 0, 0.53767, 0, 0.49581, 0.01237, 0.416, 0.09877, 0.29575, 0.18518, 0.17551, 0.32004, 0.08776, 0.45491, 0, 0.61794, 0, 0.73803, 0.0112, 0.75005, 0.63897, 0.74692, 0.37355, 0.66226, 0.1893], "triangles": [10, 11, 27, 5, 27, 4, 5, 10, 27, 5, 9, 10, 6, 9, 5, 8, 9, 6, 7, 8, 6, 13, 29, 28, 28, 1, 2, 27, 28, 2, 3, 27, 2, 12, 13, 28, 27, 12, 28, 11, 12, 27, 4, 27, 3, 15, 20, 21, 16, 20, 15, 19, 20, 16, 17, 18, 19, 16, 17, 19, 29, 14, 23, 22, 23, 14, 15, 21, 22, 14, 15, 22, 29, 25, 26, 29, 26, 0, 29, 0, 1, 28, 29, 1, 23, 24, 29, 29, 24, 25, 13, 14, 29], "vertices": [1, 25, 45.79, 59.3, 1, 2, 25, 119.11, 63.46, 0.9585, 26, -62.11, 47.18, 0.0415, 3, 25, 227.13, 69.6, 0.03533, 26, 39.24, 85.05, 0.96436, 27, -119.83, 61.35, 0.00031, 2, 26, 115.77, 86.12, 0.82582, 27, -45.22, 78.4, 0.17418, 2, 26, 172.16, 66.81, 0.24566, 27, 13.96, 71.3, 0.75434, 2, 26, 230.38, 46.86, 0.00041, 27, 75.07, 63.97, 0.99959, 1, 27, 136.17, 56.63, 1, 1, 27, 169.68, 34.56, 1, 1, 27, 171.33, 27.35, 1, 2, 26, 237.06, -105.22, 0.00744, 27, 113.4, -83.35, 0.99256, 3, 28, 189.69, 178.28, 0.00019, 26, 183.07, -101.93, 0.12898, 27, 59.91, -91.42, 0.87083, 4, 25, 258.51, -132.46, 0.00085, 28, 143.87, 149.54, 0.00924, 26, 129.08, -98.64, 0.52155, 27, 6.43, -99.49, 0.46836, 4, 25, 207.92, -113.32, 0.04293, 28, 98.04, 120.81, 0.06408, 26, 75.09, -95.35, 0.78686, 27, -47.05, -107.56, 0.10613, 4, 25, 157.33, -94.17, 0.22868, 28, 52.22, 92.07, 0.3316, 26, 21.11, -92.06, 0.43578, 27, -100.54, -115.63, 0.00394, 4, 25, 131.58, -120.86, 0.12414, 28, 57.91, 55.42, 0.73587, 29, -30.89, 59.39, 0.03591, 26, 4.41, -125.18, 0.10409, 4, 25, 133.01, -142.4, 0.02858, 28, 75.93, 43.54, 0.70073, 29, -14.36, 45.51, 0.24642, 26, 12.16, -145.32, 0.02427, 2, 28, 132.62, 65.45, 0.03581, 29, 44.48, 60.72, 0.96419, 1, 29, 97.95, 42.69, 1, 1, 29, 111.34, -9.4, 1, 1, 29, 93.67, -19.49, 1, 1, 29, 57.91, -35.08, 1, 2, 28, 92.56, -39.17, 0.65361, 29, -7.4, -38.57, 0.34639, 1, 28, 28.09, -50.18, 1, 2, 25, 1.48, -104.89, 0.17265, 28, -33.45, -38.57, 0.82735, 2, 25, -26.46, -48.85, 0.70975, 28, -94.98, -26.95, 0.29025, 2, 25, -10.78, 4.32, 0.99717, 28, -127.86, 17.67, 0.00283, 1, 25, 5.99, 41.95, 1, 2, 26, 141.23, 1.49, 0.6917, 27, -2.63, 0.96, 0.3083, 2, 28, -7.72, 160.67, 0.0009, 26, 12.27, -1.39, 0.9991, 3, 25, 81.72, -7.24, 0.97813, 28, -62.74, 84.38, 0.02128, 26, -76.87, -31.43, 0.0006], "hull": 27, "edges": [0, 52, 4, 6, 12, 14, 14, 16, 16, 18, 36, 38, 38, 40, 48, 50, 50, 52, 34, 36, 34, 32, 32, 30, 30, 28, 28, 26, 40, 42, 42, 44, 44, 46, 46, 48, 0, 2, 2, 4, 6, 8, 8, 10, 10, 12, 18, 20, 20, 22, 22, 24, 24, 26], "width": 151, "height": 209}}, "youshou2": {"youshou2": {"type": "mesh", "uvs": [0.61746, 0.05388, 0.73031, 0.12729, 0.8253, 0.29705, 1, 0.88369, 1, 0.91206, 0.84127, 1, 0.6113, 1, 0.42426, 0.97571, 0.2576, 0.79204, 0, 0.38531, 0, 0.21566, 0.08425, 0, 0.21129, 0, 0.32795, 0.00265], "triangles": [7, 8, 3, 5, 6, 4, 4, 6, 3, 3, 6, 7, 8, 2, 3, 2, 9, 1, 0, 1, 13, 9, 2, 8, 1, 9, 13, 13, 9, 12, 12, 10, 11, 12, 9, 10], "vertices": [2, 23, -5.06, 116.31, 0.90571, 167, -498.7, 353.46, 0.09429, 2, 23, 55.9, 135.18, 0.90571, 167, -464.84, 299.36, 0.09429, 2, 23, 184.14, 130.67, 0.90571, 167, -436.34, 174.24, 0.09429, 1, 24, 27.6, 73.82, 1, 1, 24, 47.9, 68.85, 1, 2, 23, 686.16, 2.59, 0.00396, 24, 99.53, 7.18, 0.99604, 1, 24, 83.12, -59.83, 1, 3, 23, 636.8, -113.75, 0.02404, 24, 52.38, -110.07, 0.88167, 167, -556.66, -325.93, 0.09429, 3, 23, 493.15, -127.41, 0.6515, 24, -90.99, -126.44, 0.25421, 167, -606.65, -190.57, 0.09429, 2, 23, 183.6, -125.32, 0.90571, 167, -683.93, 109.19, 0.09429, 2, 23, 62.74, -93.29, 0.90571, 167, -683.93, 234.23, 0.09429, 2, 23, -84.43, -28.14, 0.90571, 167, -658.66, 393.17, 0.09429, 1, 23, -74.66, 8.7, 1, 1, 23, -63.81, 42.03, 1], "hull": 14, "edges": [0, 26, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26], "width": 134, "height": 324}}, "youshou3": {"youshou3": {"type": "mesh", "uvs": [0.39455, 0, 0.54367, 0.02488, 0.74663, 0.29952, 0.79852, 0.64688, 0.90604, 0.66944, 1, 0.86251, 1, 0.88541, 0.91786, 1, 0.76792, 1, 0.54864, 0.9617, 0.47321, 0.9264, 0.01578, 0.2778, 0, 0.13055, 0, 0.05419, 0.23035, 0], "triangles": [7, 8, 6, 5, 8, 9, 5, 9, 10, 0, 12, 14, 12, 13, 14, 0, 11, 12, 6, 8, 5, 10, 3, 5, 3, 11, 2, 3, 10, 11, 5, 3, 4, 11, 1, 2, 1, 11, 0], "vertices": [1, 24, -37.67, 27.08, 1, 2, 24, -11.76, 69.4, 0.63429, 23, 586.99, 83.84, 0.36571, 2, 24, 165.47, 92.24, 0.90857, 167, -333.26, -387.64, 0.09143, 2, 24, 374.17, 58.06, 0.90857, 167, -316.8, -598.49, 0.09143, 2, 24, 395.58, 87.91, 0.90857, 167, -282.72, -612.18, 0.09143, 3, 24, 516.49, 88.96, 0.76623, 25, 6.42, 96.1, 0.15417, 167, -252.93, -729.37, 0.0796, 3, 24, 530, 85.66, 0.73362, 25, 19.76, 92.17, 0.1872, 167, -252.93, -743.28, 0.07918, 3, 24, 591.36, 43.82, 0.07349, 25, 79.11, 47.53, 0.90286, 167, -278.97, -812.83, 0.02366, 2, 24, 580.06, -2.34, 0.01714, 25, 65.67, 1.94, 0.98286, 2, 25, 23.71, -58.17, 0.98286, 167, -396.02, -789.59, 0.01714, 3, 24, 514.44, -82.46, 0.00562, 25, -3.61, -75.04, 0.98286, 167, -419.93, -768.16, 0.01153, 2, 24, 97.55, -129.65, 0.90857, 167, -564.93, -374.46, 0.09143, 2, 24, 9.55, -113.25, 0.90857, 167, -569.94, -285.08, 0.09143, 2, 24, -35.47, -102.23, 0.90857, 167, -569.94, -238.73, 0.09143, 1, 24, -50.06, -23.48, 1], "hull": 15, "edges": [0, 28, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28], "width": 141, "height": 267}}, "zuoshou": {"zuoshou": {"type": "mesh", "uvs": [0.53843, 0, 0.48557, 0.08227, 0.43415, 0.16229, 0.38273, 0.24232, 0.56886, 0.18866, 0.79995, 0.12092, 0.85549, 0.15758, 0.66919, 0.24676, 0.57793, 0.29045, 0.71614, 0.27047, 0.89464, 0.31192, 1, 0.40032, 1, 0.44188, 0.74081, 0.38026, 0.71853, 0.42935, 0.71847, 0.46401, 0.71804, 0.71781, 0.4865, 0.98956, 0.44271, 1, 0.31107, 1, 0, 0.92956, 0, 0.89828, 0.1229, 0.75956, 0.10811, 0.72982, 0.00907, 0.34214, 0.07526, 0.25294, 0.15465, 0.14595, 0.19435, 0.09245, 0.23405, 0.03895, 0.43609, 0, 0.24971, 0.24947, 0.28966, 0.16538, 0.34842, 0.08129, 0.59702, 0.41072, 0.52157, 0.32915, 0.34849, 0.32697, 0.17985, 0.34764], "triangles": [15, 33, 14, 14, 33, 13, 33, 34, 13, 8, 9, 13, 34, 8, 13, 11, 12, 13, 13, 10, 11, 13, 9, 10, 3, 4, 8, 8, 4, 7, 7, 5, 6, 7, 4, 5, 26, 27, 31, 31, 32, 2, 31, 27, 32, 2, 32, 1, 27, 28, 32, 32, 29, 1, 1, 29, 0, 32, 28, 29, 36, 30, 35, 36, 25, 30, 8, 34, 3, 35, 30, 3, 25, 26, 30, 30, 31, 3, 30, 26, 31, 3, 31, 2, 34, 35, 3, 24, 25, 36, 18, 19, 17, 22, 17, 19, 22, 19, 21, 17, 22, 16, 21, 19, 20, 22, 23, 16, 16, 23, 15, 23, 36, 33, 33, 36, 35, 23, 24, 36, 15, 23, 33, 33, 35, 34], "vertices": [1, 19, 100.67, 44.35, 1, 1, 19, 49.71, 31.95, 1, 2, 18, 157.88, 18.86, 0.48571, 19, 0.15, 19.89, 0.51429, 1, 18, 107.02, 22.75, 1, 1, 19, 7.21, -22.76, 1, 1, 19, 78.08, -60.38, 1, 1, 19, 67.57, -86.07, 1, 1, 19, -7.55, -66.62, 1, 3, 18, 91.71, -40.54, 0.3261, 19, -44.36, -57.09, 0.40286, 20, 30.26, 40.44, 0.27105, 3, 18, 112.89, -77.9, 0.01897, 21, -16.43, 15, 0.32435, 20, 67.44, 18.95, 0.65668, 1, 21, 41.94, 22.56, 1, 1, 21, 97.42, -5.67, 1, 1, 21, 111.08, -26.93, 1, 2, 21, 25.85, -37.18, 0.34387, 20, 24.11, -32.36, 0.65613, 2, 17, 328.6, -102.2, 0.21429, 20, -2.11, -48.1, 0.78571, 2, 17, 307.6, -103.89, 0.54, 20, -17.39, -62.6, 0.46, 2, 17, 153.78, -116.25, 0.90163, 167, -1015.96, 289.74, 0.09837, 2, 17, -16.49, -60.86, 0.90571, 167, -1084.96, 124.51, 0.09429, 2, 17, -23.87, -48.37, 0.90571, 167, -1098.01, 118.17, 0.09429, 1, 17, -27.05, -9.27, 1, 2, 17, 8.13, 86.59, 0.90571, 167, -1229.93, 161, 0.09429, 2, 17, 27.09, 88.13, 0.90571, 167, -1229.93, 180.02, 0.09429, 2, 17, 114.12, 58.46, 0.90571, 167, -1193.31, 264.36, 0.09429, 2, 17, 131.78, 64.32, 0.90571, 167, -1197.72, 282.44, 0.09429, 2, 17, 364.32, 112.82, 0.48286, 18, 22.65, 117.44, 0.51714, 2, 18, 79.95, 110.53, 0.62089, 19, -102.35, 82.89, 0.37911, 2, 18, 148.67, 102.24, 0.48571, 19, -34.44, 96.31, 0.51429, 1, 19, -0.48, 103.01, 1, 1, 19, 33.47, 109.72, 1, 1, 19, 84.89, 70.45, 1, 2, 18, 93.8, 60.37, 0.85184, 19, -73.64, 39.5, 0.14816, 2, 18, 146.29, 60.37, 0.48571, 19, -23.73, 55.76, 0.51429, 1, 19, 29.08, 67.24, 1, 4, 17, 336.96, -65.19, 0.27198, 18, 21.79, -62.66, 0.07555, 19, -103.99, -99.79, 0.01225, 20, -18.83, -14.05, 0.64022, 2, 17, 384.57, -38.76, 0.48286, 18, 64.99, -29.52, 0.51714, 2, 17, 381.71, 12.75, 0.48286, 18, 54.58, 21.02, 0.51714, 2, 17, 365.12, 61.83, 0.48286, 18, 30.94, 67.11, 0.51714], "hull": 30, "edges": [0, 58, 6, 8, 8, 10, 10, 12, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 56, 58, 12, 14, 14, 16, 48, 50, 50, 52, 52, 54, 54, 56, 0, 2, 2, 4, 4, 6, 28, 30, 30, 32], "width": 133, "height": 267}}, "zuoshou1": {"zuoshou1": {"type": "mesh", "uvs": [0.77148, 0, 1, 0.06307, 1, 0.09684, 0.74318, 0.82006, 0.46973, 1, 0.28662, 1, 0.21982, 0.99943, 0.0409, 0.90857, 0, 0.72644, 0, 0.62101, 0.67015, 0], "triangles": [4, 5, 3, 8, 3, 5, 5, 6, 7, 5, 7, 8, 2, 3, 9, 2, 10, 0, 10, 2, 9, 3, 8, 9, 0, 1, 2], "vertices": [1, 16, 576.86, 36.97, 1, 2, 16, 572.77, -57.39, 0.91429, 167, -1101.94, 102.49, 0.08571, 2, 16, 552.73, -65.91, 0.91429, 167, -1101.94, 80.71, 0.08571, 2, 16, 85.96, -160.28, 0.91429, 167, -1197.73, -385.77, 0.08571, 2, 16, -60.76, -111.83, 0.91429, 167, -1299.73, -501.83, 0.08571, 1, 16, -87.48, -48.98, 1, 1, 16, -96.89, -25.91, 1, 2, 16, -69.07, 58.44, 0.91429, 167, -1459.68, -442.86, 0.08571, 2, 16, 33.07, 118.45, 0.91429, 167, -1474.94, -325.38, 0.08571, 2, 16, 95.65, 145.06, 0.91429, 167, -1474.94, -257.38, 0.08571, 2, 16, 562.07, 71.75, 0.91429, 167, -1224.97, 143.17, 0.08571], "hull": 11, "edges": [0, 20, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20], "width": 165, "height": 283}}, "zuoshou2": {"zuoshou2": {"type": "mesh", "uvs": [0.91655, 0, 1, 0.17938, 1, 0.30862, 0.88893, 0.548, 0.61989, 0.88614, 0.39561, 0.99425, 0.21908, 1, 0.02982, 1, 0, 0.84233, 0, 0.76789, 0.12616, 0.61392, 0.07115, 0.58584, 0.26591, 0.16012, 0.43551, 0.02575, 0.72368, 0], "triangles": [14, 0, 1, 13, 14, 12, 1, 2, 14, 14, 2, 12, 3, 12, 2, 11, 12, 3, 10, 11, 3, 4, 8, 9, 4, 10, 3, 4, 9, 10, 5, 8, 4, 6, 7, 8, 5, 6, 8], "vertices": [3, 15, -76.18, 29.08, 0.38057, 167, -1162.89, 345.17, 0.04229, 12, 489.37, 206.95, 0.57714, 3, 15, 55.22, 93.97, 0.82543, 167, -1134.94, 201.31, 0.09171, 12, 366.07, 286.17, 0.08286, 3, 15, 155.21, 121.29, 0.82543, 167, -1134.93, 97.65, 0.09171, 12, 291.15, 357.8, 0.08286, 2, 15, 350.21, 136.01, 0.9, 167, -1172.14, -94.32, 0.1, 2, 15, 635.57, 120.56, 0.9, 167, -1262.27, -365.52, 0.1, 2, 15, 739.01, 70.95, 0.9, 167, -1337.4, -452.22, 0.1, 1, 15, 759.05, 15.12, 1, 2, 15, 775.76, -46.04, 0.9, 167, -1459.94, -456.83, 0.1, 2, 15, 656.42, -89.02, 0.9, 167, -1469.94, -330.38, 0.1, 2, 15, 598.83, -104.75, 0.9, 167, -1469.94, -270.68, 0.1, 2, 15, 468.57, -96.54, 0.9, 167, -1427.67, -147.19, 0.1, 2, 15, 451.71, -120.25, 0.9, 167, -1446.1, -124.68, 0.1, 2, 15, 105.16, -147.32, 0.9, 167, -1380.85, 216.75, 0.1, 2, 15, -13.78, -120.93, 0.9, 167, -1324.04, 324.52, 0.1, 1, 15, -59.15, -33.25, 1], "hull": 15, "edges": [0, 28, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28], "width": 149, "height": 352}}}}], "animations": {"idle": {"slots": {"biyan": {"rgba": [{"time": 3.3333, "color": "ffffff00"}, {"time": 3.4333, "color": "ffffffff", "curve": "stepped"}, {"time": 3.8333, "color": "ffffffff"}, {"time": 3.9333, "color": "ffffff00"}], "attachment": [{"time": 3.3333, "name": "biyan"}, {"time": 3.9333}]}}, "bones": {"toufa38": {"rotate": [{"value": -8.23, "curve": [0.462, -6.2, 0.881, 0]}, {"time": 1.0667, "curve": [1.29, 0, 1.846, -8.89]}, {"time": 2.4, "value": -8.89, "curve": [2.491, -8.89, 2.578, -8.62]}, {"time": 2.6667, "value": -8.23, "curve": [3.129, -6.2, 3.547, 0]}, {"time": 3.7333, "curve": [3.957, 0, 4.513, -8.89]}, {"time": 5.0667, "value": -8.89, "curve": [5.158, -8.89, 5.244, -8.62]}, {"time": 5.3333, "value": -8.23}]}, "toufa39": {"rotate": [{"value": -12.06, "curve": [0.501, -10, 0.967, 0]}, {"time": 1.1667, "curve": [1.39, 0, 1.946, -12.41]}, {"time": 2.5, "value": -12.41, "curve": [2.557, -12.41, 2.611, -12.29]}, {"time": 2.6667, "value": -12.06, "curve": [3.167, -10, 3.634, 0]}, {"time": 3.8333, "curve": [4.057, 0, 4.613, -12.41]}, {"time": 5.1667, "value": -12.41, "curve": [5.224, -12.41, 5.278, -12.29]}, {"time": 5.3333, "value": -12.06}]}, "toufa40": {"rotate": [{"value": -14.57, "curve": [0.533, -13.48, 1.053, 0]}, {"time": 1.2667, "curve": [1.49, 0, 2.046, -14.73]}, {"time": 2.6, "value": -14.73, "curve": [2.622, -14.73, 2.644, -14.61]}, {"time": 2.6667, "value": -14.57, "curve": [3.2, -13.48, 3.72, 0]}, {"time": 3.9333, "curve": [4.157, 0, 4.713, -14.73]}, {"time": 5.2667, "value": -14.73, "curve": [5.289, -14.73, 5.311, -14.61]}, {"time": 5.3333, "value": -14.57}]}, "toufa42": {"rotate": [{"value": -0.33, "curve": [0.068, -0.13, 0.124, 0]}, {"time": 0.1667, "curve": [0.39, 0, 0.946, -3.31]}, {"time": 1.5, "value": -3.31, "curve": [1.949, -3.31, 2.278, -1.47]}, {"time": 2.6667, "value": -0.33, "curve": [2.735, -0.13, 2.79, 0]}, {"time": 2.8333, "curve": [3.057, 0, 3.613, -3.31]}, {"time": 4.1667, "value": -3.31, "curve": [4.615, -3.31, 4.944, -1.47]}, {"time": 5.3333, "value": -0.33}]}, "toufa43": {"rotate": [{"value": -0.8, "curve": [0.128, -0.35, 0.23, 0]}, {"time": 0.3, "curve": [0.524, 0, 1.079, -3.39]}, {"time": 1.6333, "value": -3.39, "curve": [2.015, -3.39, 2.322, -2.03]}, {"time": 2.6667, "value": -0.8, "curve": [2.795, -0.35, 2.897, 0]}, {"time": 2.9667, "curve": [3.19, 0, 3.746, -3.39]}, {"time": 4.3, "value": -3.39, "curve": [4.682, -3.39, 4.989, -2.03]}, {"time": 5.3333, "value": -0.8}]}, "zuoshou2": {"rotate": [{}, {"time": 1.3333, "value": -4.53}, {"time": 2.6667}, {"time": 4, "value": -4.53}, {"time": 5.3333}]}, "toufa33": {"rotate": [{"value": -0.39, "curve": [0.142, -0.17, 0.257, 0]}, {"time": 0.3333, "curve": [0.557, 0, 1.113, -1.42]}, {"time": 1.6667, "value": -1.42, "curve": [2.032, -1.42, 2.333, -0.91]}, {"time": 2.6667, "value": -0.39, "curve": [2.808, -0.17, 2.924, 0]}, {"time": 3, "curve": [3.224, 0, 3.779, -1.42]}, {"time": 4.3333, "value": -1.42, "curve": [4.699, -1.42, 5, -0.91]}, {"time": 5.3333, "value": -0.39}]}, "toufa34": {"rotate": [{"value": -0.98, "curve": [0.218, -0.48, 0.396, 0]}, {"time": 0.5, "curve": [0.724, 0, 1.279, -2.16]}, {"time": 1.8333, "value": -2.16, "curve": [2.128, -2.16, 2.389, -1.63]}, {"time": 2.6667, "value": -0.98, "curve": [2.884, -0.48, 3.062, 0]}, {"time": 3.1667, "curve": [3.39, 0, 3.946, -2.16]}, {"time": 4.5, "value": -2.16, "curve": [4.795, -2.16, 5.056, -1.63]}, {"time": 5.3333, "value": -0.98}]}, "toufa35": {"rotate": [{"value": -1.82, "curve": [0.294, -1, 0.537, 0]}, {"time": 0.6667, "curve": [0.89, 0, 1.446, -2.91]}, {"time": 2, "value": -2.91, "curve": [2.232, -2.91, 2.444, -2.45]}, {"time": 2.6667, "value": -1.82, "curve": [2.961, -1, 3.204, 0]}, {"time": 3.3333, "curve": [3.557, 0, 4.113, -2.91]}, {"time": 4.6667, "value": -2.91, "curve": [4.898, -2.91, 5.111, -2.45]}, {"time": 5.3333, "value": -1.82}]}, "toufa36": {"rotate": [{"value": -2.43, "curve": [0.365, -1.51, 0.679, 0]}, {"time": 0.8333, "curve": [1.057, 0, 1.613, -3.14]}, {"time": 2.1667, "value": -3.14, "curve": [2.337, -3.14, 2.5, -2.86]}, {"time": 2.6667, "value": -2.43, "curve": [3.032, -1.51, 3.346, 0]}, {"time": 3.5, "curve": [3.724, 0, 4.279, -3.14]}, {"time": 4.8333, "value": -3.14, "curve": [5.004, -3.14, 5.167, -2.86]}, {"time": 5.3333, "value": -2.43}]}, "toufa37": {"rotate": [{"value": -6.14, "curve": [0.435, -4.36, 0.823, 0]}, {"time": 1, "curve": [1.224, 0, 1.779, -6.86]}, {"time": 2.3333, "value": -6.86, "curve": [2.446, -6.86, 2.556, -6.59]}, {"time": 2.6667, "value": -6.14, "curve": [3.102, -4.36, 3.49, 0]}, {"time": 3.6667, "curve": [3.89, 0, 4.446, -6.86]}, {"time": 5, "value": -6.86, "curve": [5.113, -6.86, 5.222, -6.59]}, {"time": 5.3333, "value": -6.14}]}, "toufa41": {"rotate": [{"curve": [0.224, 0, 0.779, -2.12]}, {"time": 1.3333, "value": -2.12, "curve": [1.89, -2.12, 2.222, 0]}, {"time": 2.6667, "curve": [2.89, 0, 3.446, -2.12]}, {"time": 4, "value": -2.12, "curve": [4.556, -2.12, 4.889, 0]}, {"time": 5.3333}]}, "toufa44": {"rotate": [{"value": -2.84, "curve": [0.188, -1.32, 0.34, 0]}, {"time": 0.4333, "curve": [0.657, 0, 1.213, -7.43]}, {"time": 1.7667, "value": -7.43, "curve": [2.089, -7.43, 2.367, -5.27]}, {"time": 2.6667, "value": -2.84, "curve": [2.855, -1.32, 3.007, 0]}, {"time": 3.1, "curve": [3.324, 0, 3.879, -7.43]}, {"time": 4.4333, "value": -7.43, "curve": [4.756, -7.43, 5.033, -5.27]}, {"time": 5.3333, "value": -2.84}]}, "toufa45": {"rotate": [{"value": -2.93, "curve": [0.233, -1.46, 0.424, 0]}, {"time": 0.5333, "curve": [0.757, 0, 1.313, -5.96]}, {"time": 1.8667, "value": -5.96, "curve": [2.149, -5.96, 2.4, -4.61]}, {"time": 2.6667, "value": -2.93, "curve": [2.9, -1.46, 3.09, 0]}, {"time": 3.2, "curve": [3.424, 0, 3.979, -5.96]}, {"time": 4.5333, "value": -5.96, "curve": [4.815, -5.96, 5.067, -4.61]}, {"time": 5.3333, "value": -2.93}]}, "toufa49": {"rotate": [{"curve": [0.224, 0, 0.779, -3.6]}, {"time": 1.3333, "value": -3.6, "curve": [1.89, -3.6, 2.222, 0]}, {"time": 2.6667, "curve": [2.89, 0, 3.446, -3.6]}, {"time": 4, "value": -3.6, "curve": [4.556, -3.6, 4.889, 0]}, {"time": 5.3333}]}, "toufa50": {"rotate": [{"value": -0.5, "curve": [0.068, -0.2, 0.124, 0]}, {"time": 0.1667, "curve": [0.39, 0, 0.946, -4.97]}, {"time": 1.5, "value": -4.97, "curve": [1.949, -4.97, 2.278, -2.21]}, {"time": 2.6667, "value": -0.5, "curve": [2.735, -0.2, 2.79, 0]}, {"time": 2.8333, "curve": [3.057, 0, 3.613, -4.97]}, {"time": 4.1667, "value": -4.97, "curve": [4.615, -4.97, 4.944, -2.21]}, {"time": 5.3333, "value": -0.5}]}, "toufa51": {"rotate": [{"value": -1.97, "curve": [0.142, -0.86, 0.257, 0]}, {"time": 0.3333, "curve": [0.557, 0, 1.113, -7.21]}, {"time": 1.6667, "value": -7.21, "curve": [2.032, -7.21, 2.333, -4.6]}, {"time": 2.6667, "value": -1.97, "curve": [2.808, -0.86, 2.924, 0]}, {"time": 3, "curve": [3.224, 0, 3.779, -7.21]}, {"time": 4.3333, "value": -7.21, "curve": [4.699, -7.21, 5, -4.6]}, {"time": 5.3333, "value": -1.97}]}, "toufa52": {"rotate": [{"value": -5.83, "curve": [0.218, -2.83, 0.396, 0]}, {"time": 0.5, "curve": [0.724, 0, 1.279, -12.8]}, {"time": 1.8333, "value": -12.8, "curve": [2.128, -12.8, 2.389, -9.65]}, {"time": 2.6667, "value": -5.83, "curve": [2.884, -2.83, 3.062, 0]}, {"time": 3.1667, "curve": [3.39, 0, 3.946, -12.8]}, {"time": 4.5, "value": -12.8, "curve": [4.795, -12.8, 5.056, -9.65]}, {"time": 5.3333, "value": -5.83}]}, "toufa53": {"rotate": [{"value": -13.77, "curve": [0.294, -7.54, 0.537, 0]}, {"time": 0.6667, "curve": [0.89, 0, 1.446, -21.97]}, {"time": 2, "value": -21.97, "curve": [2.232, -21.97, 2.444, -18.47]}, {"time": 2.6667, "value": -13.77, "curve": [2.961, -7.54, 3.204, 0]}, {"time": 3.3333, "curve": [3.557, 0, 4.113, -21.97]}, {"time": 4.6667, "value": -21.97, "curve": [4.898, -21.97, 5.111, -18.47]}, {"time": 5.3333, "value": -13.77}]}, "toufa57": {"rotate": [{"curve": [0.224, 0, 0.779, -3.4]}, {"time": 1.3333, "value": -3.4, "curve": [1.89, -3.4, 2.222, 0]}, {"time": 2.6667, "curve": [2.89, 0, 3.446, -3.4]}, {"time": 4, "value": -3.4, "curve": [4.556, -3.4, 4.889, 0]}, {"time": 5.3333}]}, "toufa58": {"rotate": [{"curve": [0.224, 0, 0.779, -6]}, {"time": 1.3333, "value": -6, "curve": [1.89, -6, 2.222, 0]}, {"time": 2.6667, "curve": [2.89, 0, 3.446, -6]}, {"time": 4, "value": -6, "curve": [4.556, -6, 4.889, 0]}, {"time": 5.3333}]}, "toufa59": {"rotate": [{"curve": [0.224, 0, 0.779, -8.04]}, {"time": 1.3333, "value": -8.04, "curve": [1.89, -8.04, 2.222, 0]}, {"time": 2.6667, "curve": [2.89, 0, 3.446, -8.04]}, {"time": 4, "value": -8.04, "curve": [4.556, -8.04, 4.889, 0]}, {"time": 5.3333}]}, "toufa60": {"rotate": [{"curve": [0.224, 0, 0.779, -16.56]}, {"time": 1.3333, "value": -16.56, "curve": [1.89, -16.56, 2.222, 0]}, {"time": 2.6667, "curve": [2.89, 0, 3.446, -16.56]}, {"time": 4, "value": -16.56, "curve": [4.556, -16.56, 4.889, 0]}, {"time": 5.3333}]}, "toufa61": {"rotate": [{"curve": [0.224, 0, 0.779, -21.93]}, {"time": 1.3333, "value": -21.93, "curve": [1.89, -21.93, 2.222, 0]}, {"time": 2.6667, "curve": [2.89, 0, 3.446, -21.93]}, {"time": 4, "value": -21.93, "curve": [4.556, -21.93, 4.889, 0]}, {"time": 5.3333}]}, "toufa63": {"rotate": [{"curve": [0.224, 0, 0.779, -3.33]}, {"time": 1.3333, "value": -3.33, "curve": [1.89, -3.33, 2.222, 0]}, {"time": 2.6667, "curve": [2.89, 0, 3.446, -3.33]}, {"time": 4, "value": -3.33, "curve": [4.556, -3.33, 4.889, 0]}, {"time": 5.3333}]}, "toufa64": {"rotate": [{"curve": [0.224, 0, 0.779, -5.74]}, {"time": 1.3333, "value": -5.74, "curve": [1.89, -5.74, 2.222, 0]}, {"time": 2.6667, "curve": [2.89, 0, 3.446, -5.74]}, {"time": 4, "value": -5.74, "curve": [4.556, -5.74, 4.889, 0]}, {"time": 5.3333}]}, "toufa65": {"rotate": [{"curve": [0.224, 0, 0.779, -7.03]}, {"time": 1.3333, "value": -7.03, "curve": [1.89, -7.03, 2.222, 0]}, {"time": 2.6667, "curve": [2.89, 0, 3.446, -7.03]}, {"time": 4, "value": -7.03, "curve": [4.556, -7.03, 4.889, 0]}, {"time": 5.3333}]}, "toufa66": {"rotate": [{"curve": [0.224, 0, 0.779, -14.91]}, {"time": 1.3333, "value": -14.91, "curve": [1.89, -14.91, 2.222, 0]}, {"time": 2.6667, "curve": [2.89, 0, 3.446, -14.91]}, {"time": 4, "value": -14.91, "curve": [4.556, -14.91, 4.889, 0]}, {"time": 5.3333}]}, "toufa67": {"rotate": [{"curve": [0.224, 0, 0.779, -23.58]}, {"time": 1.3333, "value": -23.58, "curve": [1.89, -23.58, 2.222, 0]}, {"time": 2.6667, "curve": [2.89, 0, 3.446, -23.58]}, {"time": 4, "value": -23.58, "curve": [4.556, -23.58, 4.889, 0]}, {"time": 5.3333}]}, "toufa68": {"rotate": [{"curve": [0.224, 0, 0.779, -1.98]}, {"time": 1.3333, "value": -1.98, "curve": [1.89, -1.98, 2.222, 0]}, {"time": 2.6667, "curve": [2.89, 0, 3.446, -1.98]}, {"time": 4, "value": -1.98, "curve": [4.556, -1.98, 4.889, 0]}, {"time": 5.3333}]}, "toufa69": {"rotate": [{"value": -0.14, "curve": [0.038, -0.06, 0.072, 0]}, {"time": 0.1, "curve": [0.324, 0, 0.879, -3.07]}, {"time": 1.4333, "value": -3.07, "curve": [1.919, -3.07, 2.256, -1.07]}, {"time": 2.6667, "value": -0.14, "curve": [2.705, -0.06, 2.739, 0]}, {"time": 2.7667, "curve": [2.99, 0, 3.546, -3.07]}, {"time": 4.1, "value": -3.07, "curve": [4.586, -3.07, 4.922, -1.07]}, {"time": 5.3333, "value": -0.14}]}, "toufa70": {"rotate": [{"value": -0.53, "curve": [0.082, -0.21, 0.15, 0]}, {"time": 0.2, "curve": [0.424, 0, 0.979, -3.93]}, {"time": 1.5333, "value": -3.93, "curve": [1.964, -3.93, 2.289, -1.96]}, {"time": 2.6667, "value": -0.53, "curve": [2.749, -0.21, 2.816, 0]}, {"time": 2.8667, "curve": [3.09, 0, 3.646, -3.93]}, {"time": 4.2, "value": -3.93, "curve": [4.63, -3.93, 4.956, -1.96]}, {"time": 5.3333, "value": -0.53}]}, "toufa71": {"rotate": [{"value": -1.15, "curve": [0.128, -0.5, 0.23, 0]}, {"time": 0.3, "curve": [0.524, 0, 1.079, -4.86]}, {"time": 1.6333, "value": -4.86, "curve": [2.015, -4.86, 2.322, -2.91]}, {"time": 2.6667, "value": -1.15, "curve": [2.795, -0.5, 2.897, 0]}, {"time": 2.9667, "curve": [3.19, 0, 3.746, -4.86]}, {"time": 4.3, "value": -4.86, "curve": [4.682, -4.86, 4.989, -2.91]}, {"time": 5.3333, "value": -1.15}]}, "toufa72": {"rotate": [{"value": -1.61, "curve": [0.174, -0.74, 0.313, 0]}, {"time": 0.4, "curve": [0.624, 0, 1.179, -4.63]}, {"time": 1.7333, "value": -4.63, "curve": [2.071, -4.63, 2.356, -3.16]}, {"time": 2.6667, "value": -1.61, "curve": [2.84, -0.74, 2.979, 0]}, {"time": 3.0667, "curve": [3.29, 0, 3.846, -4.63]}, {"time": 4.4, "value": -4.63, "curve": [4.737, -4.63, 5.022, -3.16]}, {"time": 5.3333, "value": -1.61}]}, "toufa73": {"rotate": [{"value": -2.87, "curve": [0.218, -1.39, 0.396, 0]}, {"time": 0.5, "curve": [0.724, 0, 1.279, -6.3]}, {"time": 1.8333, "value": -6.3, "curve": [2.128, -6.3, 2.389, -4.75]}, {"time": 2.6667, "value": -2.87, "curve": [2.884, -1.39, 3.062, 0]}, {"time": 3.1667, "curve": [3.39, 0, 3.946, -6.3]}, {"time": 4.5, "value": -6.3, "curve": [4.795, -6.3, 5.056, -4.75]}, {"time": 5.3333, "value": -2.87}]}, "toufa74": {"rotate": [{"value": -7.16, "curve": [0.277, -3.82, 0.508, 0]}, {"time": 0.6333, "curve": [0.857, 0, 1.413, -12.08]}, {"time": 1.9667, "value": -12.08, "curve": [2.21, -12.08, 2.433, -9.98]}, {"time": 2.6667, "value": -7.16, "curve": [2.944, -3.82, 3.175, 0]}, {"time": 3.3, "curve": [3.524, 0, 4.079, -12.08]}, {"time": 4.6333, "value": -12.08, "curve": [4.876, -12.08, 5.1, -9.98]}, {"time": 5.3333, "value": -7.16}]}, "toufa78": {"rotate": [{"value": -2.38, "curve": [0.157, -1.05, 0.284, 0]}, {"time": 0.3667, "curve": [0.59, 0, 1.146, -7.67]}, {"time": 1.7, "value": -7.67, "curve": [2.05, -7.67, 2.344, -5.1]}, {"time": 2.6667, "value": -2.38, "curve": [2.823, -1.05, 2.951, 0]}, {"time": 3.0333, "curve": [3.257, 0, 3.813, -7.67]}, {"time": 4.3667, "value": -7.67, "curve": [4.717, -7.67, 5.011, -5.1]}, {"time": 5.3333, "value": -2.38}]}, "toufa79": {"rotate": [{"value": -4, "curve": [0.174, -1.83, 0.313, 0]}, {"time": 0.4, "curve": [0.624, 0, 1.179, -11.55]}, {"time": 1.7333, "value": -11.55, "curve": [2.071, -11.55, 2.356, -7.88]}, {"time": 2.6667, "value": -4, "curve": [2.84, -1.83, 2.979, 0]}, {"time": 3.0667, "curve": [3.29, 0, 3.846, -11.55]}, {"time": 4.4, "value": -11.55, "curve": [4.737, -11.55, 5.022, -7.88]}, {"time": 5.3333, "value": -4}]}, "toufa117": {"rotate": [{"value": -1.16, "curve": [0.112, -0.48, 0.203, 0]}, {"time": 0.2667, "curve": [0.49, 0, 1.046, -5.75]}, {"time": 1.6, "value": -5.75, "curve": [1.997, -5.75, 2.311, -3.3]}, {"time": 2.6667, "value": -1.16, "curve": [2.779, -0.48, 2.87, 0]}, {"time": 2.9333, "curve": [3.157, 0, 3.713, -5.75]}, {"time": 4.2667, "value": -5.75, "curve": [4.663, -5.75, 4.978, -3.3]}, {"time": 5.3333, "value": -1.16}]}, "toufa118": {"rotate": [{"value": -3, "curve": [0.188, -1.39, 0.34, 0]}, {"time": 0.4333, "curve": [0.657, 0, 1.213, -7.83]}, {"time": 1.7667, "value": -7.83, "curve": [2.089, -7.83, 2.367, -5.55]}, {"time": 2.6667, "value": -3, "curve": [2.855, -1.39, 3.007, 0]}, {"time": 3.1, "curve": [3.324, 0, 3.879, -7.83]}, {"time": 4.4333, "value": -7.83, "curve": [4.756, -7.83, 5.033, -5.55]}, {"time": 5.3333, "value": -3}]}, "zuoshou1": {"rotate": [{}, {"time": 1.3333, "value": 2.43}, {"time": 2.6667}, {"time": 4, "value": 2.43}, {"time": 5.3333}]}, "zuoshou": {"rotate": [{}, {"time": 1.3333, "value": -2.11}, {"time": 2.6667}, {"time": 4, "value": -2.11}, {"time": 5.3333}]}, "zuoshou3": {"rotate": [{}, {"time": 1.3333, "value": -2.11}, {"time": 2.6667}, {"time": 4, "value": -2.11}, {"time": 5.3333}]}, "zuoshou4": {"rotate": [{}, {"time": 1.3333, "value": -13.35}, {"time": 2.6667}, {"time": 4, "value": -13.35}, {"time": 5.3333}]}, "zuoshou5": {"rotate": [{}, {"time": 1.3333, "value": -2.11}, {"time": 2.6667}, {"time": 4, "value": -2.11}, {"time": 5.3333}]}, "zuoshou6": {"rotate": [{}, {"time": 1.3333, "value": -13.35}, {"time": 2.6667}, {"time": 4, "value": -13.35}, {"time": 5.3333}]}, "shoubiao": {"translate": [{"curve": [0.447, 0, 0.895, 1.37, 0.447, 0, 0.895, -17.68]}, {"time": 1.3333, "x": 1.37, "y": -17.68, "curve": [1.784, 1.37, 2.222, 0, 1.784, -17.68, 2.222, 0]}, {"time": 2.6667, "curve": [3.114, 0, 3.561, 1.37, 3.114, 0, 3.561, -17.68]}, {"time": 4, "x": 1.37, "y": -17.68, "curve": [4.45, 1.37, 4.889, 0, 4.45, -17.68, 4.889, 0]}, {"time": 5.3333}]}, "toufa5": {"rotate": [{"value": -2.28, "curve": [0.253, -1.88, 0.439, -1.39]}, {"time": 0.5, "value": -1.2, "curve": [0.602, -0.89, 1.223, -2.73]}, {"time": 1.8333, "value": -2.91, "curve": [2.119, -2.99, 2.403, -2.7]}, {"time": 2.6333, "value": -2.34, "curve": [2.645, -2.32, 2.656, -2.3]}, {"time": 2.6667, "value": -2.28, "curve": [2.919, -1.88, 3.106, -1.39]}, {"time": 3.1667, "value": -1.2, "curve": [3.269, -0.89, 3.89, -2.73]}, {"time": 4.5, "value": -2.91, "curve": [4.786, -2.99, 5.069, -2.7]}, {"time": 5.3, "value": -2.34, "curve": [5.312, -2.32, 5.322, -2.3]}, {"time": 5.3333, "value": -2.28}]}, "toufa6": {"rotate": [{"value": -3.75, "curve": [0.294, -2.91, 0.513, -1.89]}, {"time": 0.5667, "value": -1.72, "curve": [0.693, -1.33, 1.298, -4.69]}, {"time": 1.9, "value": -4.86, "curve": [2.171, -4.93, 2.411, -4.48]}, {"time": 2.6667, "value": -3.75, "curve": [2.96, -2.91, 3.18, -1.89]}, {"time": 3.2333, "value": -1.72, "curve": [3.36, -1.33, 3.965, -4.69]}, {"time": 4.5667, "value": -4.86, "curve": [4.838, -4.93, 5.078, -4.48]}, {"time": 5.3333, "value": -3.75}]}, "toufa7": {"rotate": [{"value": -6.86, "curve": [0.357, -4.97, 0.624, -2.32]}, {"time": 0.6667, "value": -2.21, "curve": [0.818, -1.81, 1.416, -8.59]}, {"time": 2, "value": -8.73, "curve": [2.235, -8.78, 2.444, -8.03]}, {"time": 2.6667, "value": -6.86, "curve": [3.024, -4.97, 3.291, -2.32]}, {"time": 3.3333, "value": -2.21, "curve": [3.485, -1.81, 4.083, -8.59]}, {"time": 4.6667, "value": -8.73, "curve": [4.901, -8.78, 5.111, -8.03]}, {"time": 5.3333, "value": -6.86}]}, "toufa8": {"rotate": [{"value": -13.6, "curve": [0.454, -9.85, 0.808, -2.67]}, {"time": 0.8333, "value": -2.63, "curve": [1.013, -2.34, 1.598, -15.75]}, {"time": 2.1667, "value": -15.83, "curve": [2.34, -15.85, 2.5, -14.98]}, {"time": 2.6667, "value": -13.6, "curve": [3.121, -9.85, 3.475, -2.67]}, {"time": 3.5, "value": -2.63, "curve": [3.68, -2.34, 4.265, -15.75]}, {"time": 4.8333, "value": -15.83, "curve": [5.007, -15.85, 5.167, -14.98]}, {"time": 5.3333, "value": -13.6}]}, "toufa31": {"rotate": [{"curve": [0.224, 0, 0.779, -1.21]}, {"time": 1.3333, "value": -1.21, "curve": [1.89, -1.21, 2.222, 0]}, {"time": 2.6667, "curve": [2.89, 0, 3.446, -1.21]}, {"time": 4, "value": -1.21, "curve": [4.556, -1.21, 4.889, 0]}, {"time": 5.3333}]}, "toufa32": {"rotate": [{"value": -0.15, "curve": [0.068, -0.06, 0.124, 0]}, {"time": 0.1667, "curve": [0.39, 0, 0.946, -1.48]}, {"time": 1.5, "value": -1.48, "curve": [1.949, -1.48, 2.278, -0.66]}, {"time": 2.6667, "value": -0.15, "curve": [2.735, -0.06, 2.79, 0]}, {"time": 2.8333, "curve": [3.057, 0, 3.613, -1.48]}, {"time": 4.1667, "value": -1.48, "curve": [4.615, -1.48, 4.944, -0.66]}, {"time": 5.3333, "value": -0.15}]}, "toufa46": {"rotate": [{"value": -5.4, "curve": [0.277, -2.88, 0.508, 0]}, {"time": 0.6333, "curve": [0.857, 0, 1.413, -9.1]}, {"time": 1.9667, "value": -9.1, "curve": [2.21, -9.1, 2.433, -7.52]}, {"time": 2.6667, "value": -5.4, "curve": [2.944, -2.88, 3.175, 0]}, {"time": 3.3, "curve": [3.524, 0, 4.079, -9.1]}, {"time": 4.6333, "value": -9.1, "curve": [4.876, -9.1, 5.1, -7.52]}, {"time": 5.3333, "value": -5.4}]}, "toufa47": {"rotate": [{"value": -10.6, "curve": [0.321, -6.07, 0.593, 0]}, {"time": 0.7333, "curve": [0.957, 0, 1.513, -15.39]}, {"time": 2.0667, "value": -15.39, "curve": [2.272, -15.39, 2.467, -13.41]}, {"time": 2.6667, "value": -10.6, "curve": [2.988, -6.07, 3.26, 0]}, {"time": 3.4, "curve": [3.624, 0, 4.179, -15.39]}, {"time": 4.7333, "value": -15.39, "curve": [4.939, -15.39, 5.133, -13.41]}, {"time": 5.3333, "value": -10.6}]}, "toufa48": {"rotate": [{"value": -18.73, "curve": [0.38, -11.91, 0.708, 0]}, {"time": 0.8667, "curve": [1.09, 0, 1.646, -23.37]}, {"time": 2.2, "value": -23.37, "curve": [2.359, -23.37, 2.511, -21.51]}, {"time": 2.6667, "value": -18.73, "curve": [3.047, -11.91, 3.375, 0]}, {"time": 3.5333, "curve": [3.757, 0, 4.313, -23.37]}, {"time": 4.8667, "value": -23.37, "curve": [5.026, -23.37, 5.178, -21.51]}, {"time": 5.3333, "value": -18.73}]}, "touying2": {"rotate": [{"curve": [0.224, 0, 0.779, -1.85]}, {"time": 1.3333, "value": -1.85, "curve": [1.89, -1.85, 2.222, 0]}, {"time": 2.6667, "curve": [2.89, 0, 3.446, -1.85]}, {"time": 4, "value": -1.85, "curve": [4.556, -1.85, 4.889, 0]}, {"time": 5.3333}]}, "touying3": {"rotate": [{"curve": [0.224, 0, 0.779, -1.85]}, {"time": 1.3333, "value": -1.85, "curve": [1.89, -1.85, 2.222, 0]}, {"time": 2.6667, "curve": [2.89, 0, 3.446, -1.85]}, {"time": 4, "value": -1.85, "curve": [4.556, -1.85, 4.889, 0]}, {"time": 5.3333}]}, "toufa54": {"rotate": [{"curve": [0.224, 0, 0.779, -1.99]}, {"time": 1.3333, "value": -1.99, "curve": [1.89, -1.99, 2.222, 0]}, {"time": 2.6667, "curve": [2.89, 0, 3.446, -1.99]}, {"time": 4, "value": -1.99, "curve": [4.556, -1.99, 4.889, 0]}, {"time": 5.3333}]}, "toufa55": {"rotate": [{"curve": [0.224, 0, 0.779, -3.28]}, {"time": 1.3333, "value": -3.28, "curve": [1.89, -3.28, 2.222, 0]}, {"time": 2.6667, "curve": [2.89, 0, 3.446, -3.28]}, {"time": 4, "value": -3.28, "curve": [4.556, -3.28, 4.889, 0]}, {"time": 5.3333}]}, "toufa56": {"rotate": [{"curve": [0.224, 0, 0.779, -3.32]}, {"time": 1.3333, "value": -3.32, "curve": [1.89, -3.32, 2.222, 0]}, {"time": 2.6667, "curve": [2.89, 0, 3.446, -3.32]}, {"time": 4, "value": -3.32, "curve": [4.556, -3.32, 4.889, 0]}, {"time": 5.3333}]}, "toufa62": {"rotate": [{"curve": [0.224, 0, 0.779, -2.48]}, {"time": 1.3333, "value": -2.48, "curve": [1.89, -2.48, 2.222, 0]}, {"time": 2.6667, "curve": [2.89, 0, 3.446, -2.48]}, {"time": 4, "value": -2.48, "curve": [4.556, -2.48, 4.889, 0]}, {"time": 5.3333}]}, "toufa75": {"rotate": [{"value": -10.45, "curve": [0.321, -5.99, 0.593, 0]}, {"time": 0.7333, "curve": [0.957, 0, 1.513, -15.19]}, {"time": 2.0667, "value": -15.19, "curve": [2.272, -15.19, 2.467, -13.23]}, {"time": 2.6667, "value": -10.45, "curve": [2.988, -5.99, 3.26, 0]}, {"time": 3.4, "curve": [3.624, 0, 4.179, -15.19]}, {"time": 4.7333, "value": -15.19, "curve": [4.939, -15.19, 5.133, -13.23]}, {"time": 5.3333, "value": -10.45}]}, "toufa76": {"rotate": [{"curve": [0.224, 0, 0.779, -3.3]}, {"time": 1.3333, "value": -3.3, "curve": [1.89, -3.3, 2.222, 0]}, {"time": 2.6667, "curve": [2.89, 0, 3.446, -3.3]}, {"time": 4, "value": -3.3, "curve": [4.556, -3.3, 4.889, 0]}, {"time": 5.3333}]}, "toufa77": {"rotate": [{"value": -0.39, "curve": [0.054, -0.16, 0.098, 0]}, {"time": 0.1333, "curve": [0.357, 0, 0.913, -5.26]}, {"time": 1.4667, "value": -5.26, "curve": [1.934, -5.26, 2.267, -2.08]}, {"time": 2.6667, "value": -0.39, "curve": [2.72, -0.16, 2.764, 0]}, {"time": 2.8, "curve": [3.024, 0, 3.579, -5.26]}, {"time": 4.1333, "value": -5.26, "curve": [4.601, -5.26, 4.933, -2.08]}, {"time": 5.3333, "value": -0.39}]}, "toufa80": {"rotate": [{"value": -6.44, "curve": [0.233, -3.21, 0.424, 0]}, {"time": 0.5333, "curve": [0.757, 0, 1.313, -13.12]}, {"time": 1.8667, "value": -13.12, "curve": [2.149, -13.12, 2.4, -10.14]}, {"time": 2.6667, "value": -6.44, "curve": [2.9, -3.21, 3.09, 0]}, {"time": 3.2, "curve": [3.424, 0, 3.979, -13.12]}, {"time": 4.5333, "value": -13.12, "curve": [4.815, -13.12, 5.067, -10.14]}, {"time": 5.3333, "value": -6.44}]}, "toufa81": {"rotate": [{"value": -8.26, "curve": [0.277, -4.4, 0.508, 0]}, {"time": 0.6333, "curve": [0.857, 0, 1.413, -13.93]}, {"time": 1.9667, "value": -13.93, "curve": [2.21, -13.93, 2.433, -11.51]}, {"time": 2.6667, "value": -8.26, "curve": [2.944, -4.4, 3.175, 0]}, {"time": 3.3, "curve": [3.524, 0, 4.079, -13.93]}, {"time": 4.6333, "value": -13.93, "curve": [4.876, -13.93, 5.1, -11.51]}, {"time": 5.3333, "value": -8.26}]}, "toufa110": {"rotate": [{"curve": [0.224, 0, 0.779, -2.88]}, {"time": 1.3333, "value": -2.88, "curve": [1.89, -2.88, 2.222, 0]}, {"time": 2.6667, "curve": [2.89, 0, 3.446, -2.88]}, {"time": 4, "value": -2.88, "curve": [4.556, -2.88, 4.889, 0]}, {"time": 5.3333}]}, "toufa111": {"rotate": [{"value": -0.27, "curve": [0.054, -0.11, 0.098, 0]}, {"time": 0.1333, "curve": [0.357, 0, 0.913, -3.65]}, {"time": 1.4667, "value": -3.65, "curve": [1.934, -3.65, 2.267, -1.44]}, {"time": 2.6667, "value": -0.27, "curve": [2.72, -0.11, 2.764, 0]}, {"time": 2.8, "curve": [3.024, 0, 3.579, -3.65]}, {"time": 4.1333, "value": -3.65, "curve": [4.601, -3.65, 4.933, -1.44]}, {"time": 5.3333, "value": -0.27}]}, "toufa112": {"rotate": [{"value": -1.9, "curve": [0.128, -0.82, 0.23, 0]}, {"time": 0.3, "curve": [0.524, 0, 1.079, -8.03]}, {"time": 1.6333, "value": -8.03, "curve": [2.015, -8.03, 2.322, -4.81]}, {"time": 2.6667, "value": -1.9, "curve": [2.795, -0.82, 2.897, 0]}, {"time": 2.9667, "curve": [3.19, 0, 3.746, -8.03]}, {"time": 4.3, "value": -8.03, "curve": [4.682, -8.03, 4.989, -4.81]}, {"time": 5.3333, "value": -1.9}]}, "toufa113": {"rotate": [{"value": -4.81, "curve": [0.174, -2.2, 0.313, 0]}, {"time": 0.4, "curve": [0.624, 0, 1.179, -13.87]}, {"time": 1.7333, "value": -13.87, "curve": [2.071, -13.87, 2.356, -9.47]}, {"time": 2.6667, "value": -4.81, "curve": [2.84, -2.2, 2.979, 0]}, {"time": 3.0667, "curve": [3.29, 0, 3.846, -13.87]}, {"time": 4.4, "value": -13.87, "curve": [4.737, -13.87, 5.022, -9.47]}, {"time": 5.3333, "value": -4.81}]}, "toufa114": {"rotate": [{"value": -10.29, "curve": [0.233, -5.13, 0.424, 0]}, {"time": 0.5333, "curve": [0.757, 0, 1.313, -20.95]}, {"time": 1.8667, "value": -20.95, "curve": [2.149, -20.95, 2.4, -16.21]}, {"time": 2.6667, "value": -10.29, "curve": [2.9, -5.13, 3.09, 0]}, {"time": 3.2, "curve": [3.424, 0, 3.979, -20.95]}, {"time": 4.5333, "value": -20.95, "curve": [4.815, -20.95, 5.067, -16.21]}, {"time": 5.3333, "value": -10.29}]}, "toufa115": {"rotate": [{"curve": [0.224, 0, 0.779, -1.75]}, {"time": 1.3333, "value": -1.75, "curve": [1.89, -1.75, 2.222, 0]}, {"time": 2.6667, "curve": [2.89, 0, 3.446, -1.75]}, {"time": 4, "value": -1.75, "curve": [4.556, -1.75, 4.889, 0]}, {"time": 5.3333}]}, "toufa116": {"rotate": [{"value": -0.3, "curve": [0.054, -0.13, 0.098, 0]}, {"time": 0.1333, "curve": [0.357, 0, 0.913, -4.09]}, {"time": 1.4667, "value": -4.09, "curve": [1.934, -4.09, 2.267, -1.61]}, {"time": 2.6667, "value": -0.3, "curve": [2.72, -0.13, 2.764, 0]}, {"time": 2.8, "curve": [3.024, 0, 3.579, -4.09]}, {"time": 4.1333, "value": -4.09, "curve": [4.601, -4.09, 4.933, -1.61]}, {"time": 5.3333, "value": -0.3}]}, "toufa119": {"rotate": [{"value": -7.35, "curve": [0.277, -3.92, 0.508, 0]}, {"time": 0.6333, "curve": [0.857, 0, 1.413, -12.4]}, {"time": 1.9667, "value": -12.4, "curve": [2.21, -12.4, 2.433, -10.24]}, {"time": 2.6667, "value": -7.35, "curve": [2.944, -3.92, 3.175, 0]}, {"time": 3.3, "curve": [3.524, 0, 4.079, -12.4]}, {"time": 4.6333, "value": -12.4, "curve": [4.876, -12.4, 5.1, -10.24]}, {"time": 5.3333, "value": -7.35}]}, "toufa120": {"rotate": [{"value": -8.33, "curve": [0.256, -4.51, 0.51, 0]}, {"time": 0.7667, "curve": [1.214, 0, 1.661, -13.57]}, {"time": 2.1, "value": -13.57, "curve": [2.291, -13.57, 2.478, -11.15]}, {"time": 2.6667, "value": -8.33, "curve": [2.923, -4.51, 3.177, 0]}, {"time": 3.4333, "curve": [3.881, 0, 4.328, -13.57]}, {"time": 4.7667, "value": -13.57, "curve": [4.957, -13.57, 5.144, -11.15]}, {"time": 5.3333, "value": -8.33}]}, "shenti": {"rotate": [{"curve": [0.444, 0, 0.895, -1.4]}, {"time": 1.3333, "value": -1.4, "curve": [1.784, -1.4, 2.222, 0]}, {"time": 2.6667, "curve": [3.111, 0, 3.561, -1.4]}, {"time": 4, "value": -1.4, "curve": [4.45, -1.4, 4.889, 0]}, {"time": 5.3333}], "translate": [{"curve": [0.444, 0, 0.889, 0, 0.444, 0, 0.889, 21.45]}, {"time": 1.3333, "y": 21.45, "curve": [1.778, 0, 2.222, 0, 1.778, 21.45, 2.222, 0]}, {"time": 2.6667, "curve": [3.111, 0, 3.556, 0, 3.111, 0, 3.556, 21.45]}, {"time": 4, "y": 21.45, "curve": [4.444, 0, 4.889, 0, 4.444, 21.45, 4.889, 0]}, {"time": 5.3333}]}, "shenti2": {"rotate": [{"curve": [0.444, 1.06, 0.895, 4.91]}, {"time": 1.3333, "value": 4.91, "curve": [1.784, 4.91, 2.222, -1.06]}, {"time": 2.6667, "curve": [3.111, 1.06, 3.561, 4.91]}, {"time": 4, "value": 4.91, "curve": [4.45, 4.91, 4.889, 0]}, {"time": 5.3333}], "translate": [{"curve": [0.222, 0, 0.444, -16.38, 0.222, 0, 0.444, -8]}, {"time": 0.6667, "x": -16.38, "y": -8, "curve": [1.111, -16.38, 1.556, 6.29, 1.111, -8, 1.556, 3.57]}, {"time": 2, "x": 6.29, "y": 3.57, "curve": [2.222, 6.29, 2.444, 0, 2.222, 3.57, 2.444, 0]}, {"time": 2.6667, "curve": [2.889, 0, 3.111, -16.38, 2.889, 0, 3.111, -8]}, {"time": 3.3333, "x": -16.38, "y": -8, "curve": [3.778, -16.38, 4.222, 6.29, 3.778, -8, 4.222, 3.57]}, {"time": 4.6667, "x": 6.29, "y": 3.57, "curve": [4.889, 6.29, 5.111, 3.78, 4.889, 3.57, 5.111, 1.93]}, {"time": 5.3333}]}, "shenti3": {"rotate": [{"curve": [0.444, 0.66, 0.895, -0.06]}, {"time": 1.3333, "value": -0.06, "curve": [1.784, -0.06, 2.627, -0.06]}, {"time": 2.6667, "curve": [3.111, 0.66, 3.561, -0.06]}, {"time": 4, "value": -0.06, "curve": [4.45, -0.06, 4.889, 0]}, {"time": 5.3333}], "translate": [{"curve": [0.444, 0, 0.889, -13.37, 0.444, 0, 0.889, -2.54]}, {"time": 1.3333, "x": -13.37, "y": -2.54, "curve": [1.778, -13.37, 2.222, 0, 1.778, -2.54, 2.222, 0]}, {"time": 2.6667, "curve": [3.111, 0, 3.556, -13.37, 3.111, 0, 3.556, -2.54]}, {"time": 4, "x": -13.37, "y": -2.54, "curve": [4.444, -13.37, 4.889, 0, 4.444, -2.54, 4.889, 0]}, {"time": 5.3333}]}, "shenti4": {"rotate": [{"curve": [0.444, 0, 0.889, -1.12]}, {"time": 1.3333, "value": -1.12, "curve": [1.778, -1.12, 2.222, 0]}, {"time": 2.6667, "curve": [3.111, 0, 3.556, -1.12]}, {"time": 4, "value": -1.12, "curve": [4.444, -1.12, 4.889, 0]}, {"time": 5.3333}]}, "lian": {"rotate": [{"curve": [0.444, -1.48, 0.895, -3.33]}, {"time": 1.3333, "value": -3.33, "curve": [1.784, -3.33, 2.222, 1.48]}, {"time": 2.6667, "curve": [3.111, -1.48, 3.561, -3.33]}, {"time": 4, "value": -3.33, "curve": [4.45, -3.33, 4.889, 0]}, {"time": 5.3333}]}, "toufa2": {"rotate": [{"value": -0.2, "curve": [0.057, -0.14, 0.101, -0.09]}, {"time": 0.1333, "value": -0.06, "curve": [0.152, -0.04, 0.809, -1.01]}, {"time": 1.4667, "value": -1.05, "curve": [1.941, -1.08, 2.267, -0.64]}, {"time": 2.6667, "value": -0.2, "curve": [2.723, -0.14, 2.767, -0.09]}, {"time": 2.8, "value": -0.06, "curve": [2.819, -0.04, 3.476, -1.01]}, {"time": 4.1333, "value": -1.05, "curve": [4.607, -1.08, 4.933, -0.64]}, {"time": 5.3333, "value": -0.2}]}, "toufa3": {"rotate": [{"value": -0.97, "curve": [0.139, -0.68, 0.243, -0.41]}, {"time": 0.3, "value": -0.29, "curve": [0.347, -0.2, 0.994, -2.07]}, {"time": 1.6333, "value": -2.18, "curve": [2.021, -2.24, 2.322, -1.71]}, {"time": 2.6667, "value": -0.97, "curve": [2.806, -0.68, 2.91, -0.41]}, {"time": 2.9667, "value": -0.29, "curve": [3.014, -0.2, 3.661, -2.07]}, {"time": 4.3, "value": -2.18, "curve": [4.688, -2.24, 4.989, -1.71]}, {"time": 5.3333, "value": -0.97}]}, "toufa4": {"rotate": [{"value": -1.3, "curve": [0.176, -1.04, 0.307, -0.77]}, {"time": 0.3667, "value": -0.61, "curve": [0.436, -0.43, 1.071, -1.82]}, {"time": 1.7, "value": -1.97, "curve": [2.012, -2.04, 2.322, -1.77]}, {"time": 2.5667, "value": -1.45, "curve": [2.602, -1.4, 2.633, -1.35]}, {"time": 2.6667, "value": -1.3, "curve": [2.843, -1.04, 2.974, -0.77]}, {"time": 3.0333, "value": -0.61, "curve": [3.103, -0.43, 3.738, -1.82]}, {"time": 4.3667, "value": -1.97, "curve": [4.678, -2.04, 4.988, -1.77]}, {"time": 5.2333, "value": -1.45, "curve": [5.269, -1.4, 5.3, -1.35]}, {"time": 5.3333, "value": -1.3}]}, "toufa9": {"rotate": [{"curve": [0.224, 0, 0.779, 5.73]}, {"time": 1.3333, "value": 5.73, "curve": [1.89, 5.73, 2.222, 0]}, {"time": 2.6667, "curve": [2.89, 0, 3.446, 5.73]}, {"time": 4, "value": 5.73, "curve": [4.556, 5.73, 4.889, 0]}, {"time": 5.3333}]}, "touying": {"rotate": [{"curve": [0.224, 0, 0.779, -1.85]}, {"time": 1.3333, "value": -1.85, "curve": [1.89, -1.85, 2.222, 0]}, {"time": 2.6667, "curve": [2.89, 0, 3.446, -1.85]}, {"time": 4, "value": -1.85, "curve": [4.556, -1.85, 4.889, 0]}, {"time": 5.3333}]}, "toufa83": {"rotate": [{"value": 0.34, "curve": [0.054, 0.14, 0.098, 0]}, {"time": 0.1333, "curve": [0.357, 0, 0.913, 4.64]}, {"time": 1.4667, "value": 4.64, "curve": [1.934, 4.64, 2.267, 1.83]}, {"time": 2.6667, "value": 0.34, "curve": [2.72, 0.14, 2.764, 0]}, {"time": 2.8, "curve": [3.024, 0, 3.579, 4.64]}, {"time": 4.1333, "value": 4.64, "curve": [4.601, 4.64, 4.933, 1.83]}, {"time": 5.3333, "value": 0.34}]}, "toufa84": {"rotate": [{"value": 1.83, "curve": [0.128, 0.79, 0.23, 0]}, {"time": 0.3, "curve": [0.524, 0, 1.079, 7.72]}, {"time": 1.6333, "value": 7.72, "curve": [2.015, 7.72, 2.322, 4.63]}, {"time": 2.6667, "value": 1.83, "curve": [2.795, 0.79, 2.897, 0]}, {"time": 2.9667, "curve": [3.19, 0, 3.746, 7.72]}, {"time": 4.3, "value": 7.72, "curve": [4.682, 7.72, 4.989, 4.63]}, {"time": 5.3333, "value": 1.83}]}, "toufa85": {"rotate": [{"value": 5.09, "curve": [0.188, 2.37, 0.34, 0]}, {"time": 0.4333, "curve": [0.657, 0, 1.213, 13.3]}, {"time": 1.7667, "value": 13.3, "curve": [2.089, 13.3, 2.367, 9.44]}, {"time": 2.6667, "value": 5.09, "curve": [2.855, 2.37, 3.007, 0]}, {"time": 3.1, "curve": [3.324, 0, 3.879, 13.3]}, {"time": 4.4333, "value": 13.3, "curve": [4.756, 13.3, 5.033, 9.44]}, {"time": 5.3333, "value": 5.09}]}, "toufa86": {"rotate": [{"value": 6.67, "curve": [0.218, 3.24, 0.396, 0]}, {"time": 0.5, "curve": [0.724, 0, 1.279, 14.65]}, {"time": 1.8333, "value": 14.65, "curve": [2.128, 14.65, 2.389, 11.05]}, {"time": 2.6667, "value": 6.67, "curve": [2.884, 3.24, 3.062, 0]}, {"time": 3.1667, "curve": [3.39, 0, 3.946, 14.65]}, {"time": 4.5, "value": 14.65, "curve": [4.795, 14.65, 5.056, 11.05]}, {"time": 5.3333, "value": 6.67}]}, "toufa87": {"rotate": [{"value": 10.05, "curve": [0.263, 5.23, 0.48, 0]}, {"time": 0.6, "curve": [0.824, 0, 1.379, 17.97]}, {"time": 1.9333, "value": 17.97, "curve": [2.189, 17.97, 2.422, 14.53]}, {"time": 2.6667, "value": 10.05, "curve": [2.93, 5.23, 3.147, 0]}, {"time": 3.2667, "curve": [3.49, 0, 4.046, 17.97]}, {"time": 4.6, "value": 17.97, "curve": [4.856, 17.97, 5.089, 14.53]}, {"time": 5.3333, "value": 10.05}]}, "toufa96": {"rotate": [{"curve": [0.224, 0, 0.779, 4.85]}, {"time": 1.3333, "value": 4.85, "curve": [1.89, 4.85, 2.222, 0]}, {"time": 2.6667, "curve": [2.89, 0, 3.446, 4.85]}, {"time": 4, "value": 4.85, "curve": [4.556, 4.85, 4.889, 0]}, {"time": 5.3333}]}, "toufa103": {"rotate": [{"curve": [0.224, 0, 0.779, 2.62]}, {"time": 1.3333, "value": 2.62, "curve": [1.89, 2.62, 2.222, 0]}, {"time": 2.6667, "curve": [2.89, 0, 3.446, 2.62]}, {"time": 4, "value": 2.62, "curve": [4.556, 2.62, 4.889, 0]}, {"time": 5.3333}]}, "toufa104": {"rotate": [{"value": 0.32, "curve": [0.068, 0.13, 0.124, 0]}, {"time": 0.1667, "curve": [0.39, 0, 0.946, 3.18]}, {"time": 1.5, "value": 3.18, "curve": [1.949, 3.18, 2.278, 1.41]}, {"time": 2.6667, "value": 0.32, "curve": [2.735, 0.13, 2.79, 0]}, {"time": 2.8333, "curve": [3.057, 0, 3.613, 3.18]}, {"time": 4.1667, "value": 3.18, "curve": [4.615, 3.18, 4.944, 1.41]}, {"time": 5.3333, "value": 0.32}]}, "toufa105": {"rotate": [{"value": 1.07, "curve": [0.128, 0.46, 0.23, 0]}, {"time": 0.3, "curve": [0.524, 0, 1.079, 4.5]}, {"time": 1.6333, "value": 4.5, "curve": [2.015, 4.5, 2.322, 2.7]}, {"time": 2.6667, "value": 1.07, "curve": [2.795, 0.46, 2.897, 0]}, {"time": 2.9667, "curve": [3.19, 0, 3.746, 4.5]}, {"time": 4.3, "value": 4.5, "curve": [4.682, 4.5, 4.989, 2.7]}, {"time": 5.3333, "value": 1.07}]}, "toufa106": {"rotate": [{"value": 2.2, "curve": [0.204, 1.05, 0.368, 0]}, {"time": 0.4667, "curve": [0.69, 0, 1.246, 5.25]}, {"time": 1.8, "value": 5.25, "curve": [2.11, 5.25, 2.378, 3.83]}, {"time": 2.6667, "value": 2.2, "curve": [2.871, 1.05, 3.035, 0]}, {"time": 3.1333, "curve": [3.357, 0, 3.913, 5.25]}, {"time": 4.4667, "value": 5.25, "curve": [4.776, 5.25, 5.044, 3.83]}, {"time": 5.3333, "value": 2.2}]}, "yanbai": {"translate": [{"curve": [0.447, 0, 0.895, 2.32, 0.447, 0, 0.895, 4.19]}, {"time": 1.3333, "x": 2.32, "y": 4.19, "curve": [1.784, 2.32, 2.222, 0, 1.784, 4.19, 2.222, 0]}, {"time": 2.6667, "curve": [3.114, 0, 3.561, 2.32, 3.114, 0, 3.561, 4.19]}, {"time": 4, "x": 2.32, "y": 4.19, "curve": [4.45, 2.32, 4.889, 0, 4.45, 4.19, 4.889, 0]}, {"time": 5.3333}], "scale": [{}, {"time": 1.3333, "y": 0.934}, {"time": 2.6667}, {"time": 4, "y": 0.934}, {"time": 5.3333}]}, "tongkong": {"translate": [{}, {"time": 1.3333, "x": -6.17, "y": -16.51}, {"time": 2.6667}, {"time": 4, "x": -6.17, "y": -16.51}, {"time": 5.3333}]}, "lian2": {"translate": [{"curve": [0.224, 0, 0.779, 2.66, 0.224, 0, 0.779, -10.34]}, {"time": 1.3333, "x": 2.66, "y": -10.34, "curve": [1.89, 2.66, 2.222, 0, 1.89, -10.34, 2.222, 0]}, {"time": 2.6667, "curve": [2.89, 0, 3.446, 2.66, 2.89, 0, 3.446, -10.34]}, {"time": 4, "x": 2.66, "y": -10.34, "curve": [4.556, 2.66, 5.111, 0, 4.556, -10.34, 5.111, 0]}, {"time": 5.3333}]}, "biyan": {"translate": [{}, {"time": 1.3333, "x": -28.97, "y": -50.83}, {"time": 2.6667}, {"time": 3.3333, "x": 3.34, "y": 2.96, "curve": [3.412, 2.58, 3.487, 1.89, 3.412, 1.66, 3.487, 0.23]}, {"time": 3.5667, "x": 0.71, "y": -1.8, "curve": "stepped"}, {"time": 3.9333, "x": 0.71, "y": -1.8, "curve": [4.04, -0.81, 3.978, -28.97, 4.04, -4.41, 3.978, -50.83]}, {"time": 4, "x": -28.97, "y": -50.83}, {"time": 5.3333}]}, "meimao": {"rotate": [{}, {"time": 1.3333, "value": 9.54}, {"time": 2.6667}, {"time": 4, "value": 9.54}, {"time": 5.3333}], "translate": [{"curve": [0.447, 0, 0.895, 4.74, 0.447, 0, 0.895, 4.5]}, {"time": 1.3333, "x": 4.74, "y": 4.5, "curve": [1.784, 4.74, 2.222, 0, 1.784, 4.5, 2.222, 0]}, {"time": 2.6667, "curve": [3.114, 0, 3.561, 4.74, 3.114, 0, 3.561, 4.5]}, {"time": 4, "x": 4.74, "y": 4.5, "curve": [4.45, 4.74, 4.889, 0, 4.45, 4.5, 4.889, 0]}, {"time": 5.3333}], "scale": [{}, {"time": 1.3333, "y": 0.972}, {"time": 2.6667}, {"time": 4, "y": 0.972}, {"time": 5.3333}]}, "toufa121": {"translate": [{"curve": [0.447, 0, 0.895, -6.11, 0.447, 0, 0.895, -8.37]}, {"time": 1.3333, "x": -6.11, "y": -8.37, "curve": [1.784, -6.11, 2.222, 0, 1.784, -8.37, 2.222, 0]}, {"time": 2.6667, "curve": [3.114, 0, 3.561, -6.11, 3.114, 0, 3.561, -8.37]}, {"time": 4, "x": -6.11, "y": -8.37, "curve": [4.45, -6.11, 4.889, 0, 4.45, -8.37, 4.889, 0]}, {"time": 5.3333}]}, "shenti6": {"translate": [{"x": 11.96, "y": -0.94, "curve": [0.186, 6.95, 0.31, 1.92, 0.186, -0.71, 0.31, -0.37]}, {"time": 0.3333, "curve": [0.39, -4.68, 1.029, 22.3, 0.39, 0.9, 1.029, -0.7]}, {"time": 1.6667, "x": 24.63, "y": -1.15, "curve": [2.04, 25.99, 2.333, 20.95, 2.04, -1.41, 2.507, -1.15]}, {"time": 2.6667, "x": 11.96, "y": -0.94, "curve": [2.852, 6.95, 2.977, 1.92, 2.852, -0.71, 2.977, -0.37]}, {"time": 3, "curve": [3.056, -4.68, 3.695, 22.3, 3.056, 0.9, 3.695, -0.7]}, {"time": 4.3333, "x": 24.63, "y": -1.15, "curve": [4.706, 25.99, 5.079, 18.97, 4.706, -1.41, 5.079, -1.28]}, {"time": 5.3333, "x": 11.96, "y": -0.94}]}, "meimao2": {"translate": [{}, {"time": 1.3333, "x": 0.96, "y": -0.11}, {"time": 2.6667}, {"time": 4, "x": 0.96, "y": -0.11}, {"time": 5.3333}]}, "lian4": {"translate": [{}, {"time": 1.3333, "x": 1.91, "y": 5.01}, {"time": 2.6667}, {"time": 4, "x": 1.91, "y": 5.01}, {"time": 5.3333}]}, "toufa10": {"rotate": [{"value": 0.3, "curve": [0.054, 0.13, 0.098, 0]}, {"time": 0.1333, "curve": [0.357, 0, 0.913, 4.12]}, {"time": 1.4667, "value": 4.12, "curve": [1.934, 4.12, 2.267, 1.62]}, {"time": 2.6667, "value": 0.3, "curve": [2.72, 0.13, 2.764, 0]}, {"time": 2.8, "curve": [3.024, 0, 3.579, 4.12]}, {"time": 4.1333, "value": 4.12, "curve": [4.601, 4.12, 4.933, 1.62]}, {"time": 5.3333, "value": 0.3}]}, "toufa11": {"rotate": [{"value": 1.06, "curve": [0.044, 0.73, 0.692, 0.91]}, {"time": 1.3333, "value": 1.34, "curve": [1.916, 1.71, 2.631, 1.34]}, {"time": 2.6667, "value": 1.06, "curve": [2.71, 0.73, 3.359, 0.91]}, {"time": 4, "value": 1.34, "curve": [4.582, 1.71, 5.157, 2.42]}, {"time": 5.3333, "value": 1.06}]}, "toufa12": {"rotate": [{"value": 3.93, "curve": [0.081, 2.8, 0.712, 3.39]}, {"time": 1.3333, "value": 4.23, "curve": [1.928, 5.01, 2.645, 4.23]}, {"time": 2.6667, "value": 3.93, "curve": [2.748, 2.8, 3.379, 3.39]}, {"time": 4, "value": 4.23, "curve": [4.594, 5.01, 5.179, 6.1]}, {"time": 5.3333, "value": 3.93}]}, "toufa13": {"rotate": [{"value": 7.75, "curve": [0.11, 5.73, 0.722, 6.41]}, {"time": 1.3333, "value": 7.56, "curve": [1.932, 8.67, 2.656, 7.94]}, {"time": 2.6667, "value": 7.75, "curve": [2.777, 5.73, 3.389, 6.41]}, {"time": 4, "value": 7.56, "curve": [4.598, 8.67, 5.196, 10.26]}, {"time": 5.3333, "value": 7.75}]}, "toufa14": {"rotate": [{"value": 20.53, "curve": [0.145, 15.95, 0.742, 16.94]}, {"time": 1.3333, "value": 18.94, "curve": [1.947, 21, 2.616, 22.11]}, {"time": 2.6667, "value": 20.53, "curve": [2.812, 15.95, 3.409, 16.94]}, {"time": 4, "value": 18.94, "curve": [4.613, 21, 5.221, 24.08]}, {"time": 5.3333, "value": 20.53}]}, "toufa15": {"rotate": [{"curve": [0.224, 0, 0.779, 1.94]}, {"time": 1.3333, "value": 1.94, "curve": [1.89, 1.94, 2.222, 0]}, {"time": 2.6667, "curve": [2.89, 0, 3.446, 1.94]}, {"time": 4, "value": 1.94, "curve": [4.556, 1.94, 4.889, 0]}, {"time": 5.3333}]}, "toufa16": {"rotate": [{"value": 0.5, "curve": [0.068, 0.2, 0.124, 0]}, {"time": 0.1667, "curve": [0.39, 0, 0.946, 4.99]}, {"time": 1.5, "value": 4.99, "curve": [1.949, 4.99, 2.278, 2.22]}, {"time": 2.6667, "value": 0.5, "curve": [2.735, 0.2, 2.79, 0]}, {"time": 2.8333, "curve": [3.057, 0, 3.613, 4.99]}, {"time": 4.1667, "value": 4.99, "curve": [4.615, 4.99, 4.944, 2.22]}, {"time": 5.3333, "value": 0.5}]}, "toufa17": {"rotate": [{"value": 1.95, "curve": [0.142, 0.85, 0.257, 0]}, {"time": 0.3333, "curve": [0.557, 0, 1.113, 7.14]}, {"time": 1.6667, "value": 7.14, "curve": [2.032, 7.14, 2.333, 4.56]}, {"time": 2.6667, "value": 1.95, "curve": [2.808, 0.85, 2.924, 0]}, {"time": 3, "curve": [3.224, 0, 3.779, 7.14]}, {"time": 4.3333, "value": 7.14, "curve": [4.699, 7.14, 5, 4.56]}, {"time": 5.3333, "value": 1.95}]}, "toufa18": {"rotate": [{"value": 5.6, "curve": [0.218, 2.72, 0.396, 0]}, {"time": 0.5, "curve": [0.724, 0, 1.279, 12.32]}, {"time": 1.8333, "value": 12.32, "curve": [2.128, 12.32, 2.389, 9.29]}, {"time": 2.6667, "value": 5.6, "curve": [2.884, 2.72, 3.062, 0]}, {"time": 3.1667, "curve": [3.39, 0, 3.946, 12.32]}, {"time": 4.5, "value": 12.32, "curve": [4.795, 12.32, 5.056, 9.29]}, {"time": 5.3333, "value": 5.6}]}, "toufa19": {"rotate": [{"value": 12.85, "curve": [0.294, 7.04, 0.537, 0]}, {"time": 0.6667, "curve": [0.89, 0, 1.446, 20.5]}, {"time": 2, "value": 20.5, "curve": [2.232, 20.5, 2.444, 17.24]}, {"time": 2.6667, "value": 12.85, "curve": [2.961, 7.04, 3.204, 0]}, {"time": 3.3333, "curve": [3.557, 0, 4.113, 20.5]}, {"time": 4.6667, "value": 20.5, "curve": [4.898, 20.5, 5.111, 17.24]}, {"time": 5.3333, "value": 12.85}]}, "toufa20": {"rotate": [{"value": 21.66, "curve": [0.365, 13.43, 0.679, 0]}, {"time": 0.8333, "curve": [1.057, 0, 1.613, 27.9]}, {"time": 2.1667, "value": 27.9, "curve": [2.337, 27.9, 2.5, 25.41]}, {"time": 2.6667, "value": 21.66, "curve": [3.032, 13.43, 3.346, 0]}, {"time": 3.5, "curve": [3.724, 0, 4.279, 27.9]}, {"time": 4.8333, "value": 27.9, "curve": [5.004, 27.9, 5.167, 25.41]}, {"time": 5.3333, "value": 21.66}]}, "toufa21": {"rotate": [{"curve": [0.224, 0, 0.779, 2.79]}, {"time": 1.3333, "value": 2.79, "curve": [1.89, 2.79, 2.222, 0]}, {"time": 2.6667, "curve": [2.89, 0, 3.446, 2.79]}, {"time": 4, "value": 2.79, "curve": [4.556, 2.79, 4.889, 0]}, {"time": 5.3333}]}, "toufa22": {"rotate": [{"value": 0.32, "curve": [0.054, 0.14, 0.098, 0]}, {"time": 0.1333, "curve": [0.357, 0, 0.913, 4.37]}, {"time": 1.4667, "value": 4.37, "curve": [1.934, 4.37, 2.267, 1.72]}, {"time": 2.6667, "value": 0.32, "curve": [2.72, 0.14, 2.764, 0]}, {"time": 2.8, "curve": [3.024, 0, 3.579, 4.37]}, {"time": 4.1333, "value": 4.37, "curve": [4.601, 4.37, 4.933, 1.72]}, {"time": 5.3333, "value": 0.32}]}, "toufa23": {"rotate": [{"value": 1.07, "curve": [0.128, 0.46, 0.23, 0]}, {"time": 0.3, "curve": [0.524, 0, 1.079, 4.53]}, {"time": 1.6333, "value": 4.53, "curve": [2.015, 4.53, 2.322, 2.71]}, {"time": 2.6667, "value": 1.07, "curve": [2.795, 0.46, 2.897, 0]}, {"time": 2.9667, "curve": [3.19, 0, 3.746, 4.53]}, {"time": 4.3, "value": 4.53, "curve": [4.682, 4.53, 4.989, 2.71]}, {"time": 5.3333, "value": 1.07}]}, "toufa24": {"rotate": [{"value": 3.11, "curve": [0.218, 1.51, 0.396, 0]}, {"time": 0.5, "curve": [0.724, 0, 1.279, 6.84]}, {"time": 1.8333, "value": 6.84, "curve": [2.128, 6.84, 2.389, 5.15]}, {"time": 2.6667, "value": 3.11, "curve": [2.884, 1.51, 3.062, 0]}, {"time": 3.1667, "curve": [3.39, 0, 3.946, 6.84]}, {"time": 4.5, "value": 6.84, "curve": [4.795, 6.84, 5.056, 5.15]}, {"time": 5.3333, "value": 3.11}]}, "toufa25": {"rotate": [{"value": 6.66, "curve": [0.277, 3.55, 0.508, 0]}, {"time": 0.6333, "curve": [0.857, 0, 1.413, 11.23]}, {"time": 1.9667, "value": 11.23, "curve": [2.21, 11.23, 2.433, 9.28]}, {"time": 2.6667, "value": 6.66, "curve": [2.944, 3.55, 3.175, 0]}, {"time": 3.3, "curve": [3.524, 0, 4.079, 11.23]}, {"time": 4.6333, "value": 11.23, "curve": [4.876, 11.23, 5.1, 9.28]}, {"time": 5.3333, "value": 6.66}]}, "toufa26": {"rotate": [{"value": 11.13, "curve": [0.27, 6.2, 0.535, 0]}, {"time": 0.8, "curve": [1.244, 0, 1.689, 17.17]}, {"time": 2.1333, "value": 17.17, "curve": [2.313, 17.17, 2.489, 14.38]}, {"time": 2.6667, "value": 11.13, "curve": [2.936, 6.2, 3.201, 0]}, {"time": 3.4667, "curve": [3.911, 0, 4.356, 17.17]}, {"time": 4.8, "value": 17.17, "curve": [4.979, 17.17, 5.156, 14.38]}, {"time": 5.3333, "value": 11.13}]}, "toufa27": {"rotate": [{"curve": [0.224, 0, 0.779, 5.48]}, {"time": 1.3333, "value": 5.48, "curve": [1.89, 5.48, 2.222, 0]}, {"time": 2.6667, "curve": [2.89, 0, 3.446, 5.48]}, {"time": 4, "value": 5.48, "curve": [4.556, 5.48, 4.889, 0]}, {"time": 5.3333}]}, "toufa28": {"rotate": [{"value": 0.67, "curve": [0.054, 0.28, 0.098, 0]}, {"time": 0.1333, "curve": [0.357, 0, 0.913, 9.06]}, {"time": 1.4667, "value": 9.06, "curve": [1.934, 9.06, 2.267, 3.57]}, {"time": 2.6667, "value": 0.67, "curve": [2.72, 0.28, 2.764, 0]}, {"time": 2.8, "curve": [3.024, 0, 3.579, 9.06]}, {"time": 4.1333, "value": 9.06, "curve": [4.601, 9.06, 4.933, 3.57]}, {"time": 5.3333, "value": 0.67}]}, "toufa29": {"rotate": [{"value": 3.36, "curve": [0.157, 1.49, 0.284, 0]}, {"time": 0.3667, "curve": [0.59, 0, 1.146, 10.85]}, {"time": 1.7, "value": 10.85, "curve": [2.05, 10.85, 2.344, 7.21]}, {"time": 2.6667, "value": 3.36, "curve": [2.823, 1.49, 2.951, 0]}, {"time": 3.0333, "curve": [3.257, 0, 3.813, 10.85]}, {"time": 4.3667, "value": 10.85, "curve": [4.717, 10.85, 5.011, 7.21]}, {"time": 5.3333, "value": 3.36}]}, "toufa30": {"rotate": [{"value": 9.1, "curve": [0.218, 4.42, 0.396, 0]}, {"time": 0.5, "curve": [0.724, 0, 1.279, 19.99]}, {"time": 1.8333, "value": 19.99, "curve": [2.128, 19.99, 2.389, 15.07]}, {"time": 2.6667, "value": 9.1, "curve": [2.884, 4.42, 3.062, 0]}, {"time": 3.1667, "curve": [3.39, 0, 3.946, 19.99]}, {"time": 4.5, "value": 19.99, "curve": [4.795, 19.99, 5.056, 15.07]}, {"time": 5.3333, "value": 9.1}]}, "toufa82": {"rotate": [{"curve": [0.224, 0, 0.779, 2.57]}, {"time": 1.3333, "value": 2.57, "curve": [1.89, 2.57, 2.222, 0]}, {"time": 2.6667, "curve": [2.89, 0, 3.446, 2.57]}, {"time": 4, "value": 2.57, "curve": [4.556, 2.57, 4.889, 0]}, {"time": 5.3333}]}, "toufa88": {"rotate": [{"curve": [0.224, 0, 0.779, 3.84]}, {"time": 1.3333, "value": 3.84, "curve": [1.89, 3.84, 2.222, 0]}, {"time": 2.6667, "curve": [2.89, 0, 3.446, 3.84]}, {"time": 4, "value": 3.84, "curve": [4.556, 3.84, 4.889, 0]}, {"time": 5.3333}]}, "toufa89": {"rotate": [{"value": 0.47, "curve": [0.082, 0.19, 0.15, 0]}, {"time": 0.2, "curve": [0.424, 0, 0.979, 3.49]}, {"time": 1.5333, "value": 3.49, "curve": [1.964, 3.49, 2.289, 1.74]}, {"time": 2.6667, "value": 0.47, "curve": [2.749, 0.19, 2.816, 0]}, {"time": 2.8667, "curve": [3.09, 0, 3.646, 3.49]}, {"time": 4.2, "value": 3.49, "curve": [4.63, 3.49, 4.956, 1.74]}, {"time": 5.3333, "value": 0.47}]}, "toufa90": {"rotate": [{"value": 1.4, "curve": [0.082, 0.99, 0.706, -0.29]}, {"time": 1.3333, "value": 0.02, "curve": [1.922, 0.3, 2.389, 2.78]}, {"time": 2.6667, "value": 1.4, "curve": [2.749, 0.99, 3.372, -0.29]}, {"time": 4, "value": 0.02, "curve": [4.588, 0.3, 5.179, 2.17]}, {"time": 5.3333, "value": 1.4}]}, "toufa91": {"rotate": [{"value": 1.34, "curve": [0.105, 0.98, 0.714, -0.47]}, {"time": 1.3333, "value": -0.25, "curve": [1.925, -0.05, 2.222, 2.86]}, {"time": 2.6667, "value": 1.34, "curve": [2.771, 0.98, 3.381, -0.47]}, {"time": 4, "value": -0.25, "curve": [4.592, -0.05, 5.192, 1.82]}, {"time": 5.3333, "value": 1.34}]}, "toufa92": {"rotate": [{"value": 2.44, "curve": [0.119, 1.81, 0.727, 0.46]}, {"time": 1.3333, "value": 0.79, "curve": [1.935, 1.11, 2.353, 4.09]}, {"time": 2.6667, "value": 2.44, "curve": [2.785, 1.81, 3.393, 0.46]}, {"time": 4, "value": 0.79, "curve": [4.602, 1.11, 5.202, 3.13]}, {"time": 5.3333, "value": 2.44}]}, "toufa93": {"rotate": [{"value": 4.52, "curve": [0.138, 3.47, 0.733, 2.17]}, {"time": 1.3333, "value": 2.66, "curve": [1.939, 3.14, 2.422, 6.39]}, {"time": 2.6667, "value": 4.52, "curve": [2.805, 3.47, 3.4, 2.17]}, {"time": 4, "value": 2.66, "curve": [4.605, 3.14, 5.216, 5.41]}, {"time": 5.3333, "value": 4.52}]}, "toufa94": {"rotate": [{"value": 9.95, "curve": [0.308, 5.58, 0.565, 0]}, {"time": 0.7, "curve": [0.924, 0, 1.479, 15.11]}, {"time": 2.0333, "value": 15.11, "curve": [2.252, 15.11, 2.456, 12.95]}, {"time": 2.6667, "value": 9.95, "curve": [2.975, 5.58, 3.232, 0]}, {"time": 3.3667, "curve": [3.59, 0, 4.146, 15.11]}, {"time": 4.7, "value": 15.11, "curve": [4.919, 15.11, 5.122, 12.95]}, {"time": 5.3333, "value": 9.95}]}, "toufa95": {"rotate": [{"value": 31.03, "curve": [0.365, 19.25, 0.679, 0]}, {"time": 0.8333, "curve": [1.057, 0, 1.613, 39.97]}, {"time": 2.1667, "value": 39.97, "curve": [2.337, 39.97, 2.5, 36.41]}, {"time": 2.6667, "value": 31.03, "curve": [3.032, 19.25, 3.346, 0]}, {"time": 3.5, "curve": [3.724, 0, 4.279, 39.97]}, {"time": 4.8333, "value": 39.97, "curve": [5.004, 39.97, 5.167, 36.41]}, {"time": 5.3333, "value": 31.03}]}, "toufa97": {"rotate": [{"value": 0.61, "curve": [0.054, 0.25, 0.098, 0]}, {"time": 0.1333, "curve": [0.357, 0, 0.913, 8.23]}, {"time": 1.4667, "value": 8.23, "curve": [1.934, 8.23, 2.267, 3.24]}, {"time": 2.6667, "value": 0.61, "curve": [2.72, 0.25, 2.764, 0]}, {"time": 2.8, "curve": [3.024, 0, 3.579, 8.23]}, {"time": 4.1333, "value": 8.23, "curve": [4.601, 8.23, 4.933, 3.24]}, {"time": 5.3333, "value": 0.61}]}, "toufa98": {"rotate": [{"value": 1.01, "curve": [0.082, 0.41, 0.15, 0]}, {"time": 0.2, "curve": [0.424, 0, 0.979, 7.56]}, {"time": 1.5333, "value": 7.56, "curve": [1.964, 7.56, 2.289, 3.77]}, {"time": 2.6667, "value": 1.01, "curve": [2.749, 0.41, 2.816, 0]}, {"time": 2.8667, "curve": [3.09, 0, 3.646, 7.56]}, {"time": 4.2, "value": 7.56, "curve": [4.63, 7.56, 4.956, 3.77]}, {"time": 5.3333, "value": 1.01}]}, "toufa99": {"rotate": [{"value": 2.67, "curve": [0.128, 1.15, 0.23, 0]}, {"time": 0.3, "curve": [0.524, 0, 1.079, 11.28]}, {"time": 1.6333, "value": 11.28, "curve": [2.015, 11.28, 2.322, 6.76]}, {"time": 2.6667, "value": 2.67, "curve": [2.795, 1.15, 2.897, 0]}, {"time": 2.9667, "curve": [3.19, 0, 3.746, 11.28]}, {"time": 4.3, "value": 11.28, "curve": [4.682, 11.28, 4.989, 6.76]}, {"time": 5.3333, "value": 2.67}]}, "toufa100": {"rotate": [{"value": 8.09, "curve": [0.218, 3.93, 0.396, 0]}, {"time": 0.5, "curve": [0.724, 0, 1.279, 17.78]}, {"time": 1.8333, "value": 17.78, "curve": [2.128, 17.78, 2.389, 13.41]}, {"time": 2.6667, "value": 8.09, "curve": [2.884, 3.93, 3.062, 0]}, {"time": 3.1667, "curve": [3.39, 0, 3.946, 17.78]}, {"time": 4.5, "value": 17.78, "curve": [4.795, 17.78, 5.056, 13.41]}, {"time": 5.3333, "value": 8.09}]}, "toufa101": {"rotate": [{"value": 1.31, "curve": [0.054, 0.55, 0.098, 0]}, {"time": 0.1333, "curve": [0.357, 0, 0.913, 17.75]}, {"time": 1.4667, "value": 17.75, "curve": [1.934, 17.75, 2.267, 7]}, {"time": 2.6667, "value": 1.31, "curve": [2.72, 0.55, 2.764, 0]}, {"time": 2.8, "curve": [3.024, 0, 3.579, 17.75]}, {"time": 4.1333, "value": 17.75, "curve": [4.601, 17.75, 4.933, 7]}, {"time": 5.3333, "value": 1.31}]}, "toufa102": {"rotate": [{"value": 30.31, "curve": [0.308, 16.99, 0.565, 0]}, {"time": 0.7, "curve": [0.924, 0, 1.479, 46]}, {"time": 2.0333, "value": 46, "curve": [2.252, 46, 2.456, 39.44]}, {"time": 2.6667, "value": 30.31, "curve": [2.975, 16.99, 3.232, 0]}, {"time": 3.3667, "curve": [3.59, 0, 4.146, 46]}, {"time": 4.7, "value": 46, "curve": [4.919, 46, 5.122, 39.44]}, {"time": 5.3333, "value": 30.31}]}, "toufa107": {"rotate": [{"value": 3.11, "curve": [0.233, 1.55, 0.424, 0]}, {"time": 0.5333, "curve": [0.757, 0, 1.313, 6.34]}, {"time": 1.8667, "value": 6.34, "curve": [2.149, 6.34, 2.4, 4.9]}, {"time": 2.6667, "value": 3.11, "curve": [2.9, 1.55, 3.09, 0]}, {"time": 3.2, "curve": [3.424, 0, 3.979, 6.34]}, {"time": 4.5333, "value": 6.34, "curve": [4.815, 6.34, 5.067, 4.9]}, {"time": 5.3333, "value": 3.11}]}, "toufa108": {"rotate": [{"value": 5.49, "curve": [0.263, 2.86, 0.48, 0]}, {"time": 0.6, "curve": [0.824, 0, 1.379, 9.82]}, {"time": 1.9333, "value": 9.82, "curve": [2.189, 9.82, 2.422, 7.94]}, {"time": 2.6667, "value": 5.49, "curve": [2.93, 2.86, 3.147, 0]}, {"time": 3.2667, "curve": [3.49, 0, 4.046, 9.82]}, {"time": 4.6, "value": 9.82, "curve": [4.856, 9.82, 5.089, 7.94]}, {"time": 5.3333, "value": 5.49}]}, "toufa109": {"rotate": [{"value": 25.11, "curve": [0.353, 15.15, 0.651, 0]}, {"time": 0.8, "curve": [1.024, 0, 1.579, 33.61]}, {"time": 2.1333, "value": 33.61, "curve": [2.317, 33.61, 2.489, 30.12]}, {"time": 2.6667, "value": 25.11, "curve": [3.02, 15.15, 3.318, 0]}, {"time": 3.4667, "curve": [3.69, 0, 4.246, 33.61]}, {"time": 4.8, "value": 33.61, "curve": [4.984, 33.61, 5.156, 30.12]}, {"time": 5.3333, "value": 25.11}]}, "shenti5": {"translate": [{"x": 11.96, "y": -0.94, "curve": [0.186, 6.95, 0.31, 1.92, 0.186, -0.71, 0.31, -0.37]}, {"time": 0.3333, "curve": [0.39, -4.68, 1.029, 22.3, 0.39, 0.9, 1.029, -0.7]}, {"time": 1.6667, "x": 24.63, "y": -1.15, "curve": [2.04, 25.99, 2.333, 20.95, 2.04, -1.41, 2.507, -1.15]}, {"time": 2.6667, "x": 11.96, "y": -0.94, "curve": [2.852, 6.95, 2.977, 1.92, 2.852, -0.71, 2.977, -0.37]}, {"time": 3, "curve": [3.056, -4.68, 3.695, 22.3, 3.056, 0.9, 3.695, -0.7]}, {"time": 4.3333, "x": 24.63, "y": -1.15, "curve": [4.706, 25.99, 5.079, 18.97, 4.706, -1.41, 5.079, -1.28]}, {"time": 5.3333, "x": 11.96, "y": -0.94}]}, "bone": {"translate": [{"curve": [0.444, 0, 0.889, 0, 0.444, 0, 0.889, -19.06]}, {"time": 1.3333, "y": -19.06, "curve": [1.778, 0, 2.222, 0, 1.778, -19.06, 2.222, 0]}, {"time": 2.6667, "curve": [3.111, 0, 3.556, 0, 3.111, 0, 3.556, -19.06]}, {"time": 4, "y": -19.06, "curve": [4.444, 0, 4.889, 0, 4.444, -19.06, 4.889, 0]}, {"time": 5.3333}]}, "yaodai3": {"translate": [{}, {"time": 1.3333, "x": -9.74, "y": 4.81}, {"time": 2.6667}, {"time": 4, "x": -9.74, "y": 4.81}, {"time": 5.3333}]}, "bone2": {"translate": [{"curve": [0.447, 0, 0.895, 204.51, 0.447, 0, 0.895, 0]}, {"time": 1.3333, "x": 204.51, "curve": [1.784, 204.51, 2.222, 0, 1.784, 0, 2.222, 0]}, {"time": 2.6667, "curve": [3.114, 0, 3.561, 204.51, 3.114, 0, 3.561, 0]}, {"time": 4, "x": 204.51, "curve": [4.45, 204.51, 4.889, 0, 4.45, 0, 4.889, 0]}, {"time": 5.3333}]}, "youshou": {"rotate": [{"value": -1.29}]}, "target4": {"translate": [{}, {"time": 1.3333, "x": 1.55}, {"time": 2.6667}, {"time": 4, "x": 1.55}, {"time": 5.3333}]}, "youshou3": {"translate": [{}, {"time": 1.3333, "x": 5.64, "y": 11.34}, {"time": 2.6667}, {"time": 4, "x": 5.64, "y": 11.34}, {"time": 5.3333}]}}, "attachments": {"default": {"biyan": {"biyan": {"deform": [{"time": 3.3333, "vertices": [-24.60425, 9.95404, 24.8269, 38.48798, 39.06641, -8.05139, -24.56689, -2.99921, -15.26709, 2.4397, 0, 0, 43.58008, 25.85577, 50.2417, 24.18927, 81.37427, -33.48193, 23.59497, 2.55145, 0, 0, -33.54053, 1.14465]}, {"time": 3.5667, "curve": "stepped"}, {"time": 3.7}, {"time": 3.9333, "vertices": [-24.60425, 9.95404, 24.8269, 38.48798, 39.06641, -8.05139, -24.56689, -2.99921, -15.26709, 2.4397, 0, 0, 43.58008, 25.85577, 50.2417, 24.18927, 81.37427, -33.48193, 23.59497, 2.55145, 0, 0, -33.54053, 1.14465]}]}}, "shouyinying": {"shouyinying": {"deform": [{}, {"time": 1.3333, "offset": 10, "vertices": [11.0361, 29.67767, 3.03687, 34.06653, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -20.48334, 33.01192, 10.94318, 8.08449, 29.4245, 16.53258]}, {"time": 2.6667}, {"time": 4, "offset": 10, "vertices": [11.0361, 29.67767, 3.03687, 34.06653, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -20.48334, 33.01192, 10.94318, 8.08449, 29.4245, 16.53258]}, {"time": 5.3333}]}}}}}, "xiuxian": {"slots": {"biyan": {"rgba": [{"time": 3.3333, "color": "ffffff00"}, {"time": 3.4333, "color": "ffffffff", "curve": "stepped"}, {"time": 3.8333, "color": "ffffffff"}, {"time": 3.9333, "color": "ffffff00", "curve": "stepped"}, {"time": 5.8333, "color": "ffffff00"}, {"time": 5.9, "color": "ffffffff", "curve": "stepped"}, {"time": 7.8, "color": "ffffffff"}, {"time": 7.8333, "color": "ffffff00"}], "attachment": [{"time": 3.3333, "name": "biyan"}, {"time": 3.9333}, {"time": 5.8333, "name": "biyan"}]}}, "bones": {"bone": {"rotate": [{"time": 5.8333, "curve": [6.167, -0.28, 6.5, -0.83]}, {"time": 6.8333, "value": -0.83, "curve": [7.444, -0.83, 8.056, 0]}, {"time": 8.6667}], "translate": [{"curve": [0.444, 0, 0.889, 0, 0.444, 0, 0.889, -19.06]}, {"time": 1.3333, "y": -19.06, "curve": [1.778, 0, 2.222, 0, 1.778, -19.06, 2.222, 0]}, {"time": 2.6667, "curve": [3.111, 0, 3.556, 0, 3.111, 0, 3.556, -19.06]}, {"time": 4, "y": -19.06, "curve": [4.444, 0, 4.889, 0, 4.444, -19.06, 4.889, 0]}, {"time": 5.3333}, {"time": 5.5667, "x": 8.12}, {"time": 5.8333, "x": 12.17, "curve": [6, 15.95, 6.167, 19.73, 6, -12.09, 6.167, -36.27]}, {"time": 6.3333, "x": 23.51, "y": -36.27, "curve": [6.5, 27.29, 6.667, 34.84, 6.5, -36.27, 6.667, 0]}, {"time": 6.8333, "x": 34.84, "curve": [7, 34.84, 7.167, 15.2, 7, 0, 7.167, -22.67]}, {"time": 7.3333, "x": 5.37, "y": -22.67, "curve": [7.5, -4.45, 7.667, -24.1, 7.5, -22.67, 7.667, 0]}, {"time": 7.8333, "x": -24.1, "curve": [8.111, -24.1, 8.389, 0, 8.111, 0, 8.389, 0]}, {"time": 8.6667}]}, "yaodai": {"rotate": [{"time": 5.8333}, {"time": 7.8333, "value": -0.83}, {"time": 8.1667, "value": -0.1}, {"time": 8.6667}], "translate": [{"curve": [0.444, 0, 0.889, 11.89, 0.444, 0, 0.889, -2.97]}, {"time": 1.3333, "x": 11.89, "y": -2.97, "curve": [1.778, 11.89, 2.222, 0, 1.778, -2.97, 2.222, 0]}, {"time": 2.6667, "curve": [3.111, 0, 3.556, 11.89, 3.111, 0, 3.556, -2.97]}, {"time": 4, "x": 11.89, "y": -2.97, "curve": [4.444, 11.89, 4.889, 5.45, 4.444, -2.97, 4.889, 2.97]}, {"time": 5.3333, "curve": "stepped"}, {"time": 5.8333}, {"time": 6.8667, "x": -15.46, "y": -0.22}, {"time": 7.8333}]}, "yaodai2": {"rotate": [{"time": 5.8333}, {"time": 7.8333, "value": -0.83}, {"time": 8.1667, "value": -0.1}, {"time": 8.6667}], "translate": [{"time": 5.8333}, {"time": 6.8, "x": -3.61, "y": 0.62}, {"time": 7.8333, "x": -5.49, "y": 3.05}, {"time": 8.1667}]}, "yaodai3": {"rotate": [{"time": 5.5667}, {"time": 5.8333, "value": -0.23}, {"time": 6.8, "value": -4.27}, {"time": 7.8333, "value": 2.15}, {"time": 8.1667, "value": -2.2}, {"time": 8.6667}], "translate": [{"time": 5.5667}, {"time": 5.8333, "x": -0.79, "y": -12.16}, {"time": 6.8, "x": -28.33, "y": 1.98}, {"time": 7.8333, "x": -30.16, "y": 7.18}, {"time": 8.1667, "x": -22.91, "y": 5.91}, {"time": 8.6667}]}, "shenti": {"rotate": [{"curve": [0.444, 0, 0.895, -1.4]}, {"time": 1.3333, "value": -1.4, "curve": [1.784, -1.4, 2.222, 0]}, {"time": 2.6667, "curve": [3.111, 0, 3.561, -1.4]}, {"time": 4, "value": -1.4, "curve": [4.45, -1.4, 4.889, 0]}, {"time": 5.3333}, {"time": 5.5667, "value": -2.07}, {"time": 5.8333, "value": -3.1, "curve": [5.989, -3.5, 7.778, -2.65]}, {"time": 8, "value": -2.02, "curve": [8.222, -1.4, 8.444, 0]}, {"time": 8.6667}], "translate": [{"curve": [0.444, 0, 0.889, 0, 0.444, 0, 0.889, 21.45]}, {"time": 1.3333, "y": 21.45, "curve": [1.778, 0, 2.222, 0, 1.778, 21.45, 2.222, 0]}, {"time": 2.6667, "curve": [3.111, 0, 3.556, 0, 3.111, 0, 3.556, 21.45]}, {"time": 4, "y": 21.45, "curve": [4.444, 0, 4.889, 0, 4.444, 21.45, 4.889, 0]}, {"time": 5.3333}]}, "shenti2": {"rotate": [{"curve": [0.444, 1.06, 0.895, 4.91]}, {"time": 1.3333, "value": 4.91, "curve": [1.784, 4.91, 2.222, -1.06]}, {"time": 2.6667, "curve": [3.111, 1.06, 3.561, 4.91]}, {"time": 4, "value": 4.91, "curve": [4.45, 4.91, 4.889, 0]}, {"time": 5.3333}, {"time": 5.5667, "value": -2.07}, {"time": 5.8333, "value": -3.1, "curve": [5.989, -3.5, 6.144, -4.31]}, {"time": 6.3, "value": -4.31, "curve": [6.489, -4.31, 6.678, -3.1]}, {"time": 6.8667, "value": -3.1, "curve": [7.022, -3.1, 7.178, -3.75]}, {"time": 7.3333, "value": -3.75, "curve": [7.556, -3.75, 7.778, -2.65]}, {"time": 8, "value": -2.02, "curve": [8.222, -1.4, 8.444, -0.53]}, {"time": 8.6667}], "translate": [{"curve": [0.222, 0, 0.444, -16.38, 0.222, 0, 0.444, -8]}, {"time": 0.6667, "x": -16.38, "y": -8, "curve": [1.111, -16.38, 1.556, 6.29, 1.111, -8, 1.556, 3.57]}, {"time": 2, "x": 6.29, "y": 3.57, "curve": [2.222, 6.29, 2.444, 0, 2.222, 3.57, 2.444, 0]}, {"time": 2.6667, "curve": [2.889, 0, 3.111, -16.38, 2.889, 0, 3.111, -8]}, {"time": 3.3333, "x": -16.38, "y": -8, "curve": [3.778, -16.38, 4.222, 6.29, 3.778, -8, 4.222, 3.57]}, {"time": 4.6667, "x": 6.29, "y": 3.57, "curve": [4.889, 6.29, 5.111, 3.78, 4.889, 3.57, 5.111, 1.93]}, {"time": 5.3333}]}, "shenti3": {"rotate": [{"curve": [0.444, 0.66, 0.895, -0.06]}, {"time": 1.3333, "value": -0.06, "curve": [1.784, -0.06, 2.627, -0.06]}, {"time": 2.6667, "curve": [3.111, 0.66, 3.561, -0.06]}, {"time": 4, "value": -0.06, "curve": [4.45, -0.06, 4.889, 0]}, {"time": 5.3333}, {"time": 5.5667, "value": -2.07}, {"time": 5.8333, "value": -3.1, "curve": [5.989, -3.5, 6.144, -4.31]}, {"time": 6.3, "value": -4.31, "curve": [6.489, -4.31, 6.678, -3.1]}, {"time": 6.8667, "value": -3.1, "curve": [7.022, -3.1, 7.178, -3.75]}, {"time": 7.3333, "value": -3.75, "curve": [7.556, -3.75, 7.778, -2.65]}, {"time": 8, "value": -2.02, "curve": [8.222, -1.4, 8.444, -0.33]}, {"time": 8.6667}], "translate": [{"curve": [0.444, 0, 0.889, -13.37, 0.444, 0, 0.889, -2.54]}, {"time": 1.3333, "x": -13.37, "y": -2.54, "curve": [1.778, -13.37, 2.222, 0, 1.778, -2.54, 2.222, 0]}, {"time": 2.6667, "curve": [3.111, 0, 3.556, -13.37, 3.111, 0, 3.556, -2.54]}, {"time": 4, "x": -13.37, "y": -2.54, "curve": [4.444, -13.37, 4.889, 0, 4.444, -2.54, 4.889, 0]}, {"time": 5.3333}]}, "shenti4": {"rotate": [{"curve": [0.444, 0, 0.889, -1.12]}, {"time": 1.3333, "value": -1.12, "curve": [1.778, -1.12, 2.222, 0]}, {"time": 2.6667, "curve": [3.111, 0, 3.556, -1.12]}, {"time": 4, "value": -1.12, "curve": [4.444, -1.12, 4.889, 0]}, {"time": 5.3333}, {"time": 5.5667, "value": -1.72}, {"time": 5.8333, "value": -2.58, "curve": [5.989, -2.66, 6.144, -2.83]}, {"time": 6.3, "value": -2.83, "curve": [6.644, -2.83, 6.989, -2.83]}, {"time": 7.3333, "value": -2.77, "curve": [7.556, -2.74, 7.778, -2.54]}, {"time": 8, "value": -2.08, "curve": [8.222, -1.62, 8.444, 0]}, {"time": 8.6667}]}, "lian": {"rotate": [{"curve": [0.444, -1.48, 0.895, -3.33]}, {"time": 1.3333, "value": -3.33, "curve": [1.784, -3.33, 2.222, 1.48]}, {"time": 2.6667, "curve": [3.111, -1.48, 3.561, -3.33]}, {"time": 4, "value": -3.33, "curve": [4.45, -3.33, 4.889, 0]}, {"time": 5.3333}, {"time": 5.5667, "value": -1.72}, {"time": 5.8333, "value": -2.58, "curve": [5.989, -0.03, 6.144, 5.07]}, {"time": 6.3, "value": 5.07, "curve": [6.644, 5.07, 6.989, -4.04]}, {"time": 7.3333, "value": -4.04, "curve": [7.501, -4.04, 7.668, 0.09]}, {"time": 7.8333, "value": 2.16, "curve": [7.89, 2.84, 7.945, 3.34]}, {"time": 8, "value": 3.34, "curve": [8.112, 3.34, 8.224, 2.7]}, {"time": 8.3333, "value": 1.95, "curve": [8.446, 1.21, 8.556, 0.37]}, {"time": 8.6667}]}, "zuoshou2": {"rotate": [{}, {"time": 1.3333, "value": -4.53}, {"time": 2.6667}, {"time": 4, "value": -4.53}, {"time": 5.3333}, {"time": 6, "value": 65.39}, {"time": 6.2333, "value": 68.8}, {"time": 6.4667, "value": 70.74}, {"time": 6.9, "value": 65.39}, {"time": 7.1667, "value": 68.8}, {"time": 7.4, "value": 70.74}, {"time": 7.8333, "value": 65.39}, {"time": 8.6667}], "translate": [{"time": 6}, {"time": 6.2333, "x": 23.09, "y": -10.74}, {"time": 6.4667, "x": 1.4, "y": 2.57}, {"time": 6.7, "x": 9.79, "y": -9.13}, {"time": 6.9}, {"time": 7.1667, "x": 23.09, "y": -10.74}, {"time": 7.4, "x": 1.4, "y": 2.57}, {"time": 7.6333, "x": 9.79, "y": -9.13}, {"time": 7.8333}]}, "zuoshou1": {"rotate": [{}, {"time": 1.3333, "value": 2.43}, {"time": 2.6667}, {"time": 4, "value": 2.43}, {"time": 5.3333}, {"time": 6, "value": -22.05}, {"time": 6.4667, "value": -26.64}, {"time": 6.9, "value": -22.05}, {"time": 7.4, "value": -26.64}, {"time": 7.8333, "value": -22.05}, {"time": 8.6667}]}, "zuoshou": {"rotate": [{}, {"time": 1.3333, "value": -2.11}, {"time": 2.6667}, {"time": 4, "value": -2.11}, {"time": 5.3333}]}, "zuoshou3": {"rotate": [{}, {"time": 1.3333, "value": -2.11}, {"time": 2.6667}, {"time": 4, "value": -2.11}, {"time": 5.3333}, {"time": 6, "value": -9.14}, {"time": 6.4667, "value": -19.7}, {"time": 7, "value": -9.14}, {"time": 7.4, "value": -19.7}, {"time": 7.8333, "value": -9.14}, {"time": 8.6667}]}, "zuoshou4": {"rotate": [{}, {"time": 1.3333, "value": -13.35}, {"time": 2.6667}, {"time": 4, "value": -13.35}, {"time": 5.3333}, {"time": 6, "value": -9.14}, {"time": 6.4667, "value": -19.7}, {"time": 7, "value": -9.14}, {"time": 7.4, "value": -19.7}, {"time": 7.8333, "value": -9.14}, {"time": 8.6667}]}, "zuoshou5": {"rotate": [{}, {"time": 1.3333, "value": -2.11}, {"time": 2.6667}, {"time": 4, "value": -2.11}, {"time": 5.3333}, {"time": 6, "value": -9.14}, {"time": 6.4667, "value": -19.7}, {"time": 7, "value": -9.14}, {"time": 7.4, "value": -19.7}, {"time": 7.8333, "value": -9.14}, {"time": 8.6667}]}, "zuoshou6": {"rotate": [{}, {"time": 1.3333, "value": -13.35}, {"time": 2.6667}, {"time": 4, "value": -13.35}, {"time": 5.3333}, {"time": 6, "value": -9.14}, {"time": 6.4667, "value": -19.7}, {"time": 7, "value": -9.14}, {"time": 7.4, "value": -19.7}, {"time": 7.8333, "value": -9.14}, {"time": 8.6667}]}, "shoubiao": {"rotate": [{"time": 5.8333}, {"time": 7.8333, "value": -0.83}, {"time": 8.1667, "value": -0.1}, {"time": 8.6667}], "translate": [{"curve": [0.447, 0, 0.895, 1.37, 0.447, 0, 0.895, -17.68]}, {"time": 1.3333, "x": 1.37, "y": -17.68, "curve": [1.784, 1.37, 2.222, 0, 1.784, -17.68, 2.222, 0]}, {"time": 2.6667, "curve": [3.114, 0, 3.561, 1.37, 3.114, 0, 3.561, -17.68]}, {"time": 4, "x": 1.37, "y": -17.68, "curve": [4.45, 1.37, 4.889, 0, 4.45, -17.68, 4.889, 0]}, {"time": 5.3333}]}, "youshou3": {"rotate": [{"time": 5.3333}, {"time": 5.8333, "value": 0.98, "curve": [6.778, 0.98, 7.722, 0]}, {"time": 8.6667}], "translate": [{}, {"time": 1.3333, "x": 5.64, "y": 11.34}, {"time": 2.6667}, {"time": 4, "x": 5.64, "y": 11.34}, {"time": 5.3333, "curve": "stepped"}, {"time": 5.8333, "curve": [5.922, -3.68, 6.256, -42.05, 5.922, -1.22, 6.256, -1.75]}, {"time": 6.3333, "x": -42.05, "y": -1.75, "curve": [6.511, -42.05, 6.689, 1.71, 6.511, -1.75, 6.689, -4.45]}, {"time": 6.8667, "x": 1.71, "y": -4.45, "curve": [7.022, 1.71, 7.178, -18.26, 7.022, -4.45, 7.178, -3.54]}, {"time": 7.3333, "x": -18.26, "y": -2.01, "curve": [7.556, -18.26, 7.778, -18.26, 7.556, 0.16, 7.778, 6.65]}, {"time": 8, "x": -17.61, "y": 6.65, "curve": [8.222, -16.97, 8.444, -2.58, 8.222, 6.65, 8.444, 0]}, {"time": 8.6667}]}, "youshou": {"rotate": [{"value": -1.29, "curve": "stepped"}, {"time": 5.3333, "value": -1.29}, {"time": 5.5667, "value": 15.19, "curve": [5.656, 17.81, 5.744, 23.06]}, {"time": 5.8333, "value": 23.06, "curve": "stepped"}, {"time": 7.8333, "value": 23.06, "curve": [7.944, 23.06, 8.056, 5.08]}, {"time": 8.1667, "value": 2.53, "curve": [8.333, -1.29, 8.5, -1.29]}, {"time": 8.6667, "value": -1.29}]}, "erji": {"rotate": [{"time": 5.8333}, {"time": 7.8333, "value": -0.83}, {"time": 8.1667, "value": -0.1}, {"time": 8.6667}]}, "toufa1": {"rotate": [{"time": 5.3333, "curve": [5.444, 0, 5.556, -1.71]}, {"time": 5.6667, "value": -0.85, "curve": [5.744, -0.26, 6.188, 2.12]}, {"time": 6.3333, "value": 2.12, "curve": [6.468, 2.12, 6.602, 0.71]}, {"time": 6.7333, "value": 0.4, "curve": [6.88, 0.06, 7.023, -1.01]}, {"time": 7.1667, "value": -1.01, "curve": [7.222, -1.01, 7.667, 1.79]}, {"time": 7.8333, "value": 1.79, "curve": [8, 1.79, 8.556, 0]}, {"time": 8.6667}]}, "toufa2": {"rotate": [{"value": -0.2, "curve": [0.057, -0.14, 0.101, -0.09]}, {"time": 0.1333, "value": -0.06, "curve": [0.152, -0.04, 0.809, -1.01]}, {"time": 1.4667, "value": -1.05, "curve": [1.941, -1.08, 2.267, -0.64]}, {"time": 2.6667, "value": -0.2, "curve": [2.723, -0.14, 2.767, -0.09]}, {"time": 2.8, "value": -0.06, "curve": [2.819, -0.04, 3.476, -1.01]}, {"time": 4.1333, "value": -1.05, "curve": [4.607, -1.08, 4.933, -0.64]}, {"time": 5.3333, "value": -0.2, "curve": [5.444, -0.08, 5.556, -1.78]}, {"time": 5.6667, "value": -0.89, "curve": [5.744, -0.26, 6.188, 2.12]}, {"time": 6.3333, "value": 2.12, "curve": [6.468, 2.12, 6.602, 0.71]}, {"time": 6.7333, "value": 0.4, "curve": [6.88, 0.06, 7.023, -1.01]}, {"time": 7.1667, "value": -1.01, "curve": [7.222, -1.01, 7.667, 1.79]}, {"time": 7.8333, "value": 1.79, "curve": [8, 1.79, 8.556, -0.2]}, {"time": 8.6667, "value": -0.2}]}, "toufa3": {"rotate": [{"value": -0.97, "curve": [0.139, -0.68, 0.243, -0.41]}, {"time": 0.3, "value": -0.29, "curve": [0.347, -0.2, 0.994, -2.07]}, {"time": 1.6333, "value": -2.18, "curve": [2.021, -2.24, 2.322, -1.71]}, {"time": 2.6667, "value": -0.97, "curve": [2.806, -0.68, 2.91, -0.41]}, {"time": 2.9667, "value": -0.29, "curve": [3.014, -0.2, 3.661, -2.07]}, {"time": 4.3, "value": -2.18, "curve": [4.688, -2.24, 4.989, -1.71]}, {"time": 5.3333, "value": -0.97, "curve": [5.556, -0.5, 5.631, -2.79]}, {"time": 6, "value": -2.78, "curve": [6.2, -2.78, 6.43, -4.65]}, {"time": 6.6667, "value": -4.52, "curve": [6.889, -4.4, 7.12, 1.07]}, {"time": 7.3333, "value": 1.41, "curve": [7.514, 1.69, 7.686, 4.44]}, {"time": 7.8333, "value": 4.14, "curve": [8.298, 3.23, 8.628, -0.97]}, {"time": 8.6667, "value": -0.97}]}, "toufa4": {"rotate": [{"value": -1.3, "curve": [0.176, -1.04, 0.307, -0.77]}, {"time": 0.3667, "value": -0.61, "curve": [0.436, -0.43, 1.071, -1.82]}, {"time": 1.7, "value": -1.97, "curve": [2.012, -2.04, 2.322, -1.77]}, {"time": 2.5667, "value": -1.45, "curve": [2.602, -1.4, 2.633, -1.35]}, {"time": 2.6667, "value": -1.3, "curve": [2.843, -1.04, 2.974, -0.77]}, {"time": 3.0333, "value": -0.61, "curve": [3.103, -0.43, 3.738, -1.82]}, {"time": 4.3667, "value": -1.97, "curve": [4.678, -2.04, 4.988, -1.77]}, {"time": 5.2333, "value": -1.45, "curve": [5.269, -1.4, 5.3, -1.35]}, {"time": 5.3333, "value": -1.3, "curve": [5.556, -0.98, 5.631, -3.15]}, {"time": 6, "value": -3.14, "curve": [6.2, -3.14, 6.43, -5.01]}, {"time": 6.6667, "value": -4.88, "curve": [6.889, -4.76, 7.12, 0.71]}, {"time": 7.3333, "value": 1.06, "curve": [7.514, 1.34, 7.686, 4.09]}, {"time": 7.8333, "value": 3.8, "curve": [8.298, 2.9, 8.628, -1.3]}, {"time": 8.6667, "value": -1.3}]}, "toufa5": {"rotate": [{"value": -2.28, "curve": [0.253, -1.88, 0.439, -1.39]}, {"time": 0.5, "value": -1.2, "curve": [0.602, -0.89, 1.223, -2.73]}, {"time": 1.8333, "value": -2.91, "curve": [2.119, -2.99, 2.403, -2.7]}, {"time": 2.6333, "value": -2.34, "curve": [2.645, -2.32, 2.656, -2.3]}, {"time": 2.6667, "value": -2.28, "curve": [2.919, -1.88, 3.106, -1.39]}, {"time": 3.1667, "value": -1.2, "curve": [3.269, -0.89, 3.89, -2.73]}, {"time": 4.5, "value": -2.91, "curve": [4.786, -2.99, 5.069, -2.7]}, {"time": 5.3, "value": -2.34, "curve": [5.312, -2.32, 5.322, -2.3]}, {"time": 5.3333, "value": -2.28, "curve": [5.556, -1.93, 5.631, -4.12]}, {"time": 6, "value": -4.11, "curve": [6.2, -4.11, 6.43, -5.98]}, {"time": 6.6667, "value": -5.85, "curve": [6.889, -5.73, 7.12, -0.26]}, {"time": 7.3333, "value": 0.09, "curve": [7.514, 0.37, 7.686, 3.12]}, {"time": 7.8333, "value": 2.82, "curve": [8.298, 1.92, 8.628, -2.28]}, {"time": 8.6667, "value": -2.28}]}, "toufa6": {"rotate": [{"value": -3.75, "curve": [0.294, -2.91, 0.513, -1.89]}, {"time": 0.5667, "value": -1.72, "curve": [0.693, -1.33, 1.298, -4.69]}, {"time": 1.9, "value": -4.86, "curve": [2.171, -4.93, 2.411, -4.48]}, {"time": 2.6667, "value": -3.75, "curve": [2.96, -2.91, 3.18, -1.89]}, {"time": 3.2333, "value": -1.72, "curve": [3.36, -1.33, 3.965, -4.69]}, {"time": 4.5667, "value": -4.86, "curve": [4.838, -4.93, 5.078, -4.48]}, {"time": 5.3333, "value": -3.75, "curve": [5.556, -3.12, 5.618, -5.63]}, {"time": 6, "value": -5.63, "curve": [6.203, -5.63, 6.436, -7.5]}, {"time": 6.6667, "value": -7.37, "curve": [6.889, -7.26, 7.117, -1.79]}, {"time": 7.3333, "value": -1.46, "curve": [7.513, -1.19, 7.685, 1.61]}, {"time": 7.8333, "value": 1.35, "curve": [8.324, 0.49, 8.664, -3.75]}, {"time": 8.6667, "value": -3.75}]}, "toufa7": {"rotate": [{"value": -6.86, "curve": [0.357, -4.97, 0.624, -2.32]}, {"time": 0.6667, "value": -2.21, "curve": [0.818, -1.81, 1.416, -8.59]}, {"time": 2, "value": -8.73, "curve": [2.235, -8.78, 2.444, -8.03]}, {"time": 2.6667, "value": -6.86, "curve": [3.024, -4.97, 3.291, -2.32]}, {"time": 3.3333, "value": -2.21, "curve": [3.485, -1.81, 4.083, -8.59]}, {"time": 4.6667, "value": -8.73, "curve": [4.901, -8.78, 5.111, -8.03]}, {"time": 5.3333, "value": -6.86, "curve": [5.556, -5.68, 5.618, -8.71]}, {"time": 6, "value": -8.7, "curve": [6.203, -8.7, 6.436, -10.58]}, {"time": 6.6667, "value": -10.45, "curve": [6.889, -10.34, 7.117, -4.88]}, {"time": 7.3333, "value": -4.54, "curve": [7.513, -4.27, 7.685, -1.49]}, {"time": 7.8333, "value": -1.75, "curve": [8.324, -2.61, 8.664, -6.86]}, {"time": 8.6667, "value": -6.86}]}, "toufa8": {"rotate": [{"value": -13.6, "curve": [0.454, -9.85, 0.808, -2.67]}, {"time": 0.8333, "value": -2.63, "curve": [1.013, -2.34, 1.598, -15.75]}, {"time": 2.1667, "value": -15.83, "curve": [2.34, -15.85, 2.5, -14.98]}, {"time": 2.6667, "value": -13.6, "curve": [3.121, -9.85, 3.475, -2.67]}, {"time": 3.5, "value": -2.63, "curve": [3.68, -2.34, 4.265, -15.75]}, {"time": 4.8333, "value": -15.83, "curve": [5.007, -15.85, 5.167, -14.98]}, {"time": 5.3333, "value": -13.6, "curve": [5.553, -11.79, 5.618, -15.42]}, {"time": 6, "value": -15.41, "curve": [6.203, -15.41, 6.436, -17.28]}, {"time": 6.6667, "value": -17.16, "curve": [6.889, -17.05, 7.117, -11.59]}, {"time": 7.3333, "value": -11.26, "curve": [7.513, -10.99, 7.685, -8.21]}, {"time": 7.8333, "value": -8.48, "curve": [8.324, -9.35, 8.664, -13.6]}, {"time": 8.6667, "value": -13.6}]}, "toufa9": {"rotate": [{"curve": [0.224, 0, 0.779, 5.73]}, {"time": 1.3333, "value": 5.73, "curve": [1.89, 5.73, 2.222, 0]}, {"time": 2.6667, "curve": [2.89, 0, 3.446, 5.73]}, {"time": 4, "value": 5.73, "curve": [4.556, 5.73, 4.889, 0]}, {"time": 5.3333, "curve": [5.444, 0, 5.556, -1.71]}, {"time": 5.6667, "value": -0.85, "curve": [5.744, -0.26, 6.188, 2.12]}, {"time": 6.3333, "value": 2.12, "curve": [6.468, 2.12, 6.602, 0.71]}, {"time": 6.7333, "value": 0.4, "curve": [6.88, 0.06, 7.023, -1.01]}, {"time": 7.1667, "value": -1.01, "curve": [7.222, -1.01, 7.667, 1.79]}, {"time": 7.8333, "value": 1.79, "curve": [8, 1.79, 8.167, 2.16]}, {"time": 8.3333, "value": 2.16, "curve": [8.444, 2.16, 8.556, 0]}, {"time": 8.6667}]}, "toufa10": {"rotate": [{"value": 0.3, "curve": [0.054, 0.13, 0.098, 0]}, {"time": 0.1333, "curve": [0.357, 0, 0.913, 4.12]}, {"time": 1.4667, "value": 4.12, "curve": [1.934, 4.12, 2.267, 1.62]}, {"time": 2.6667, "value": 0.3, "curve": [2.72, 0.13, 2.764, 0]}, {"time": 2.8, "curve": [3.024, 0, 3.579, 4.12]}, {"time": 4.1333, "value": 4.12, "curve": [4.601, 4.12, 4.933, 1.62]}, {"time": 5.3333, "value": 0.3, "curve": [5.444, -0.06, 5.556, -1.75]}, {"time": 5.6667, "value": -0.91, "curve": [5.744, -0.33, 6.188, 2.12]}, {"time": 6.3333, "value": 2.12, "curve": [6.468, 2.12, 6.602, 0.71]}, {"time": 6.7333, "value": 0.4, "curve": [6.88, 0.06, 7.023, -1.01]}, {"time": 7.1667, "value": -1.01, "curve": [7.222, -1.01, 7.667, 1.79]}, {"time": 7.8333, "value": 1.79, "curve": [8, 1.79, 8.167, 2.16]}, {"time": 8.3333, "value": 2.16, "curve": [8.444, 2.16, 8.556, 0.44]}, {"time": 8.6667, "value": 0.3}]}, "toufa11": {"rotate": [{"value": 1.06, "curve": [0.044, 0.73, 0.692, 0.91]}, {"time": 1.3333, "value": 1.34, "curve": [1.916, 1.71, 2.631, 1.34]}, {"time": 2.6667, "value": 1.06, "curve": [2.71, 0.73, 3.359, 0.91]}, {"time": 4, "value": 1.34, "curve": [4.582, 1.71, 5.157, 2.42]}, {"time": 5.3333, "value": 1.06, "curve": [5.444, 0.21, 5.466, -6.7]}, {"time": 5.6667, "value": -6.72, "curve": [5.843, -6.73, 6.077, 10.76]}, {"time": 6.3333, "value": 10.98, "curve": [6.441, 11.07, 6.554, 11.5]}, {"time": 6.6667, "value": 10.81, "curve": [6.778, 10.14, 6.889, 7.8]}, {"time": 7, "value": 8.38, "curve": [7.049, 8.64, 7.446, 8.59]}, {"time": 7.8333, "value": 6.92, "curve": [8.014, 6.18, 8.19, 3.99]}, {"time": 8.3333, "value": 3.29, "curve": [8.53, 2.35, 8.665, 1.06]}, {"time": 8.6667, "value": 1.06}]}, "toufa12": {"rotate": [{"value": 3.93, "curve": [0.081, 2.8, 0.712, 3.39]}, {"time": 1.3333, "value": 4.23, "curve": [1.928, 5.01, 2.645, 4.23]}, {"time": 2.6667, "value": 3.93, "curve": [2.748, 2.8, 3.379, 3.39]}, {"time": 4, "value": 4.23, "curve": [4.594, 5.01, 5.179, 6.1]}, {"time": 5.3333, "value": 3.93, "curve": [5.444, 2.38, 5.464, -3.78]}, {"time": 5.6667, "value": -3.79, "curve": [5.846, -3.79, 6.081, 13.7]}, {"time": 6.3333, "value": 13.92, "curve": [6.441, 14.01, 6.554, 14.44]}, {"time": 6.6667, "value": 13.75, "curve": [6.778, 13.07, 6.889, 10.72]}, {"time": 7, "value": 11.31, "curve": [7.049, 11.56, 7.443, 11.5]}, {"time": 7.8333, "value": 9.83, "curve": [8.012, 9.08, 8.189, 6.9]}, {"time": 8.3333, "value": 6.18, "curve": [8.532, 5.24, 8.667, 3.93]}, {"time": 8.6667, "value": 3.93}]}, "toufa13": {"rotate": [{"value": 7.75, "curve": [0.11, 5.73, 0.722, 6.41]}, {"time": 1.3333, "value": 7.56, "curve": [1.932, 8.67, 2.656, 7.94]}, {"time": 2.6667, "value": 7.75, "curve": [2.777, 5.73, 3.389, 6.41]}, {"time": 4, "value": 7.56, "curve": [4.598, 8.67, 5.196, 10.26]}, {"time": 5.3333, "value": 7.75, "curve": [5.444, 5.71, 5.465, 0.01]}, {"time": 5.6667, "curve": [5.846, -0.01, 6.082, 17.48]}, {"time": 6.3333, "value": 17.7, "curve": [6.442, 17.79, 6.554, 18.23]}, {"time": 6.6667, "value": 17.53, "curve": [6.778, 16.85, 6.89, 14.5]}, {"time": 7, "value": 15.09, "curve": [7.049, 15.35, 7.443, 15.29]}, {"time": 7.8333, "value": 13.64, "curve": [8.012, 12.88, 8.19, 10.71]}, {"time": 8.3333, "value": 9.99, "curve": [8.532, 9.05, 8.667, 7.75]}, {"time": 8.6667, "value": 7.75}]}, "toufa14": {"rotate": [{"value": 20.53, "curve": [0.145, 15.95, 0.742, 16.94]}, {"time": 1.3333, "value": 18.94, "curve": [1.947, 21, 2.616, 22.11]}, {"time": 2.6667, "value": 20.53, "curve": [2.812, 15.95, 3.409, 16.94]}, {"time": 4, "value": 18.94, "curve": [4.613, 21, 5.221, 24.08]}, {"time": 5.3333, "value": 20.53, "curve": [5.444, 17.02, 5.465, 12.76]}, {"time": 5.6667, "value": 12.74, "curve": [5.846, 12.72, 6.082, 30.21]}, {"time": 6.3333, "value": 30.43, "curve": [6.442, 30.52, 6.554, 30.96]}, {"time": 6.6667, "value": 30.26, "curve": [6.778, 29.59, 6.89, 27.24]}, {"time": 7, "value": 27.83, "curve": [7.049, 28.09, 7.443, 28.04]}, {"time": 7.8333, "value": 26.39, "curve": [8.012, 25.64, 8.19, 23.47]}, {"time": 8.3333, "value": 22.76, "curve": [8.532, 21.82, 8.667, 20.53]}, {"time": 8.6667, "value": 20.53}]}, "toufa15": {"rotate": [{"curve": [0.224, 0, 0.779, 1.94]}, {"time": 1.3333, "value": 1.94, "curve": [1.89, 1.94, 2.222, 0]}, {"time": 2.6667, "curve": [2.89, 0, 3.446, 1.94]}, {"time": 4, "value": 1.94, "curve": [4.556, 1.94, 4.889, 0]}, {"time": 5.3333, "curve": [5.444, 0, 5.556, -3.55]}, {"time": 5.6667, "value": -2.69, "curve": [5.744, -2.1, 6.188, 2.12]}, {"time": 6.3333, "value": 2.12, "curve": [6.445, 2.12, 6.557, 1.93]}, {"time": 6.6667, "value": 1.67, "curve": [6.779, 1.41, 6.89, -1.55]}, {"time": 7, "value": -1.55, "curve": [7.034, -1.55, 7.792, 3.16]}, {"time": 7.8333, "value": 3.16, "curve": [7.927, 3.16, 8.145, 1.82]}, {"time": 8.3333, "value": 1.33, "curve": [8.485, 0.94, 8.618, 0]}, {"time": 8.6667}]}, "toufa16": {"rotate": [{"value": 0.5, "curve": [0.068, 0.2, 0.124, 0]}, {"time": 0.1667, "curve": [0.39, 0, 0.946, 4.99]}, {"time": 1.5, "value": 4.99, "curve": [1.949, 4.99, 2.278, 2.22]}, {"time": 2.6667, "value": 0.5, "curve": [2.735, 0.2, 2.79, 0]}, {"time": 2.8333, "curve": [3.057, 0, 3.613, 4.99]}, {"time": 4.1667, "value": 4.99, "curve": [4.615, 4.99, 4.944, 2.22]}, {"time": 5.3333, "value": 0.5, "curve": [5.444, 0.01, 5.556, -3.52]}, {"time": 5.6667, "value": -2.72, "curve": [5.744, -2.17, 6.188, 2.12]}, {"time": 6.3333, "value": 2.12, "curve": [6.445, 2.12, 6.557, 1.93]}, {"time": 6.6667, "value": 1.67, "curve": [6.779, 1.41, 6.89, -1.55]}, {"time": 7, "value": -1.55, "curve": [7.034, -1.55, 7.792, 3.16]}, {"time": 7.8333, "value": 3.16, "curve": [7.927, 3.16, 8.145, 2.02]}, {"time": 8.3333, "value": 1.68, "curve": [8.485, 1.41, 8.618, 0.56]}, {"time": 8.6667, "value": 0.5}]}, "toufa17": {"rotate": [{"value": 1.95, "curve": [0.142, 0.85, 0.257, 0]}, {"time": 0.3333, "curve": [0.557, 0, 1.113, 7.14]}, {"time": 1.6667, "value": 7.14, "curve": [2.032, 7.14, 2.333, 4.56]}, {"time": 2.6667, "value": 1.95, "curve": [2.808, 0.85, 2.924, 0]}, {"time": 3, "curve": [3.224, 0, 3.779, 7.14]}, {"time": 4.3333, "value": 7.14, "curve": [4.699, 7.14, 5, 4.56]}, {"time": 5.3333, "value": 1.95, "curve": [5.444, 1.08, 5.472, -0.89]}, {"time": 5.6667, "value": -1.48, "curve": [5.765, -1.77, 5.877, -0.16]}, {"time": 6, "value": -0.16, "curve": [6.201, -0.17, 6.431, -0.76]}, {"time": 6.6667, "value": -0.62, "curve": [6.777, -0.55, 6.889, 0.63]}, {"time": 7, "value": 2.39, "curve": [7.084, 3.71, 7.748, 8.47]}, {"time": 7.8333, "value": 8.32, "curve": [7.919, 8.16, 8.128, 6.94]}, {"time": 8.3333, "value": 5.28, "curve": [8.497, 3.96, 8.656, 1.95]}, {"time": 8.6667, "value": 1.95}]}, "toufa18": {"rotate": [{"value": 5.6, "curve": [0.218, 2.72, 0.396, 0]}, {"time": 0.5, "curve": [0.724, 0, 1.279, 12.32]}, {"time": 1.8333, "value": 12.32, "curve": [2.128, 12.32, 2.389, 9.29]}, {"time": 2.6667, "value": 5.6, "curve": [2.884, 2.72, 3.062, 0]}, {"time": 3.1667, "curve": [3.39, 0, 3.946, 12.32]}, {"time": 4.5, "value": 12.32, "curve": [4.795, 12.32, 5.056, 9.29]}, {"time": 5.3333, "value": 5.6, "curve": [5.444, 4.13, 5.5, 1.26]}, {"time": 5.6667, "value": -1.05, "curve": [5.806, -2.91, 5.945, -2.82]}, {"time": 6, "value": -2.93, "curve": [6.202, -3.3, 6.434, 7.61]}, {"time": 6.6667, "value": 7.66, "curve": [6.777, 7.69, 6.889, -4.59]}, {"time": 7, "value": -3.47, "curve": [7.085, -2.62, 7.76, 7.62]}, {"time": 7.8333, "value": 6.91, "curve": [7.92, 6.07, 8.131, 5.47]}, {"time": 8.3333, "value": 5.66, "curve": [8.504, 5.81, 8.667, 5.6]}, {"time": 8.6667, "value": 5.6}]}, "toufa19": {"rotate": [{"value": 12.85, "curve": [0.294, 7.04, 0.537, 0]}, {"time": 0.6667, "curve": [0.89, 0, 1.446, 20.5]}, {"time": 2, "value": 20.5, "curve": [2.232, 20.5, 2.444, 17.24]}, {"time": 2.6667, "value": 12.85, "curve": [2.961, 7.04, 3.204, 0]}, {"time": 3.3333, "curve": [3.557, 0, 4.113, 20.5]}, {"time": 4.6667, "value": 20.5, "curve": [4.898, 20.5, 5.111, 17.24]}, {"time": 5.3333, "value": 12.85, "curve": [5.444, 10.65, 5.5, 8.5]}, {"time": 5.6667, "value": 6.19, "curve": [5.806, 4.33, 5.945, 4.42]}, {"time": 6, "value": 4.31, "curve": [6.202, 3.94, 6.434, 14.85]}, {"time": 6.6667, "value": 14.9, "curve": [6.777, 14.93, 6.889, 2.65]}, {"time": 7, "value": 3.77, "curve": [7.085, 4.62, 7.76, 14.86]}, {"time": 7.8333, "value": 14.15, "curve": [7.92, 13.31, 8.131, 12.72]}, {"time": 8.3333, "value": 12.9, "curve": [8.504, 13.05, 8.667, 12.85]}, {"time": 8.6667, "value": 12.85}]}, "toufa20": {"rotate": [{"value": 21.66, "curve": [0.365, 13.43, 0.679, 0]}, {"time": 0.8333, "curve": [1.057, 0, 1.613, 27.9]}, {"time": 2.1667, "value": 27.9, "curve": [2.337, 27.9, 2.5, 25.41]}, {"time": 2.6667, "value": 21.66, "curve": [3.032, 13.43, 3.346, 0]}, {"time": 3.5, "curve": [3.724, 0, 4.279, 27.9]}, {"time": 4.8333, "value": 27.9, "curve": [5.004, 27.9, 5.167, 25.41]}, {"time": 5.3333, "value": 21.66, "curve": [5.444, 19.16, 5.5, 17.31]}, {"time": 5.6667, "value": 15, "curve": [5.806, 13.14, 5.945, 13.22]}, {"time": 6, "value": 13.12, "curve": [6.202, 12.74, 6.434, 23.66]}, {"time": 6.6667, "value": 23.71, "curve": [6.777, 23.73, 6.889, 11.46]}, {"time": 7, "value": 12.57, "curve": [7.085, 13.42, 7.76, 23.67]}, {"time": 7.8333, "value": 22.96, "curve": [7.92, 22.12, 8.131, 21.53]}, {"time": 8.3333, "value": 21.71, "curve": [8.504, 21.86, 8.667, 21.66]}, {"time": 8.6667, "value": 21.66}]}, "toufa21": {"rotate": [{"curve": [0.224, 0, 0.779, 2.79]}, {"time": 1.3333, "value": 2.79, "curve": [1.89, 2.79, 2.222, 0]}, {"time": 2.6667, "curve": [2.89, 0, 3.446, 2.79]}, {"time": 4, "value": 2.79, "curve": [4.556, 2.79, 4.889, 0]}, {"time": 5.3333, "curve": [5.444, 0, 5.556, -3.55]}, {"time": 5.6667, "value": -2.69, "curve": [5.744, -2.1, 6.188, 2.12]}, {"time": 6.3333, "value": 2.12, "curve": [6.445, 2.12, 6.557, 1.93]}, {"time": 6.6667, "value": 1.67, "curve": [6.779, 1.41, 6.89, -1.55]}, {"time": 7, "value": -1.55, "curve": [7.034, -1.55, 7.792, 3.16]}, {"time": 7.8333, "value": 3.16, "curve": [7.927, 3.16, 8.145, 1.82]}, {"time": 8.3333, "value": 1.33, "curve": [8.485, 0.94, 8.618, 0]}, {"time": 8.6667}]}, "toufa22": {"rotate": [{"value": 0.32, "curve": [0.054, 0.14, 0.098, 0]}, {"time": 0.1333, "curve": [0.357, 0, 0.913, 4.37]}, {"time": 1.4667, "value": 4.37, "curve": [1.934, 4.37, 2.267, 1.72]}, {"time": 2.6667, "value": 0.32, "curve": [2.72, 0.14, 2.764, 0]}, {"time": 2.8, "curve": [3.024, 0, 3.579, 4.37]}, {"time": 4.1333, "value": 4.37, "curve": [4.601, 4.37, 4.933, 1.72]}, {"time": 5.3333, "value": 0.32, "curve": [5.444, -0.07, 5.556, -3.59]}, {"time": 5.6667, "value": -2.76, "curve": [5.744, -2.18, 6.188, 2.12]}, {"time": 6.3333, "value": 2.12, "curve": [6.445, 2.12, 6.557, 1.93]}, {"time": 6.6667, "value": 1.67, "curve": [6.779, 1.41, 6.89, -1.55]}, {"time": 7, "value": -1.55, "curve": [7.034, -1.55, 7.792, 3.16]}, {"time": 7.8333, "value": 3.16, "curve": [7.927, 3.16, 8.145, 1.97]}, {"time": 8.3333, "value": 1.57, "curve": [8.485, 1.27, 8.618, 0.38]}, {"time": 8.6667, "value": 0.32}]}, "toufa23": {"rotate": [{"value": 1.07, "curve": [0.128, 0.46, 0.23, 0]}, {"time": 0.3, "curve": [0.524, 0, 1.079, 4.53]}, {"time": 1.6333, "value": 4.53, "curve": [2.015, 4.53, 2.322, 2.71]}, {"time": 2.6667, "value": 1.07, "curve": [2.795, 0.46, 2.897, 0]}, {"time": 2.9667, "curve": [3.19, 0, 3.746, 4.53]}, {"time": 4.3, "value": 4.53, "curve": [4.682, 4.53, 4.989, 2.71]}, {"time": 5.3333, "value": 1.07, "curve": [5.444, 0.54, 5.478, -1.77]}, {"time": 5.6667, "value": -2.37, "curve": [5.763, -2.67, 5.876, -1.07]}, {"time": 6, "value": -1.08, "curve": [6.2, -1.09, 6.43, -1.68]}, {"time": 6.6667, "value": -1.53, "curve": [6.777, -1.46, 6.89, -0.31]}, {"time": 7, "value": 1.43, "curve": [7.086, 2.76, 7.748, 7.58]}, {"time": 7.8333, "value": 7.43, "curve": [7.919, 7.27, 8.129, 6.06]}, {"time": 8.3333, "value": 4.39, "curve": [8.497, 3.08, 8.656, 1.07]}, {"time": 8.6667, "value": 1.07}]}, "toufa24": {"rotate": [{"value": 3.11, "curve": [0.218, 1.51, 0.396, 0]}, {"time": 0.5, "curve": [0.724, 0, 1.279, 6.84]}, {"time": 1.8333, "value": 6.84, "curve": [2.128, 6.84, 2.389, 5.15]}, {"time": 2.6667, "value": 3.11, "curve": [2.884, 1.51, 3.062, 0]}, {"time": 3.1667, "curve": [3.39, 0, 3.946, 6.84]}, {"time": 4.5, "value": 6.84, "curve": [4.795, 6.84, 5.056, 5.15]}, {"time": 5.3333, "value": 3.11, "curve": [5.444, 2.29, 5.471, 0.29]}, {"time": 5.6667, "value": -0.3, "curve": [5.764, -0.59, 5.876, 1.03]}, {"time": 6, "value": 1.02, "curve": [6.2, 1.02, 6.43, 0.42]}, {"time": 6.6667, "value": 0.57, "curve": [6.777, 0.63, 6.889, 1.82]}, {"time": 7, "value": 3.58, "curve": [7.084, 4.89, 7.747, 9.64]}, {"time": 7.8333, "value": 9.48, "curve": [7.919, 9.33, 8.128, 8.11]}, {"time": 8.3333, "value": 6.44, "curve": [8.497, 5.12, 8.656, 3.11]}, {"time": 8.6667, "value": 3.11}]}, "toufa25": {"rotate": [{"value": 6.66, "curve": [0.277, 3.55, 0.508, 0]}, {"time": 0.6333, "curve": [0.857, 0, 1.413, 11.23]}, {"time": 1.9667, "value": 11.23, "curve": [2.21, 11.23, 2.433, 9.28]}, {"time": 2.6667, "value": 6.66, "curve": [2.944, 3.55, 3.175, 0]}, {"time": 3.3, "curve": [3.524, 0, 4.079, 11.23]}, {"time": 4.6333, "value": 11.23, "curve": [4.876, 11.23, 5.1, 9.28]}, {"time": 5.3333, "value": 6.66, "curve": [5.556, 4.17, 5.876, -1.66]}, {"time": 6, "value": -1.89, "curve": [6.201, -2.27, 6.433, 7.38]}, {"time": 6.6667, "value": 7.43, "curve": [6.832, 7.47, 7.002, -3.56]}, {"time": 7.1667, "value": -1.88, "curve": [7.34, -0.16, 7.509, 1.25]}, {"time": 7.6667, "value": 1.19, "curve": [7.796, 1.13, 7.913, 7.42]}, {"time": 8, "value": 6.56, "curve": [8.409, 2.58, 8.662, 6.66]}, {"time": 8.6667, "value": 6.66}]}, "toufa26": {"rotate": [{"value": 11.13, "curve": [0.27, 6.2, 0.535, 0]}, {"time": 0.8, "curve": [1.244, 0, 1.689, 17.17]}, {"time": 2.1333, "value": 17.17, "curve": [2.313, 17.17, 2.489, 14.38]}, {"time": 2.6667, "value": 11.13, "curve": [2.936, 6.2, 3.201, 0]}, {"time": 3.4667, "curve": [3.911, 0, 4.356, 17.17]}, {"time": 4.8, "value": 17.17, "curve": [4.979, 17.17, 5.156, 14.38]}, {"time": 5.3333, "value": 11.13, "curve": [5.556, 7.06, 5.876, 2.83]}, {"time": 6, "value": 2.59, "curve": [6.202, 2.22, 6.434, 11.86]}, {"time": 6.6667, "value": 11.91, "curve": [6.832, 11.95, 7.001, 0.91]}, {"time": 7.1667, "value": 2.58, "curve": [7.339, 4.32, 7.509, 5.73]}, {"time": 7.6667, "value": 5.66, "curve": [7.796, 5.61, 7.913, 11.91]}, {"time": 8, "value": 11.06, "curve": [8.412, 7.05, 8.666, 11.13]}, {"time": 8.6667, "value": 11.13}]}, "toufa27": {"rotate": [{"curve": [0.224, 0, 0.779, 5.48]}, {"time": 1.3333, "value": 5.48, "curve": [1.89, 5.48, 2.222, 0]}, {"time": 2.6667, "curve": [2.89, 0, 3.446, 5.48]}, {"time": 4, "value": 5.48, "curve": [4.556, 5.48, 4.889, 0]}, {"time": 5.3333, "curve": [5.444, 0, 5.478, -2.67]}, {"time": 5.6667, "value": -3.24, "curve": [5.763, -3.53, 5.876, -1.91]}, {"time": 6, "value": -1.91, "curve": [6.2, -1.91, 6.43, -2.51]}, {"time": 6.6667, "value": -2.37, "curve": [6.777, -2.31, 6.89, -1.17]}, {"time": 7, "value": 0.57, "curve": [7.086, 1.89, 7.748, 6.61]}, {"time": 7.8333, "value": 6.45, "curve": [7.919, 6.28, 8.129, 5.06]}, {"time": 8.3333, "value": 3.37, "curve": [8.497, 2.03, 8.656, 0]}, {"time": 8.6667}]}, "toufa28": {"rotate": [{"value": 0.67, "curve": [0.054, 0.28, 0.098, 0]}, {"time": 0.1333, "curve": [0.357, 0, 0.913, 9.06]}, {"time": 1.4667, "value": 9.06, "curve": [1.934, 9.06, 2.267, 3.57]}, {"time": 2.6667, "value": 0.67, "curve": [2.72, 0.28, 2.764, 0]}, {"time": 2.8, "curve": [3.024, 0, 3.579, 9.06]}, {"time": 4.1333, "value": 9.06, "curve": [4.601, 9.06, 4.933, 3.57]}, {"time": 5.3333, "value": 0.67, "curve": [5.556, -0.94, 5.877, -7.64]}, {"time": 6, "value": -7.87, "curve": [6.202, -8.25, 6.435, 1.39]}, {"time": 6.6667, "value": 1.45, "curve": [6.832, 1.48, 7.001, -9.56]}, {"time": 7.1667, "value": -7.89, "curve": [7.34, -6.15, 7.509, -4.73]}, {"time": 7.6667, "value": -4.8, "curve": [7.796, -4.85, 7.913, 1.45]}, {"time": 8, "value": 0.6, "curve": [8.412, -3.41, 8.666, 0.67]}, {"time": 8.6667, "value": 0.67}]}, "toufa29": {"rotate": [{"value": 3.36, "curve": [0.157, 1.49, 0.284, 0]}, {"time": 0.3667, "curve": [0.59, 0, 1.146, 10.85]}, {"time": 1.7, "value": 10.85, "curve": [2.05, 10.85, 2.344, 7.21]}, {"time": 2.6667, "value": 3.36, "curve": [2.823, 1.49, 2.951, 0]}, {"time": 3.0333, "curve": [3.257, 0, 3.813, 10.85]}, {"time": 4.3667, "value": 10.85, "curve": [4.717, 10.85, 5.011, 7.21]}, {"time": 5.3333, "value": 3.36, "curve": [5.556, 0.71, 5.876, -4.93]}, {"time": 6, "value": -5.16, "curve": [6.202, -5.54, 6.434, 4.11]}, {"time": 6.6667, "value": 4.16, "curve": [6.833, 4.19, 7.003, -6.85]}, {"time": 7.1667, "value": -5.17, "curve": [7.342, -3.45, 7.51, -2.03]}, {"time": 7.6667, "value": -2.1, "curve": [7.796, -2.15, 7.912, 4.15]}, {"time": 8, "value": 3.3, "curve": [8.412, -0.71, 8.666, 3.36]}, {"time": 8.6667, "value": 3.36}]}, "toufa30": {"rotate": [{"value": 9.1, "curve": [0.218, 4.42, 0.396, 0]}, {"time": 0.5, "curve": [0.724, 0, 1.279, 19.99]}, {"time": 1.8333, "value": 19.99, "curve": [2.128, 19.99, 2.389, 15.07]}, {"time": 2.6667, "value": 9.1, "curve": [2.884, 4.42, 3.062, 0]}, {"time": 3.1667, "curve": [3.39, 0, 3.946, 19.99]}, {"time": 4.5, "value": 19.99, "curve": [4.795, 19.99, 5.056, 15.07]}, {"time": 5.3333, "value": 9.1, "curve": [5.556, 4.32, 5.876, 0.79]}, {"time": 6, "value": 0.56, "curve": [6.202, 0.18, 6.434, 9.83]}, {"time": 6.6667, "value": 9.88, "curve": [6.832, 9.91, 7.001, -1.12]}, {"time": 7.1667, "value": 0.55, "curve": [7.339, 2.29, 7.509, 3.7]}, {"time": 7.6667, "value": 3.63, "curve": [7.796, 3.58, 7.913, 9.88]}, {"time": 8, "value": 9.03, "curve": [8.412, 5.02, 8.666, 9.1]}, {"time": 8.6667, "value": 9.1}]}, "toufa31": {"rotate": [{"curve": [0.224, 0, 0.779, -1.21]}, {"time": 1.3333, "value": -1.21, "curve": [1.89, -1.21, 2.222, 0]}, {"time": 2.6667, "curve": [2.89, 0, 3.446, -1.21]}, {"time": 4, "value": -1.21, "curve": [4.556, -1.21, 4.889, 0]}, {"time": 5.3333, "curve": [5.556, 0, 5.631, -1.9]}, {"time": 6, "value": -1.89, "curve": [6.2, -1.89, 6.43, -3.75]}, {"time": 6.6667, "value": -3.61, "curve": [6.889, -3.48, 7.12, 2]}, {"time": 7.3333, "value": 2.36, "curve": [7.574, 2.75, 7.804, 5.51]}, {"time": 8, "value": 5.13, "curve": [8.372, 4.42, 8.636, 0.03]}, {"time": 8.6667}]}, "toufa32": {"rotate": [{"value": -0.15, "curve": [0.068, -0.06, 0.124, 0]}, {"time": 0.1667, "curve": [0.39, 0, 0.946, -1.48]}, {"time": 1.5, "value": -1.48, "curve": [1.949, -1.48, 2.278, -0.66]}, {"time": 2.6667, "value": -0.15, "curve": [2.735, -0.06, 2.79, 0]}, {"time": 2.8333, "curve": [3.057, 0, 3.613, -1.48]}, {"time": 4.1667, "value": -1.48, "curve": [4.615, -1.48, 4.944, -0.66]}, {"time": 5.3333, "value": -0.15, "curve": [5.556, 0.14, 5.631, -2]}, {"time": 6, "value": -2, "curve": [6.2, -1.99, 6.43, -3.86]}, {"time": 6.6667, "value": -3.73, "curve": [6.889, -3.61, 7.12, 1.86]}, {"time": 7.3333, "value": 2.21, "curve": [7.574, 2.59, 7.804, 5.34]}, {"time": 8, "value": 4.95, "curve": [8.372, 4.23, 8.636, -0.15]}, {"time": 8.6667, "value": -0.15}]}, "toufa33": {"rotate": [{"value": -0.39, "curve": [0.142, -0.17, 0.257, 0]}, {"time": 0.3333, "curve": [0.557, 0, 1.113, -1.42]}, {"time": 1.6667, "value": -1.42, "curve": [2.032, -1.42, 2.333, -0.91]}, {"time": 2.6667, "value": -0.39, "curve": [2.808, -0.17, 2.924, 0]}, {"time": 3, "curve": [3.224, 0, 3.779, -1.42]}, {"time": 4.3333, "value": -1.42, "curve": [4.699, -1.42, 5, -0.91]}, {"time": 5.3333, "value": -0.39, "curve": [5.556, -0.04, 5.631, -2.23]}, {"time": 6, "value": -2.23, "curve": [6.2, -2.22, 6.43, -4.09]}, {"time": 6.6667, "value": -3.96, "curve": [6.889, -3.84, 7.12, 1.63]}, {"time": 7.3333, "value": 1.98, "curve": [7.574, 2.35, 7.804, 5.1]}, {"time": 8, "value": 4.72, "curve": [8.372, 3.99, 8.636, -0.39]}, {"time": 8.6667, "value": -0.39}]}, "toufa34": {"rotate": [{"value": -0.98, "curve": [0.218, -0.48, 0.396, 0]}, {"time": 0.5, "curve": [0.724, 0, 1.279, -2.16]}, {"time": 1.8333, "value": -2.16, "curve": [2.128, -2.16, 2.389, -1.63]}, {"time": 2.6667, "value": -0.98, "curve": [2.884, -0.48, 3.062, 0]}, {"time": 3.1667, "curve": [3.39, 0, 3.946, -2.16]}, {"time": 4.5, "value": -2.16, "curve": [4.795, -2.16, 5.056, -1.63]}, {"time": 5.3333, "value": -0.98, "curve": [5.556, -0.47, 5.63, 2.14]}, {"time": 6, "value": 2.14, "curve": [6.105, 2.14, 6.554, 3.5]}, {"time": 6.6667, "value": 3.56, "curve": [6.887, 3.68, 7.115, -5.9]}, {"time": 7.3333, "value": -5.56, "curve": [7.448, -5.38, 7.561, -11.07]}, {"time": 7.6667, "value": -8.75, "curve": [7.786, -6.22, 7.897, -8.11]}, {"time": 8, "value": -8.32, "curve": [8.059, -8.43, 8.641, -0.98]}, {"time": 8.6667, "value": -0.98}]}, "toufa35": {"rotate": [{"value": -1.82, "curve": [0.294, -1, 0.537, 0]}, {"time": 0.6667, "curve": [0.89, 0, 1.446, -2.91]}, {"time": 2, "value": -2.91, "curve": [2.232, -2.91, 2.444, -2.45]}, {"time": 2.6667, "value": -1.82, "curve": [2.961, -1, 3.204, 0]}, {"time": 3.3333, "curve": [3.557, 0, 4.113, -2.91]}, {"time": 4.6667, "value": -2.91, "curve": [4.898, -2.91, 5.111, -2.45]}, {"time": 5.3333, "value": -1.82, "curve": [5.444, -1.51, 5.478, 3.74]}, {"time": 5.6667, "value": 3.19, "curve": [5.763, 2.91, 5.876, 1.34]}, {"time": 6, "value": 1.35, "curve": [6.105, 1.35, 6.554, 2.71]}, {"time": 6.6667, "value": 2.77, "curve": [6.889, 2.88, 7.12, -6.71]}, {"time": 7.3333, "value": -6.36, "curve": [7.448, -6.19, 7.56, -11.89]}, {"time": 7.6667, "value": -9.57, "curve": [7.785, -7.04, 7.897, -8.93]}, {"time": 8, "value": -9.13, "curve": [8.06, -9.25, 8.641, -1.82]}, {"time": 8.6667, "value": -1.82}]}, "toufa36": {"rotate": [{"value": -2.43, "curve": [0.365, -1.51, 0.679, 0]}, {"time": 0.8333, "curve": [1.057, 0, 1.613, -3.14]}, {"time": 2.1667, "value": -3.14, "curve": [2.337, -3.14, 2.5, -2.86]}, {"time": 2.6667, "value": -2.43, "curve": [3.032, -1.51, 3.346, 0]}, {"time": 3.5, "curve": [3.724, 0, 4.279, -3.14]}, {"time": 4.8333, "value": -3.14, "curve": [5.004, -3.14, 5.167, -2.86]}, {"time": 5.3333, "value": -2.43, "curve": [5.444, -2.15, 5.466, -3.7]}, {"time": 5.6667, "value": -3.69, "curve": [5.763, -3.69, 5.877, 12.07]}, {"time": 6, "value": 13.8, "curve": [6.106, 15.23, 6.521, 23.56]}, {"time": 6.6667, "value": 21.31, "curve": [6.875, 18.2, 7.131, 12.36]}, {"time": 7.3333, "value": 6.87, "curve": [7.405, 4.99, 7.538, 4.09]}, {"time": 7.6667, "value": 1.22, "curve": [7.809, -1.89, 7.947, -10.51]}, {"time": 8, "value": -6.92, "curve": [8.06, -3.03, 8.659, -2.43]}, {"time": 8.6667, "value": -2.43}]}, "toufa37": {"rotate": [{"value": -6.14, "curve": [0.435, -4.36, 0.823, 0]}, {"time": 1, "curve": [1.224, 0, 1.779, -6.86]}, {"time": 2.3333, "value": -6.86, "curve": [2.446, -6.86, 2.556, -6.59]}, {"time": 2.6667, "value": -6.14, "curve": [3.102, -4.36, 3.49, 0]}, {"time": 3.6667, "curve": [3.89, 0, 4.446, -6.86]}, {"time": 5, "value": -6.86, "curve": [5.113, -6.86, 5.222, -6.59]}, {"time": 5.3333, "value": -6.14, "curve": [5.444, -5.68, 5.465, -7.41]}, {"time": 5.6667, "value": -7.41, "curve": [5.763, -7.41, 5.877, 8.33]}, {"time": 6, "value": 10.04, "curve": [6.105, 11.49, 6.522, 19.85]}, {"time": 6.6667, "value": 17.6, "curve": [6.875, 14.51, 7.129, 8.67]}, {"time": 7.3333, "value": 3.19, "curve": [7.405, 1.29, 7.538, 0.39]}, {"time": 7.6667, "value": -2.46, "curve": [7.809, -5.55, 7.946, -14.14]}, {"time": 8, "value": -10.54, "curve": [8.059, -6.65, 8.666, -6.14]}, {"time": 8.6667, "value": -6.14}]}, "toufa38": {"rotate": [{"value": -8.23, "curve": [0.462, -6.2, 0.881, 0]}, {"time": 1.0667, "curve": [1.29, 0, 1.846, -8.89]}, {"time": 2.4, "value": -8.89, "curve": [2.491, -8.89, 2.578, -8.62]}, {"time": 2.6667, "value": -8.23, "curve": [3.129, -6.2, 3.547, 0]}, {"time": 3.7333, "curve": [3.957, 0, 4.513, -8.89]}, {"time": 5.0667, "value": -8.89, "curve": [5.158, -8.89, 5.244, -8.62]}, {"time": 5.3333, "value": -8.23, "curve": [5.444, -7.74, 5.465, -9.51]}, {"time": 5.6667, "value": -9.51, "curve": [5.763, -9.51, 5.877, 6.24]}, {"time": 6, "value": 7.94, "curve": [6.105, 9.4, 6.522, 17.76]}, {"time": 6.6667, "value": 15.51, "curve": [6.875, 12.42, 7.129, 6.57]}, {"time": 7.3333, "value": 1.1, "curve": [7.405, -0.8, 7.538, -1.71]}, {"time": 7.6667, "value": -4.56, "curve": [7.809, -7.65, 7.946, -16.23]}, {"time": 8, "value": -12.63, "curve": [8.059, -8.74, 8.666, -8.23]}, {"time": 8.6667, "value": -8.23}]}, "toufa39": {"rotate": [{"value": -12.06, "curve": [0.501, -10, 0.967, 0]}, {"time": 1.1667, "curve": [1.39, 0, 1.946, -12.41]}, {"time": 2.5, "value": -12.41, "curve": [2.557, -12.41, 2.611, -12.29]}, {"time": 2.6667, "value": -12.06, "curve": [3.167, -10, 3.634, 0]}, {"time": 3.8333, "curve": [4.057, 0, 4.613, -12.41]}, {"time": 5.1667, "value": -12.41, "curve": [5.224, -12.41, 5.278, -12.29]}, {"time": 5.3333, "value": -12.06, "curve": [5.444, -11.6, 5.465, -19.63]}, {"time": 5.6667, "value": -19.63, "curve": [5.763, -19.63, 5.877, -7.02]}, {"time": 6, "value": -3.61, "curve": [6.105, -0.71, 6.218, 2.01]}, {"time": 6.3333, "value": 2.11, "curve": [6.547, 2.28, 7.459, -10.84]}, {"time": 7.6667, "value": -11.15, "curve": [7.851, -11.42, 8.021, -4.46]}, {"time": 8.1667, "value": -4.91, "curve": [8.469, -5.84, 8.666, -12.06]}, {"time": 8.6667, "value": -12.06}]}, "toufa40": {"rotate": [{"value": -14.57, "curve": [0.533, -13.48, 1.053, 0]}, {"time": 1.2667, "curve": [1.49, 0, 2.046, -14.73]}, {"time": 2.6, "value": -14.73, "curve": [2.622, -14.73, 2.644, -14.61]}, {"time": 2.6667, "value": -14.57, "curve": [3.2, -13.48, 3.72, 0]}, {"time": 3.9333, "curve": [4.157, 0, 4.713, -14.73]}, {"time": 5.2667, "value": -14.73, "curve": [5.289, -14.73, 5.311, -14.61]}, {"time": 5.3333, "value": -14.57, "curve": [5.444, -14.34, 5.465, -22.15]}, {"time": 5.6667, "value": -22.15, "curve": [5.763, -22.15, 5.877, -9.54]}, {"time": 6, "value": -6.13, "curve": [6.105, -3.23, 6.218, -0.51]}, {"time": 6.3333, "value": -0.41, "curve": [6.547, -0.24, 7.459, -13.36]}, {"time": 7.6667, "value": -13.67, "curve": [7.851, -13.94, 8.021, -6.97]}, {"time": 8.1667, "value": -7.42, "curve": [8.469, -8.35, 8.666, -14.57]}, {"time": 8.6667, "value": -14.57}]}, "toufa41": {"rotate": [{"curve": [0.224, 0, 0.779, -2.12]}, {"time": 1.3333, "value": -2.12, "curve": [1.89, -2.12, 2.222, 0]}, {"time": 2.6667, "curve": [2.89, 0, 3.446, -2.12]}, {"time": 4, "value": -2.12, "curve": [4.556, -2.12, 4.889, 0]}, {"time": 5.3333, "curve": [5.444, 0, 5.466, -7.58]}, {"time": 5.6667, "value": -7.58, "curve": [5.763, -7.58, 5.877, 5.08]}, {"time": 6, "value": 8.52, "curve": [6.106, 11.38, 6.217, 14.07]}, {"time": 6.3333, "value": 14.17, "curve": [6.545, 14.34, 7.457, 1.22]}, {"time": 7.6667, "value": 0.9, "curve": [7.852, 0.63, 8.023, 7.59]}, {"time": 8.1667, "value": 7.13, "curve": [8.464, 6.21, 8.659, 0.01]}, {"time": 8.6667}], "translate": [{"time": 5.3333, "curve": [6.111, 0, 6.726, 67.63, 6.111, 0, 6.726, -108.67]}, {"time": 7.6667, "x": 67.63, "y": -108.67, "curve": [8.168, 67.63, 8.571, 0, 8.168, -108.67, 8.571, 0]}, {"time": 8.6667}]}, "toufa42": {"rotate": [{"value": -0.33, "curve": [0.068, -0.13, 0.124, 0]}, {"time": 0.1667, "curve": [0.39, 0, 0.946, -3.31]}, {"time": 1.5, "value": -3.31, "curve": [1.949, -3.31, 2.278, -1.47]}, {"time": 2.6667, "value": -0.33, "curve": [2.735, -0.13, 2.79, 0]}, {"time": 2.8333, "curve": [3.057, 0, 3.613, -3.31]}, {"time": 4.1667, "value": -3.31, "curve": [4.615, -3.31, 4.944, -1.47]}, {"time": 5.3333, "value": -0.33, "curve": [5.444, -0.01, 5.466, -5.33]}, {"time": 5.6667, "value": -5.32, "curve": [5.743, -5.32, 5.874, 3.79]}, {"time": 6, "value": 3.95, "curve": [6.168, 4.15, 6.326, 4.36]}, {"time": 6.3333, "value": 4.33, "curve": [6.595, 3.37, 7.467, 12.65]}, {"time": 7.6667, "value": 12.35, "curve": [7.852, 12.07, 8.023, 7.25]}, {"time": 8.1667, "value": 6.79, "curve": [8.464, 5.87, 8.659, -0.33]}, {"time": 8.6667, "value": -0.33}]}, "toufa43": {"rotate": [{"value": -0.8, "curve": [0.128, -0.35, 0.23, 0]}, {"time": 0.3, "curve": [0.524, 0, 1.079, -3.39]}, {"time": 1.6333, "value": -3.39, "curve": [2.015, -3.39, 2.322, -2.03]}, {"time": 2.6667, "value": -0.8, "curve": [2.795, -0.35, 2.897, 0]}, {"time": 2.9667, "curve": [3.19, 0, 3.746, -3.39]}, {"time": 4.3, "value": -3.39, "curve": [4.682, -3.39, 4.989, -2.03]}, {"time": 5.3333, "value": -0.8, "curve": [5.444, -0.41, 5.466, -5.79]}, {"time": 5.6667, "value": -5.79, "curve": [5.743, -5.78, 5.874, -2.73]}, {"time": 6, "value": -2.57, "curve": [6.168, -2.37, 6.326, -2.16]}, {"time": 6.3333, "value": -2.19, "curve": [6.595, -3.15, 7.467, 6.13]}, {"time": 7.6667, "value": 5.82, "curve": [7.852, 5.54, 8.023, 6.78]}, {"time": 8.1667, "value": 6.32, "curve": [8.464, 5.4, 8.659, -0.8]}, {"time": 8.6667, "value": -0.8}]}, "toufa44": {"rotate": [{"value": -2.84, "curve": [0.188, -1.32, 0.34, 0]}, {"time": 0.4333, "curve": [0.657, 0, 1.213, -7.43]}, {"time": 1.7667, "value": -7.43, "curve": [2.089, -7.43, 2.367, -5.27]}, {"time": 2.6667, "value": -2.84, "curve": [2.855, -1.32, 3.007, 0]}, {"time": 3.1, "curve": [3.324, 0, 3.879, -7.43]}, {"time": 4.4333, "value": -7.43, "curve": [4.756, -7.43, 5.033, -5.27]}, {"time": 5.3333, "value": -2.84, "curve": [5.444, -1.94, 5.465, -7.85]}, {"time": 5.6667, "value": -7.84, "curve": [5.744, -7.84, 5.876, -4.79]}, {"time": 6, "value": -4.64, "curve": [6.169, -4.43, 6.327, -4.23]}, {"time": 6.3333, "value": -4.25, "curve": [6.597, -5.22, 7.469, 4.09]}, {"time": 7.6667, "value": 3.79, "curve": [7.851, 3.52, 8.021, 4.76]}, {"time": 8.1667, "value": 4.31, "curve": [8.469, 3.38, 8.666, -2.84]}, {"time": 8.6667, "value": -2.84}]}, "toufa45": {"rotate": [{"value": -2.93, "curve": [0.233, -1.46, 0.424, 0]}, {"time": 0.5333, "curve": [0.757, 0, 1.313, -5.96]}, {"time": 1.8667, "value": -5.96, "curve": [2.149, -5.96, 2.4, -4.61]}, {"time": 2.6667, "value": -2.93, "curve": [2.9, -1.46, 3.09, 0]}, {"time": 3.2, "curve": [3.424, 0, 3.979, -5.96]}, {"time": 4.5333, "value": -5.96, "curve": [4.815, -5.96, 5.067, -4.61]}, {"time": 5.3333, "value": -2.93, "curve": [5.444, -2.23, 5.465, -7.94]}, {"time": 5.6667, "value": -7.93, "curve": [5.744, -7.93, 5.876, -4.88]}, {"time": 6, "value": -4.73, "curve": [6.169, -4.52, 6.327, -4.32]}, {"time": 6.3333, "value": -4.34, "curve": [6.597, -5.31, 7.469, 4]}, {"time": 7.6667, "value": 3.7, "curve": [7.851, 3.43, 8.021, 4.67]}, {"time": 8.1667, "value": 4.22, "curve": [8.469, 3.29, 8.666, -2.93]}, {"time": 8.6667, "value": -2.93}]}, "toufa46": {"rotate": [{"value": -5.4, "curve": [0.277, -2.88, 0.508, 0]}, {"time": 0.6333, "curve": [0.857, 0, 1.413, -9.1]}, {"time": 1.9667, "value": -9.1, "curve": [2.21, -9.1, 2.433, -7.52]}, {"time": 2.6667, "value": -5.4, "curve": [2.944, -2.88, 3.175, 0]}, {"time": 3.3, "curve": [3.524, 0, 4.079, -9.1]}, {"time": 4.6333, "value": -9.1, "curve": [4.876, -9.1, 5.1, -7.52]}, {"time": 5.3333, "value": -5.4, "curve": [5.444, -4.39, 5.465, -10.4]}, {"time": 5.6667, "value": -10.4, "curve": [5.744, -10.4, 5.876, -1.29]}, {"time": 6, "value": -1.13, "curve": [6.169, -0.93, 6.327, -0.72]}, {"time": 6.3333, "value": -0.75, "curve": [6.597, -1.71, 7.469, 7.59]}, {"time": 7.6667, "value": 7.29, "curve": [7.851, 7.02, 8.021, 2.2]}, {"time": 8.1667, "value": 1.75, "curve": [8.469, 0.82, 8.666, -5.4]}, {"time": 8.6667, "value": -5.4}]}, "toufa47": {"rotate": [{"value": -10.6, "curve": [0.321, -6.07, 0.593, 0]}, {"time": 0.7333, "curve": [0.957, 0, 1.513, -15.39]}, {"time": 2.0667, "value": -15.39, "curve": [2.272, -15.39, 2.467, -13.41]}, {"time": 2.6667, "value": -10.6, "curve": [2.988, -6.07, 3.26, 0]}, {"time": 3.4, "curve": [3.624, 0, 4.179, -15.39]}, {"time": 4.7333, "value": -15.39, "curve": [4.939, -15.39, 5.133, -13.41]}, {"time": 5.3333, "value": -10.6, "curve": [5.444, -9.03, 5.465, -18.15]}, {"time": 5.6667, "value": -18.14, "curve": [5.763, -18.14, 5.877, -5.52]}, {"time": 6, "value": -2.12, "curve": [6.105, 0.79, 6.218, 3.5]}, {"time": 6.3333, "value": 3.6, "curve": [6.547, 3.78, 7.459, -9.36]}, {"time": 7.6667, "value": -9.67, "curve": [7.851, -9.94, 8.021, -2.99]}, {"time": 8.1667, "value": -3.44, "curve": [8.469, -4.37, 8.666, -10.6]}, {"time": 8.6667, "value": -10.6}]}, "toufa48": {"rotate": [{"value": -18.73, "curve": [0.38, -11.91, 0.708, 0]}, {"time": 0.8667, "curve": [1.09, 0, 1.646, -23.37]}, {"time": 2.2, "value": -23.37, "curve": [2.359, -23.37, 2.511, -21.51]}, {"time": 2.6667, "value": -18.73, "curve": [3.047, -11.91, 3.375, 0]}, {"time": 3.5333, "curve": [3.757, 0, 4.313, -23.37]}, {"time": 4.8667, "value": -23.37, "curve": [5.026, -23.37, 5.178, -21.51]}, {"time": 5.3333, "value": -18.73, "curve": [5.444, -16.73, 5.465, -26.27]}, {"time": 5.6667, "value": -26.26, "curve": [5.763, -26.25, 5.877, -13.64]}, {"time": 6, "value": -10.23, "curve": [6.105, -7.33, 6.218, -4.61]}, {"time": 6.3333, "value": -4.51, "curve": [6.547, -4.34, 7.459, -17.48]}, {"time": 7.6667, "value": -17.79, "curve": [7.851, -18.07, 8.021, -11.11]}, {"time": 8.1667, "value": -11.57, "curve": [8.469, -12.5, 8.666, -18.73]}, {"time": 8.6667, "value": -18.73}]}, "touying": {"rotate": [{"curve": [0.224, 0, 0.779, -1.85]}, {"time": 1.3333, "value": -1.85, "curve": [1.89, -1.85, 2.222, 0]}, {"time": 2.6667, "curve": [2.89, 0, 3.446, -1.85]}, {"time": 4, "value": -1.85, "curve": [4.556, -1.85, 4.889, 0]}, {"time": 5.3333, "curve": [5.444, 0, 5.556, -1.71]}, {"time": 5.6667, "value": -0.85, "curve": [5.744, -0.26, 6.188, 2.12]}, {"time": 6.3333, "value": 2.12, "curve": [6.468, 2.12, 6.602, 0.71]}, {"time": 6.7333, "value": 0.4, "curve": [6.88, 0.06, 7.023, -1.01]}, {"time": 7.1667, "value": -1.01, "curve": [7.222, -1.01, 7.667, 1.79]}, {"time": 7.8333, "value": 1.79, "curve": [8, 1.79, 8.167, 2.16]}, {"time": 8.3333, "value": 2.16, "curve": [8.444, 2.16, 8.556, 0.16]}, {"time": 8.6667}]}, "touying2": {"rotate": [{"curve": [0.224, 0, 0.779, -1.85]}, {"time": 1.3333, "value": -1.85, "curve": [1.89, -1.85, 2.222, 0]}, {"time": 2.6667, "curve": [2.89, 0, 3.446, -1.85]}, {"time": 4, "value": -1.85, "curve": [4.556, -1.85, 4.889, 0]}, {"time": 5.3333, "curve": [5.444, 0, 5.556, -1.71]}, {"time": 5.6667, "value": -0.85, "curve": [5.744, -0.26, 6.188, 2.12]}, {"time": 6.3333, "value": 2.12, "curve": [6.468, 2.12, 6.602, 0.71]}, {"time": 6.7333, "value": 0.4, "curve": [6.88, 0.06, 7.023, -1.01]}, {"time": 7.1667, "value": -1.01, "curve": [7.222, -1.01, 7.667, 1.79]}, {"time": 7.8333, "value": 1.79, "curve": [8, 1.79, 8.167, 2.16]}, {"time": 8.3333, "value": 2.16, "curve": [8.444, 2.16, 8.556, 0.16]}, {"time": 8.6667}]}, "touying3": {"rotate": [{"curve": [0.224, 0, 0.779, -1.85]}, {"time": 1.3333, "value": -1.85, "curve": [1.89, -1.85, 2.222, 0]}, {"time": 2.6667, "curve": [2.89, 0, 3.446, -1.85]}, {"time": 4, "value": -1.85, "curve": [4.556, -1.85, 4.889, 0]}, {"time": 5.3333, "curve": [5.444, 0, 5.556, -1.71]}, {"time": 5.6667, "value": -0.85, "curve": [5.744, -0.26, 6.188, 2.12]}, {"time": 6.3333, "value": 2.12, "curve": [6.468, 2.12, 6.602, 0.71]}, {"time": 6.7333, "value": 0.4, "curve": [6.88, 0.06, 7.023, -1.01]}, {"time": 7.1667, "value": -1.01, "curve": [7.222, -1.01, 7.667, 1.79]}, {"time": 7.8333, "value": 1.79, "curve": [8, 1.79, 8.167, 2.16]}, {"time": 8.3333, "value": 2.16, "curve": [8.444, 2.16, 8.556, 0.16]}, {"time": 8.6667}]}, "toufa49": {"rotate": [{"curve": [0.224, 0, 0.779, -3.6]}, {"time": 1.3333, "value": -3.6, "curve": [1.89, -3.6, 2.222, 0]}, {"time": 2.6667, "curve": [2.89, 0, 3.446, -3.6]}, {"time": 4, "value": -3.6, "curve": [4.556, -3.6, 4.889, 0]}, {"time": 5.3333, "curve": [5.556, 0, 5.631, -1.89]}, {"time": 6, "value": -1.87, "curve": [6.2, -1.86, 6.43, -3.71]}, {"time": 6.6667, "value": -3.57, "curve": [6.889, -3.43, 7.12, 2.05]}, {"time": 7.3333, "value": 2.42, "curve": [7.574, 2.81, 7.804, 5.57]}, {"time": 8, "value": 5.18, "curve": [8.372, 4.47, 8.636, 0.07]}, {"time": 8.6667}]}, "toufa50": {"rotate": [{"value": -0.5, "curve": [0.068, -0.2, 0.124, 0]}, {"time": 0.1667, "curve": [0.39, 0, 0.946, -4.97]}, {"time": 1.5, "value": -4.97, "curve": [1.949, -4.97, 2.278, -2.21]}, {"time": 2.6667, "value": -0.5, "curve": [2.735, -0.2, 2.79, 0]}, {"time": 2.8333, "curve": [3.057, 0, 3.613, -4.97]}, {"time": 4.1667, "value": -4.97, "curve": [4.615, -4.97, 4.944, -2.21]}, {"time": 5.3333, "value": -0.5, "curve": [5.556, 0.47, 5.618, -2.36]}, {"time": 6, "value": -2.36, "curve": [6.203, -2.36, 6.436, -4.23]}, {"time": 6.6667, "value": -4.11, "curve": [6.889, -3.99, 7.117, 1.47]}, {"time": 7.3333, "value": 1.8, "curve": [7.574, 2.17, 7.803, 4.95]}, {"time": 8, "value": 4.6, "curve": [8.393, 3.91, 8.665, -0.5]}, {"time": 8.6667, "value": -0.5}]}, "toufa51": {"rotate": [{"value": -1.97, "curve": [0.142, -0.86, 0.257, 0]}, {"time": 0.3333, "curve": [0.557, 0, 1.113, -7.21]}, {"time": 1.6667, "value": -7.21, "curve": [2.032, -7.21, 2.333, -4.6]}, {"time": 2.6667, "value": -1.97, "curve": [2.808, -0.86, 2.924, 0]}, {"time": 3, "curve": [3.224, 0, 3.779, -7.21]}, {"time": 4.3333, "value": -7.21, "curve": [4.699, -7.21, 5, -4.6]}, {"time": 5.3333, "value": -1.97, "curve": [5.444, -1.1, 5.465, -9.54]}, {"time": 5.6667, "value": -9.53, "curve": [5.846, -9.53, 6.082, 3]}, {"time": 6.3333, "value": 3.22, "curve": [6.547, 3.39, 7.459, -9.73]}, {"time": 7.6667, "value": -10.05, "curve": [7.851, -10.32, 8.021, 5.63]}, {"time": 8.1667, "value": 5.18, "curve": [8.469, 4.25, 8.666, -1.97]}, {"time": 8.6667, "value": -1.97}]}, "toufa52": {"rotate": [{"value": -5.83, "curve": [0.218, -2.83, 0.396, 0]}, {"time": 0.5, "curve": [0.724, 0, 1.279, -12.8]}, {"time": 1.8333, "value": -12.8, "curve": [2.128, -12.8, 2.389, -9.65]}, {"time": 2.6667, "value": -5.83, "curve": [2.884, -2.83, 3.062, 0]}, {"time": 3.1667, "curve": [3.39, 0, 3.946, -12.8]}, {"time": 4.5, "value": -12.8, "curve": [4.795, -12.8, 5.056, -9.65]}, {"time": 5.3333, "value": -5.83, "curve": [5.444, -4.3, 5.465, -13.38]}, {"time": 5.6667, "value": -13.37, "curve": [5.846, -13.37, 6.082, -0.83]}, {"time": 6.3333, "value": -0.62, "curve": [6.547, -0.44, 7.459, -13.58]}, {"time": 7.6667, "value": -13.89, "curve": [7.851, -14.16, 8.021, 1.78]}, {"time": 8.1667, "value": 1.33, "curve": [8.469, 0.4, 8.666, -5.83]}, {"time": 8.6667, "value": -5.83}]}, "toufa53": {"rotate": [{"value": -13.77, "curve": [0.294, -7.54, 0.537, 0]}, {"time": 0.6667, "curve": [0.89, 0, 1.446, -21.97]}, {"time": 2, "value": -21.97, "curve": [2.232, -21.97, 2.444, -18.47]}, {"time": 2.6667, "value": -13.77, "curve": [2.961, -7.54, 3.204, 0]}, {"time": 3.3333, "curve": [3.557, 0, 4.113, -21.97]}, {"time": 4.6667, "value": -21.97, "curve": [4.898, -21.97, 5.111, -18.47]}, {"time": 5.3333, "value": -13.77, "curve": [5.444, -11.42, 5.466, -21.15]}, {"time": 5.6667, "value": -21.1, "curve": [5.843, -21.06, 6.077, -8.51]}, {"time": 6.3333, "value": -8.3, "curve": [6.545, -8.13, 7.457, -21.36]}, {"time": 7.6667, "value": -21.71, "curve": [7.852, -22.01, 8.023, -6.08]}, {"time": 8.1667, "value": -6.57, "curve": [8.464, -7.53, 8.659, -13.77]}, {"time": 8.6667, "value": -13.77}]}, "toufa54": {"rotate": [{"curve": [0.224, 0, 0.779, -1.99]}, {"time": 1.3333, "value": -1.99, "curve": [1.89, -1.99, 2.222, 0]}, {"time": 2.6667, "curve": [2.89, 0, 3.446, -1.99]}, {"time": 4, "value": -1.99, "curve": [4.556, -1.99, 4.889, 0]}, {"time": 5.3333, "curve": [5.556, 0, 5.631, -1.9]}, {"time": 6, "value": -1.89, "curve": [6.2, -1.88, 6.43, -3.73]}, {"time": 6.6667, "value": -3.59, "curve": [6.889, -3.46, 7.12, 2.02]}, {"time": 7.3333, "value": 2.38, "curve": [7.574, 2.77, 7.804, 5.53]}, {"time": 8, "value": 5.15, "curve": [8.372, 4.43, 8.636, 0.05]}, {"time": 8.6667}]}, "toufa55": {"rotate": [{"curve": [0.224, 0, 0.779, -3.28]}, {"time": 1.3333, "value": -3.28, "curve": [1.89, -3.28, 2.222, 0]}, {"time": 2.6667, "curve": [2.89, 0, 3.446, -3.28]}, {"time": 4, "value": -3.28, "curve": [4.556, -3.28, 4.889, 0]}, {"time": 5.3333, "curve": [5.556, 0, 5.631, -1.89]}, {"time": 6, "value": -1.87, "curve": [6.2, -1.86, 6.43, -3.72]}, {"time": 6.6667, "value": -3.57, "curve": [6.889, -3.44, 7.12, 2.05]}, {"time": 7.3333, "value": 2.41, "curve": [7.574, 2.8, 7.804, 5.56]}, {"time": 8, "value": 5.18, "curve": [8.372, 4.46, 8.636, 0.07]}, {"time": 8.6667}]}, "toufa56": {"rotate": [{"curve": [0.224, 0, 0.779, -3.32]}, {"time": 1.3333, "value": -3.32, "curve": [1.89, -3.32, 2.222, 0]}, {"time": 2.6667, "curve": [2.89, 0, 3.446, -3.32]}, {"time": 4, "value": -3.32, "curve": [4.556, -3.32, 4.889, 0]}, {"time": 5.3333, "curve": [5.556, 0, 5.631, -1.89]}, {"time": 6, "value": -1.87, "curve": [6.2, -1.86, 6.43, -3.71]}, {"time": 6.6667, "value": -3.57, "curve": [6.889, -3.44, 7.12, 2.05]}, {"time": 7.3333, "value": 2.41, "curve": [7.574, 2.8, 7.804, 5.56]}, {"time": 8, "value": 5.18, "curve": [8.372, 4.46, 8.636, 0.07]}, {"time": 8.6667}]}, "toufa57": {"rotate": [{"curve": [0.224, 0, 0.779, -3.4]}, {"time": 1.3333, "value": -3.4, "curve": [1.89, -3.4, 2.222, 0]}, {"time": 2.6667, "curve": [2.89, 0, 3.446, -3.4]}, {"time": 4, "value": -3.4, "curve": [4.556, -3.4, 4.889, 0]}, {"time": 5.3333, "curve": [5.556, 0, 5.631, -1.89]}, {"time": 6, "value": -1.87, "curve": [6.2, -1.86, 6.43, -3.71]}, {"time": 6.6667, "value": -3.57, "curve": [6.889, -3.44, 7.12, 2.05]}, {"time": 7.3333, "value": 2.41, "curve": [7.574, 2.8, 7.804, 5.56]}, {"time": 8, "value": 5.18, "curve": [8.372, 4.46, 8.636, 0.07]}, {"time": 8.6667}]}, "toufa58": {"rotate": [{"curve": [0.224, 0, 0.779, -6]}, {"time": 1.3333, "value": -6, "curve": [1.89, -6, 2.222, 0]}, {"time": 2.6667, "curve": [2.89, 0, 3.446, -6]}, {"time": 4, "value": -6, "curve": [4.556, -6, 4.889, 0]}, {"time": 5.3333, "curve": [5.556, 0, 5.631, -1.88]}, {"time": 6, "value": -1.85, "curve": [6.2, -1.83, 6.43, -3.67]}, {"time": 6.6667, "value": -3.52, "curve": [6.889, -3.38, 7.12, 2.11]}, {"time": 7.3333, "value": 2.47, "curve": [7.574, 2.87, 7.804, 5.63]}, {"time": 8, "value": 5.24, "curve": [8.372, 4.52, 8.636, 0.11]}, {"time": 8.6667}]}, "toufa59": {"rotate": [{"curve": [0.224, 0, 0.779, -8.04]}, {"time": 1.3333, "value": -8.04, "curve": [1.89, -8.04, 2.222, 0]}, {"time": 2.6667, "curve": [2.89, 0, 3.446, -8.04]}, {"time": 4, "value": -8.04, "curve": [4.556, -8.04, 4.889, 0]}, {"time": 5.3333, "curve": [5.444, 0, 5.465, -7.59]}, {"time": 5.6667, "value": -7.59, "curve": [5.846, -7.59, 6.082, 4.95]}, {"time": 6.3333, "value": 5.16, "curve": [6.547, 5.34, 7.459, -7.78]}, {"time": 7.6667, "value": -8.09, "curve": [7.851, -8.36, 8.021, 7.59]}, {"time": 8.1667, "value": 7.14, "curve": [8.469, 6.22, 8.666, 0]}, {"time": 8.6667}]}, "toufa60": {"rotate": [{"curve": [0.224, 0, 0.779, -16.56]}, {"time": 1.3333, "value": -16.56, "curve": [1.89, -16.56, 2.222, 0]}, {"time": 2.6667, "curve": [2.89, 0, 3.446, -16.56]}, {"time": 4, "value": -16.56, "curve": [4.556, -16.56, 4.889, 0]}, {"time": 5.3333, "curve": [5.444, 0, 5.465, -7.59]}, {"time": 5.6667, "value": -7.59, "curve": [5.846, -7.59, 6.082, 4.95]}, {"time": 6.3333, "value": 5.16, "curve": [6.547, 5.34, 7.459, -7.78]}, {"time": 7.6667, "value": -8.09, "curve": [7.851, -8.36, 8.021, 7.59]}, {"time": 8.1667, "value": 7.14, "curve": [8.469, 6.22, 8.666, 0]}, {"time": 8.6667}]}, "toufa61": {"rotate": [{"curve": [0.224, 0, 0.779, -21.93]}, {"time": 1.3333, "value": -21.93, "curve": [1.89, -21.93, 2.222, 0]}, {"time": 2.6667, "curve": [2.89, 0, 3.446, -21.93]}, {"time": 4, "value": -21.93, "curve": [4.556, -21.93, 4.889, 0]}, {"time": 5.3333, "curve": [5.444, 0, 5.465, -7.59]}, {"time": 5.6667, "value": -7.59, "curve": [5.846, -7.59, 6.082, 4.95]}, {"time": 6.3333, "value": 5.16, "curve": [6.547, 5.34, 7.459, -7.78]}, {"time": 7.6667, "value": -8.09, "curve": [7.851, -8.36, 8.021, 7.59]}, {"time": 8.1667, "value": 7.14, "curve": [8.469, 6.22, 8.666, 0]}, {"time": 8.6667}]}, "toufa62": {"rotate": [{"curve": [0.224, 0, 0.779, -2.48]}, {"time": 1.3333, "value": -2.48, "curve": [1.89, -2.48, 2.222, 0]}, {"time": 2.6667, "curve": [2.89, 0, 3.446, -2.48]}, {"time": 4, "value": -2.48, "curve": [4.556, -2.48, 4.889, 0]}, {"time": 5.3333, "curve": [5.556, 0, 5.631, -1.9]}, {"time": 6, "value": -1.88, "curve": [6.2, -1.87, 6.43, -3.73]}, {"time": 6.6667, "value": -3.59, "curve": [6.889, -3.45, 7.12, 2.03]}, {"time": 7.3333, "value": 2.39, "curve": [7.574, 2.78, 7.804, 5.54]}, {"time": 8, "value": 5.16, "curve": [8.372, 4.44, 8.636, 0.05]}, {"time": 8.6667}]}, "toufa63": {"rotate": [{"curve": [0.224, 0, 0.779, -3.33]}, {"time": 1.3333, "value": -3.33, "curve": [1.89, -3.33, 2.222, 0]}, {"time": 2.6667, "curve": [2.89, 0, 3.446, -3.33]}, {"time": 4, "value": -3.33, "curve": [4.556, -3.33, 4.889, 0]}, {"time": 5.3333, "curve": [5.556, 0, 5.631, -1.89]}, {"time": 6, "value": -1.87, "curve": [6.2, -1.86, 6.43, -3.71]}, {"time": 6.6667, "value": -3.57, "curve": [6.889, -3.44, 7.12, 2.05]}, {"time": 7.3333, "value": 2.41, "curve": [7.574, 2.8, 7.804, 5.56]}, {"time": 8, "value": 5.18, "curve": [8.372, 4.46, 8.636, 0.07]}, {"time": 8.6667}]}, "toufa64": {"rotate": [{"curve": [0.224, 0, 0.779, -5.74]}, {"time": 1.3333, "value": -5.74, "curve": [1.89, -5.74, 2.222, 0]}, {"time": 2.6667, "curve": [2.89, 0, 3.446, -5.74]}, {"time": 4, "value": -5.74, "curve": [4.556, -5.74, 4.889, 0]}, {"time": 5.3333, "curve": [5.556, 0, 5.631, -1.88]}, {"time": 6, "value": -1.85, "curve": [6.2, -1.83, 6.43, -3.68]}, {"time": 6.6667, "value": -3.53, "curve": [6.889, -3.39, 7.12, 2.1]}, {"time": 7.3333, "value": 2.47, "curve": [7.574, 2.86, 7.804, 5.62]}, {"time": 8, "value": 5.24, "curve": [8.372, 4.52, 8.636, 0.1]}, {"time": 8.6667}]}, "toufa65": {"rotate": [{"curve": [0.224, 0, 0.779, -7.03]}, {"time": 1.3333, "value": -7.03, "curve": [1.89, -7.03, 2.222, 0]}, {"time": 2.6667, "curve": [2.89, 0, 3.446, -7.03]}, {"time": 4, "value": -7.03, "curve": [4.556, -7.03, 4.889, 0]}, {"time": 5.3333, "curve": [5.444, 0, 5.465, -7.59]}, {"time": 5.6667, "value": -7.59, "curve": [5.846, -7.59, 6.082, 4.95]}, {"time": 6.3333, "value": 5.16, "curve": [6.547, 5.34, 7.459, -7.78]}, {"time": 7.6667, "value": -8.09, "curve": [7.851, -8.36, 8.021, 7.59]}, {"time": 8.1667, "value": 7.14, "curve": [8.469, 6.22, 8.666, 0]}, {"time": 8.6667}]}, "toufa66": {"rotate": [{"curve": [0.224, 0, 0.779, -14.91]}, {"time": 1.3333, "value": -14.91, "curve": [1.89, -14.91, 2.222, 0]}, {"time": 2.6667, "curve": [2.89, 0, 3.446, -14.91]}, {"time": 4, "value": -14.91, "curve": [4.556, -14.91, 4.889, 0]}, {"time": 5.3333, "curve": [5.444, 0, 5.465, -7.59]}, {"time": 5.6667, "value": -7.59, "curve": [5.846, -7.59, 6.082, 4.95]}, {"time": 6.3333, "value": 5.16, "curve": [6.547, 5.34, 7.459, -7.78]}, {"time": 7.6667, "value": -8.09, "curve": [7.851, -8.36, 8.021, 7.59]}, {"time": 8.1667, "value": 7.14, "curve": [8.469, 6.22, 8.666, 0]}, {"time": 8.6667}]}, "toufa67": {"rotate": [{"curve": [0.224, 0, 0.779, -23.58]}, {"time": 1.3333, "value": -23.58, "curve": [1.89, -23.58, 2.222, 0]}, {"time": 2.6667, "curve": [2.89, 0, 3.446, -23.58]}, {"time": 4, "value": -23.58, "curve": [4.556, -23.58, 4.889, 0]}, {"time": 5.3333, "curve": [5.444, 0, 5.465, -7.59]}, {"time": 5.6667, "value": -7.59, "curve": [5.846, -7.59, 6.082, 4.95]}, {"time": 6.3333, "value": 5.16, "curve": [6.547, 5.34, 7.459, -7.78]}, {"time": 7.6667, "value": -8.09, "curve": [7.851, -8.36, 8.021, 7.59]}, {"time": 8.1667, "value": 7.14, "curve": [8.469, 6.22, 8.666, 0]}, {"time": 8.6667}]}, "toufa68": {"rotate": [{"curve": [0.224, 0, 0.779, -1.98]}, {"time": 1.3333, "value": -1.98, "curve": [1.89, -1.98, 2.222, 0]}, {"time": 2.6667, "curve": [2.89, 0, 3.446, -1.98]}, {"time": 4, "value": -1.98, "curve": [4.556, -1.98, 4.889, 0]}, {"time": 5.3333, "curve": [5.556, 0, 5.631, -1.9]}, {"time": 6, "value": -1.89, "curve": [6.2, -1.88, 6.43, -3.73]}, {"time": 6.6667, "value": -3.59, "curve": [6.889, -3.46, 7.12, 2.02]}, {"time": 7.3333, "value": 2.38, "curve": [7.574, 2.77, 7.804, 5.53]}, {"time": 8, "value": 5.15, "curve": [8.372, 4.43, 8.636, 0.05]}, {"time": 8.6667}]}, "toufa69": {"rotate": [{"value": -0.14, "curve": [0.038, -0.06, 0.072, 0]}, {"time": 0.1, "curve": [0.324, 0, 0.879, -3.07]}, {"time": 1.4333, "value": -3.07, "curve": [1.919, -3.07, 2.256, -1.07]}, {"time": 2.6667, "value": -0.14, "curve": [2.705, -0.06, 2.739, 0]}, {"time": 2.7667, "curve": [2.99, 0, 3.546, -3.07]}, {"time": 4.1, "value": -3.07, "curve": [4.586, -3.07, 4.922, -1.07]}, {"time": 5.3333, "value": -0.14, "curve": [5.556, 0.35, 5.631, -1.96]}, {"time": 6, "value": -1.95, "curve": [6.2, -1.95, 6.43, -3.81]}, {"time": 6.6667, "value": -3.69, "curve": [6.889, -3.57, 7.12, 1.9]}, {"time": 7.3333, "value": 2.25, "curve": [7.574, 2.62, 7.804, 5.37]}, {"time": 8, "value": 4.97, "curve": [8.372, 4.24, 8.636, -0.14]}, {"time": 8.6667, "value": -0.14}]}, "toufa70": {"rotate": [{"value": -0.53, "curve": [0.082, -0.21, 0.15, 0]}, {"time": 0.2, "curve": [0.424, 0, 0.979, -3.93]}, {"time": 1.5333, "value": -3.93, "curve": [1.964, -3.93, 2.289, -1.96]}, {"time": 2.6667, "value": -0.53, "curve": [2.749, -0.21, 2.816, 0]}, {"time": 2.8667, "curve": [3.09, 0, 3.646, -3.93]}, {"time": 4.2, "value": -3.93, "curve": [4.63, -3.93, 4.956, -1.96]}, {"time": 5.3333, "value": -0.53, "curve": [5.444, -0.1, 5.466, -3.68]}, {"time": 5.6667, "value": -3.67, "curve": [5.763, -3.67, 5.877, 6.35]}, {"time": 6, "value": 7.35, "curve": [6.106, 8.19, 6.217, 8.98]}, {"time": 6.3333, "value": 9.07, "curve": [6.545, 9.25, 7.457, 5.61]}, {"time": 7.6667, "value": 5.29, "curve": [7.852, 5.01, 8.023, 7.06]}, {"time": 8.1667, "value": 6.6, "curve": [8.464, 5.68, 8.659, -0.53]}, {"time": 8.6667, "value": -0.53}]}, "toufa71": {"rotate": [{"value": -1.15, "curve": [0.128, -0.5, 0.23, 0]}, {"time": 0.3, "curve": [0.524, 0, 1.079, -4.86]}, {"time": 1.6333, "value": -4.86, "curve": [2.015, -4.86, 2.322, -2.91]}, {"time": 2.6667, "value": -1.15, "curve": [2.795, -0.5, 2.897, 0]}, {"time": 2.9667, "curve": [3.19, 0, 3.746, -4.86]}, {"time": 4.3, "value": -4.86, "curve": [4.682, -4.86, 4.989, -2.91]}, {"time": 5.3333, "value": -1.15, "curve": [5.444, -0.58, 5.466, -4.29]}, {"time": 5.6667, "value": -4.28, "curve": [5.763, -4.27, 5.877, 5.74]}, {"time": 6, "value": 6.75, "curve": [6.106, 7.58, 6.217, 8.37]}, {"time": 6.3333, "value": 8.47, "curve": [6.545, 8.64, 7.457, 5]}, {"time": 7.6667, "value": 4.67, "curve": [7.852, 4.39, 8.023, 6.45]}, {"time": 8.1667, "value": 5.98, "curve": [8.464, 5.05, 8.659, -1.15]}, {"time": 8.6667, "value": -1.15}]}, "toufa72": {"rotate": [{"value": -1.61, "curve": [0.174, -0.74, 0.313, 0]}, {"time": 0.4, "curve": [0.624, 0, 1.179, -4.63]}, {"time": 1.7333, "value": -4.63, "curve": [2.071, -4.63, 2.356, -3.16]}, {"time": 2.6667, "value": -1.61, "curve": [2.84, -0.74, 2.979, 0]}, {"time": 3.0667, "curve": [3.29, 0, 3.846, -4.63]}, {"time": 4.4, "value": -4.63, "curve": [4.737, -4.63, 5.022, -3.16]}, {"time": 5.3333, "value": -1.61, "curve": [5.444, -1.05, 5.466, -4.75]}, {"time": 5.6667, "value": -4.74, "curve": [5.763, -4.73, 5.877, -0.77]}, {"time": 6, "value": 0.23, "curve": [6.106, 1.07, 6.217, 1.86]}, {"time": 6.3333, "value": 1.96, "curve": [6.545, 2.13, 7.457, -1.52]}, {"time": 7.6667, "value": -1.84, "curve": [7.852, -2.12, 8.023, 5.99]}, {"time": 8.1667, "value": 5.53, "curve": [8.464, 4.6, 8.659, -1.61]}, {"time": 8.6667, "value": -1.61}]}, "toufa73": {"rotate": [{"value": -2.87, "curve": [0.218, -1.39, 0.396, 0]}, {"time": 0.5, "curve": [0.724, 0, 1.279, -6.3]}, {"time": 1.8333, "value": -6.3, "curve": [2.128, -6.3, 2.389, -4.75]}, {"time": 2.6667, "value": -2.87, "curve": [2.884, -1.39, 3.062, 0]}, {"time": 3.1667, "curve": [3.39, 0, 3.946, -6.3]}, {"time": 4.5, "value": -6.3, "curve": [4.795, -6.3, 5.056, -4.75]}, {"time": 5.3333, "value": -2.87, "curve": [5.444, -2.11, 5.465, -6.04]}, {"time": 5.6667, "value": -6.04, "curve": [5.763, -6.03, 5.877, -2.09]}, {"time": 6, "value": -1.1, "curve": [6.105, -0.25, 6.218, 0.54]}, {"time": 6.3333, "value": 0.64, "curve": [6.547, 0.82, 7.459, -2.8]}, {"time": 7.6667, "value": -3.11, "curve": [7.851, -3.38, 8.021, 4.73]}, {"time": 8.1667, "value": 4.28, "curve": [8.469, 3.35, 8.666, -2.87]}, {"time": 8.6667, "value": -2.87}]}, "toufa74": {"rotate": [{"value": -7.16, "curve": [0.277, -3.82, 0.508, 0]}, {"time": 0.6333, "curve": [0.857, 0, 1.413, -12.08]}, {"time": 1.9667, "value": -12.08, "curve": [2.21, -12.08, 2.433, -9.98]}, {"time": 2.6667, "value": -7.16, "curve": [2.944, -3.82, 3.175, 0]}, {"time": 3.3, "curve": [3.524, 0, 4.079, -12.08]}, {"time": 4.6333, "value": -12.08, "curve": [4.876, -12.08, 5.1, -9.98]}, {"time": 5.3333, "value": -7.16, "curve": [5.444, -5.82, 5.465, -10.32]}, {"time": 5.6667, "value": -10.32, "curve": [5.763, -10.32, 5.877, -6.37]}, {"time": 6, "value": -5.38, "curve": [6.105, -4.53, 6.218, -3.74]}, {"time": 6.3333, "value": -3.64, "curve": [6.547, -3.46, 7.459, -7.09]}, {"time": 7.6667, "value": -7.4, "curve": [7.851, -7.67, 8.021, 0.44]}, {"time": 8.1667, "value": -0.01, "curve": [8.469, -0.94, 8.666, -7.16]}, {"time": 8.6667, "value": -7.16}]}, "toufa75": {"rotate": [{"value": -10.45, "curve": [0.321, -5.99, 0.593, 0]}, {"time": 0.7333, "curve": [0.957, 0, 1.513, -15.19]}, {"time": 2.0667, "value": -15.19, "curve": [2.272, -15.19, 2.467, -13.23]}, {"time": 2.6667, "value": -10.45, "curve": [2.988, -5.99, 3.26, 0]}, {"time": 3.4, "curve": [3.624, 0, 4.179, -15.19]}, {"time": 4.7333, "value": -15.19, "curve": [4.939, -15.19, 5.133, -13.23]}, {"time": 5.3333, "value": -10.45, "curve": [5.444, -8.91, 5.465, -13.61]}, {"time": 5.6667, "value": -13.6, "curve": [5.763, -13.6, 5.877, -3.6]}, {"time": 6, "value": -2.6, "curve": [6.105, -1.76, 6.218, -0.96]}, {"time": 6.3333, "value": -0.86, "curve": [6.547, -0.69, 7.459, -4.31]}, {"time": 7.6667, "value": -4.63, "curve": [7.851, -4.9, 8.021, -2.84]}, {"time": 8.1667, "value": -3.3, "curve": [8.469, -4.23, 8.666, -10.45]}, {"time": 8.6667, "value": -10.45}]}, "toufa76": {"rotate": [{"curve": [0.224, 0, 0.779, -3.3]}, {"time": 1.3333, "value": -3.3, "curve": [1.89, -3.3, 2.222, 0]}, {"time": 2.6667, "curve": [2.89, 0, 3.446, -3.3]}, {"time": 4, "value": -3.3, "curve": [4.556, -3.3, 4.889, 0]}, {"time": 5.3333, "curve": [5.556, 0, 5.631, -1.89]}, {"time": 6, "value": -1.87, "curve": [6.2, -1.86, 6.43, -3.72]}, {"time": 6.6667, "value": -3.57, "curve": [6.889, -3.44, 7.12, 2.05]}, {"time": 7.3333, "value": 2.41, "curve": [7.574, 2.8, 7.804, 5.56]}, {"time": 8, "value": 5.18, "curve": [8.372, 4.46, 8.636, 0.07]}, {"time": 8.6667}]}, "toufa77": {"rotate": [{"value": -0.39, "curve": [0.054, -0.16, 0.098, 0]}, {"time": 0.1333, "curve": [0.357, 0, 0.913, -5.26]}, {"time": 1.4667, "value": -5.26, "curve": [1.934, -5.26, 2.267, -2.08]}, {"time": 2.6667, "value": -0.39, "curve": [2.72, -0.16, 2.764, 0]}, {"time": 2.8, "curve": [3.024, 0, 3.579, -5.26]}, {"time": 4.1333, "value": -5.26, "curve": [4.601, -5.26, 4.933, -2.08]}, {"time": 5.3333, "value": -0.39, "curve": [5.556, 0.55, 5.631, -2.11]}, {"time": 6, "value": -2.09, "curve": [6.2, -2.08, 6.43, -3.95]}, {"time": 6.6667, "value": -3.83, "curve": [6.889, -3.72, 7.12, 1.74]}, {"time": 7.3333, "value": 2.08, "curve": [7.574, 2.44, 7.804, 5.17]}, {"time": 8, "value": 4.77, "curve": [8.372, 4.02, 8.636, -0.39]}, {"time": 8.6667, "value": -0.39}]}, "toufa78": {"rotate": [{"value": -2.38, "curve": [0.157, -1.05, 0.284, 0]}, {"time": 0.3667, "curve": [0.59, 0, 1.146, -7.67]}, {"time": 1.7, "value": -7.67, "curve": [2.05, -7.67, 2.344, -5.1]}, {"time": 2.6667, "value": -2.38, "curve": [2.823, -1.05, 2.951, 0]}, {"time": 3.0333, "curve": [3.257, 0, 3.813, -7.67]}, {"time": 4.3667, "value": -7.67, "curve": [4.717, -7.67, 5.011, -5.1]}, {"time": 5.3333, "value": -2.38, "curve": [5.512, -0.87, 5.631, -3.92]}, {"time": 6, "value": -3.89, "curve": [6.2, -3.87, 6.43, -5.74]}, {"time": 6.6667, "value": -5.63, "curve": [6.889, -5.53, 7.12, -0.09]}, {"time": 7.3333, "value": 0.23, "curve": [7.574, 0.57, 7.804, 3.28]}, {"time": 8, "value": 2.86, "curve": [8.372, 2.06, 8.636, -2.38]}, {"time": 8.6667, "value": -2.38}]}, "toufa79": {"rotate": [{"value": -4, "curve": [0.174, -1.83, 0.313, 0]}, {"time": 0.4, "curve": [0.624, 0, 1.179, -11.55]}, {"time": 1.7333, "value": -11.55, "curve": [2.071, -11.55, 2.356, -7.88]}, {"time": 2.6667, "value": -4, "curve": [2.84, -1.83, 2.979, 0]}, {"time": 3.0667, "curve": [3.29, 0, 3.846, -11.55]}, {"time": 4.4, "value": -11.55, "curve": [4.737, -11.55, 5.022, -7.88]}, {"time": 5.3333, "value": -4, "curve": [5.444, -2.61, 5.465, -11.56]}, {"time": 5.6667, "value": -11.55, "curve": [5.846, -11.54, 6.082, 0.99]}, {"time": 6.3333, "value": 1.2, "curve": [6.547, 1.38, 7.459, -11.75]}, {"time": 7.6667, "value": -12.07, "curve": [7.851, -12.34, 8.021, 3.61]}, {"time": 8.1667, "value": 3.15, "curve": [8.469, 2.22, 8.666, -4]}, {"time": 8.6667, "value": -4}]}, "toufa80": {"rotate": [{"value": -6.44, "curve": [0.233, -3.21, 0.424, 0]}, {"time": 0.5333, "curve": [0.757, 0, 1.313, -13.12]}, {"time": 1.8667, "value": -13.12, "curve": [2.149, -13.12, 2.4, -10.14]}, {"time": 2.6667, "value": -6.44, "curve": [2.9, -3.21, 3.09, 0]}, {"time": 3.2, "curve": [3.424, 0, 3.979, -13.12]}, {"time": 4.5333, "value": -13.12, "curve": [4.815, -13.12, 5.067, -10.14]}, {"time": 5.3333, "value": -6.44, "curve": [5.444, -4.9, 5.465, -14]}, {"time": 5.6667, "value": -13.99, "curve": [5.846, -13.98, 6.082, -1.45]}, {"time": 6.3333, "value": -1.24, "curve": [6.547, -1.06, 7.459, -14.19]}, {"time": 7.6667, "value": -14.51, "curve": [7.851, -14.78, 8.021, 1.17]}, {"time": 8.1667, "value": 0.71, "curve": [8.469, -0.22, 8.666, -6.44]}, {"time": 8.6667, "value": -6.44}]}, "toufa81": {"rotate": [{"value": -8.26, "curve": [0.277, -4.4, 0.508, 0]}, {"time": 0.6333, "curve": [0.857, 0, 1.413, -13.93]}, {"time": 1.9667, "value": -13.93, "curve": [2.21, -13.93, 2.433, -11.51]}, {"time": 2.6667, "value": -8.26, "curve": [2.944, -4.4, 3.175, 0]}, {"time": 3.3, "curve": [3.524, 0, 4.079, -13.93]}, {"time": 4.6333, "value": -13.93, "curve": [4.876, -13.93, 5.1, -11.51]}, {"time": 5.3333, "value": -8.26, "curve": [5.444, -6.71, 5.465, -15.81]}, {"time": 5.6667, "value": -15.8, "curve": [5.846, -15.8, 6.082, -3.26]}, {"time": 6.3333, "value": -3.05, "curve": [6.547, -2.88, 7.459, -16.01]}, {"time": 7.6667, "value": -16.32, "curve": [7.851, -16.6, 8.021, -0.65]}, {"time": 8.1667, "value": -1.1, "curve": [8.469, -2.04, 8.666, -8.26]}, {"time": 8.6667, "value": -8.26}]}, "toufa82": {"rotate": [{"curve": [0.224, 0, 0.779, 2.57]}, {"time": 1.3333, "value": 2.57, "curve": [1.89, 2.57, 2.222, 0]}, {"time": 2.6667, "curve": [2.89, 0, 3.446, 2.57]}, {"time": 4, "value": 2.57, "curve": [4.556, 2.57, 4.889, 0]}, {"time": 5.3333, "curve": [5.444, 0, 5.556, -3.55]}, {"time": 5.6667, "value": -2.69, "curve": [5.744, -2.1, 6.188, 2.12]}, {"time": 6.3333, "value": 2.12, "curve": [6.445, 2.12, 6.557, 1.93]}, {"time": 6.6667, "value": 1.67, "curve": [6.779, 1.41, 6.89, -1.55]}, {"time": 7, "value": -1.55, "curve": [7.034, -1.55, 7.792, 3.16]}, {"time": 7.8333, "value": 3.16, "curve": [7.927, 3.16, 8.145, 1.82]}, {"time": 8.3333, "value": 1.33, "curve": [8.485, 0.94, 8.618, 0]}, {"time": 8.6667}]}, "toufa83": {"rotate": [{"value": 0.34, "curve": [0.054, 0.14, 0.098, 0]}, {"time": 0.1333, "curve": [0.357, 0, 0.913, 4.64]}, {"time": 1.4667, "value": 4.64, "curve": [1.934, 4.64, 2.267, 1.83]}, {"time": 2.6667, "value": 0.34, "curve": [2.72, 0.14, 2.764, 0]}, {"time": 2.8, "curve": [3.024, 0, 3.579, 4.64]}, {"time": 4.1333, "value": 4.64, "curve": [4.601, 4.64, 4.933, 1.83]}, {"time": 5.3333, "value": 0.34, "curve": [5.444, -0.07, 5.478, -2.46]}, {"time": 5.6667, "value": -3.04, "curve": [5.763, -3.34, 5.876, -1.73]}, {"time": 6, "value": -1.73, "curve": [6.2, -1.74, 6.43, -2.32]}, {"time": 6.6667, "value": -2.17, "curve": [6.777, -2.1, 6.89, -0.95]}, {"time": 7, "value": 0.8, "curve": [7.086, 2.12, 7.748, 6.93]}, {"time": 7.8333, "value": 6.77, "curve": [7.919, 6.62, 8.129, 5.4]}, {"time": 8.3333, "value": 3.72, "curve": [8.497, 2.38, 8.656, 0.36]}, {"time": 8.6667, "value": 0.34}]}, "toufa84": {"rotate": [{"value": 1.83, "curve": [0.128, 0.79, 0.23, 0]}, {"time": 0.3, "curve": [0.524, 0, 1.079, 7.72]}, {"time": 1.6333, "value": 7.72, "curve": [2.015, 7.72, 2.322, 4.63]}, {"time": 2.6667, "value": 1.83, "curve": [2.795, 0.79, 2.897, 0]}, {"time": 2.9667, "curve": [3.19, 0, 3.746, 7.72]}, {"time": 4.3, "value": 7.72, "curve": [4.682, 7.72, 4.989, 4.63]}, {"time": 5.3333, "value": 1.83, "curve": [5.444, 0.93, 5.473, -1.03]}, {"time": 5.6667, "value": -1.63, "curve": [5.764, -1.93, 5.876, -0.32]}, {"time": 6, "value": -0.32, "curve": [6.201, -0.33, 6.432, -0.92]}, {"time": 6.6667, "value": -0.78, "curve": [6.777, -0.71, 6.889, 0.47]}, {"time": 7, "value": 2.23, "curve": [7.085, 3.55, 7.747, 8.33]}, {"time": 7.8333, "value": 8.18, "curve": [7.919, 8.03, 8.128, 6.81]}, {"time": 8.3333, "value": 5.15, "curve": [8.497, 3.83, 8.656, 1.83]}, {"time": 8.6667, "value": 1.83}]}, "toufa85": {"rotate": [{"value": 5.09, "curve": [0.188, 2.37, 0.34, 0]}, {"time": 0.4333, "curve": [0.657, 0, 1.213, 13.3]}, {"time": 1.7667, "value": 13.3, "curve": [2.089, 13.3, 2.367, 9.44]}, {"time": 2.6667, "value": 5.09, "curve": [2.855, 2.37, 3.007, 0]}, {"time": 3.1, "curve": [3.324, 0, 3.879, 13.3]}, {"time": 4.4333, "value": 13.3, "curve": [4.756, 13.3, 5.033, 9.44]}, {"time": 5.3333, "value": 5.09, "curve": [5.556, 1.87, 5.876, -3.24]}, {"time": 6, "value": -3.47, "curve": [6.201, -3.85, 6.433, 5.8]}, {"time": 6.6667, "value": 5.85, "curve": [6.832, 5.89, 7.002, -5.14]}, {"time": 7.1667, "value": -3.46, "curve": [7.34, -1.73, 7.509, -0.32]}, {"time": 7.6667, "value": -0.39, "curve": [7.796, -0.44, 7.913, 5.84]}, {"time": 8, "value": 4.99, "curve": [8.409, 1.01, 8.662, 5.09]}, {"time": 8.6667, "value": 5.09}]}, "toufa86": {"rotate": [{"value": 6.67, "curve": [0.218, 3.24, 0.396, 0]}, {"time": 0.5, "curve": [0.724, 0, 1.279, 14.65]}, {"time": 1.8333, "value": 14.65, "curve": [2.128, 14.65, 2.389, 11.05]}, {"time": 2.6667, "value": 6.67, "curve": [2.884, 3.24, 3.062, 0]}, {"time": 3.1667, "curve": [3.39, 0, 3.946, 14.65]}, {"time": 4.5, "value": 14.65, "curve": [4.795, 14.65, 5.056, 11.05]}, {"time": 5.3333, "value": 6.67, "curve": [5.556, 3.16, 5.876, -1.63]}, {"time": 6, "value": -1.86, "curve": [6.202, -2.24, 6.434, 7.41]}, {"time": 6.6667, "value": 7.46, "curve": [6.832, 7.49, 7.001, -3.54]}, {"time": 7.1667, "value": -1.88, "curve": [7.339, -0.14, 7.509, 1.27]}, {"time": 7.6667, "value": 1.2, "curve": [7.796, 1.15, 7.913, 7.45]}, {"time": 8, "value": 6.6, "curve": [8.412, 2.59, 8.666, 6.67]}, {"time": 8.6667, "value": 6.67}]}, "toufa87": {"rotate": [{"value": 10.05, "curve": [0.263, 5.23, 0.48, 0]}, {"time": 0.6, "curve": [0.824, 0, 1.379, 17.97]}, {"time": 1.9333, "value": 17.97, "curve": [2.189, 17.97, 2.422, 14.53]}, {"time": 2.6667, "value": 10.05, "curve": [2.93, 5.23, 3.147, 0]}, {"time": 3.2667, "curve": [3.49, 0, 4.046, 17.97]}, {"time": 4.6, "value": 17.97, "curve": [4.856, 17.97, 5.089, 14.53]}, {"time": 5.3333, "value": 10.05, "curve": [5.556, 5.98, 5.876, 1.75]}, {"time": 6, "value": 1.52, "curve": [6.202, 1.14, 6.434, 10.79]}, {"time": 6.6667, "value": 10.84, "curve": [6.832, 10.87, 7.001, -0.16]}, {"time": 7.1667, "value": 1.51, "curve": [7.339, 3.24, 7.509, 4.65]}, {"time": 7.6667, "value": 4.59, "curve": [7.796, 4.53, 7.913, 10.83]}, {"time": 8, "value": 9.98, "curve": [8.412, 5.97, 8.666, 10.05]}, {"time": 8.6667, "value": 10.05}]}, "toufa88": {"rotate": [{"curve": [0.224, 0, 0.779, 3.84]}, {"time": 1.3333, "value": 3.84, "curve": [1.89, 3.84, 2.222, 0]}, {"time": 2.6667, "curve": [2.89, 0, 3.446, 3.84]}, {"time": 4, "value": 3.84, "curve": [4.556, 3.84, 4.889, 0]}, {"time": 5.3333, "curve": [5.444, 0, 5.556, -3.55]}, {"time": 5.6667, "value": -2.69, "curve": [5.744, -2.1, 6.188, 2.12]}, {"time": 6.3333, "value": 2.12, "curve": [6.445, 2.12, 6.557, 1.93]}, {"time": 6.6667, "value": 1.67, "curve": [6.779, 1.41, 6.89, -1.55]}, {"time": 7, "value": -1.55, "curve": [7.034, -1.55, 7.792, 3.16]}, {"time": 7.8333, "value": 3.16, "curve": [7.927, 3.16, 8.145, 1.82]}, {"time": 8.3333, "value": 1.33, "curve": [8.485, 0.94, 8.618, 0]}, {"time": 8.6667}]}, "toufa89": {"rotate": [{"value": 0.47, "curve": [0.082, 0.19, 0.15, 0]}, {"time": 0.2, "curve": [0.424, 0, 0.979, 3.49]}, {"time": 1.5333, "value": 3.49, "curve": [1.964, 3.49, 2.289, 1.74]}, {"time": 2.6667, "value": 0.47, "curve": [2.749, 0.19, 2.816, 0]}, {"time": 2.8667, "curve": [3.09, 0, 3.646, 3.49]}, {"time": 4.2, "value": 3.49, "curve": [4.63, 3.49, 4.956, 1.74]}, {"time": 5.3333, "value": 0.47, "curve": [5.444, 0.09, 5.478, -2.32]}, {"time": 5.6667, "value": -2.9, "curve": [5.763, -3.2, 5.876, -1.59]}, {"time": 6, "value": -1.59, "curve": [6.2, -1.59, 6.43, -2.18]}, {"time": 6.6667, "value": -2.03, "curve": [6.777, -1.96, 6.89, -0.81]}, {"time": 7, "value": 0.94, "curve": [7.086, 2.26, 7.748, 7.06]}, {"time": 7.8333, "value": 6.9, "curve": [7.919, 6.75, 8.129, 5.53]}, {"time": 8.3333, "value": 3.84, "curve": [8.497, 2.5, 8.656, 0.48]}, {"time": 8.6667, "value": 0.47}]}, "toufa90": {"rotate": [{"value": 1.4, "curve": [0.082, 0.99, 0.706, -0.29]}, {"time": 1.3333, "value": 0.02, "curve": [1.922, 0.3, 2.389, 2.78]}, {"time": 2.6667, "value": 1.4, "curve": [2.749, 0.99, 3.372, -0.29]}, {"time": 4, "value": 0.02, "curve": [4.588, 0.3, 5.179, 2.17]}, {"time": 5.3333, "value": 1.4, "curve": [5.444, 0.85, 5.478, -1.44]}, {"time": 5.6667, "value": -2.04, "curve": [5.763, -2.34, 5.876, -0.74]}, {"time": 6, "value": -0.75, "curve": [6.2, -0.76, 6.43, -1.35]}, {"time": 6.6667, "value": -1.2, "curve": [6.777, -1.13, 6.89, 0.02]}, {"time": 7, "value": 1.76, "curve": [7.086, 3.09, 7.748, 7.91]}, {"time": 7.8333, "value": 7.76, "curve": [7.919, 7.6, 8.129, 6.39]}, {"time": 8.3333, "value": 4.72, "curve": [8.497, 3.4, 8.656, 1.4]}, {"time": 8.6667, "value": 1.4}]}, "toufa91": {"rotate": [{"value": 1.34, "curve": [0.105, 0.98, 0.714, -0.47]}, {"time": 1.3333, "value": -0.25, "curve": [1.925, -0.05, 2.222, 2.86]}, {"time": 2.6667, "value": 1.34, "curve": [2.771, 0.98, 3.381, -0.47]}, {"time": 4, "value": -0.25, "curve": [4.592, -0.05, 5.192, 1.82]}, {"time": 5.3333, "value": 1.34, "curve": [5.444, 0.96, 5.478, -1.45]}, {"time": 5.6667, "value": -2.04, "curve": [5.763, -2.34, 5.876, -0.74]}, {"time": 6, "value": -0.74, "curve": [6.2, -0.75, 6.43, -1.34]}, {"time": 6.6667, "value": -1.2, "curve": [6.777, -1.13, 6.89, 0.02]}, {"time": 7, "value": 1.76, "curve": [7.086, 3.09, 7.748, 7.88]}, {"time": 7.8333, "value": 7.72, "curve": [7.919, 7.57, 8.129, 6.35]}, {"time": 8.3333, "value": 4.68, "curve": [8.497, 3.35, 8.656, 1.34]}, {"time": 8.6667, "value": 1.34}]}, "toufa92": {"rotate": [{"value": 2.44, "curve": [0.119, 1.81, 0.727, 0.46]}, {"time": 1.3333, "value": 0.79, "curve": [1.935, 1.11, 2.353, 4.09]}, {"time": 2.6667, "value": 2.44, "curve": [2.785, 1.81, 3.393, 0.46]}, {"time": 4, "value": 0.79, "curve": [4.602, 1.11, 5.202, 3.13]}, {"time": 5.3333, "value": 2.44, "curve": [5.556, 1.27, 5.876, -5.85]}, {"time": 6, "value": -6.09, "curve": [6.202, -6.46, 6.434, 3.18]}, {"time": 6.6667, "value": 3.23, "curve": [6.833, 3.27, 7.003, -7.78]}, {"time": 7.1667, "value": -6.1, "curve": [7.341, -4.37, 7.51, -2.95]}, {"time": 7.6667, "value": -3.02, "curve": [7.797, -3.08, 7.914, 3.23]}, {"time": 8, "value": 2.37, "curve": [8.415, -1.62, 8.666, 2.44]}, {"time": 8.6667, "value": 2.44}]}, "toufa93": {"rotate": [{"value": 4.52, "curve": [0.138, 3.47, 0.733, 2.17]}, {"time": 1.3333, "value": 2.66, "curve": [1.939, 3.14, 2.422, 6.39]}, {"time": 2.6667, "value": 4.52, "curve": [2.805, 3.47, 3.4, 2.17]}, {"time": 4, "value": 2.66, "curve": [4.605, 3.14, 5.216, 5.41]}, {"time": 5.3333, "value": 4.52, "curve": [5.556, 2.83, 5.876, -3.77]}, {"time": 6, "value": -4, "curve": [6.202, -4.38, 6.434, 5.27]}, {"time": 6.6667, "value": 5.32, "curve": [6.832, 5.35, 7.001, -5.68]}, {"time": 7.1667, "value": -4.02, "curve": [7.339, -2.28, 7.509, -0.87]}, {"time": 7.6667, "value": -0.94, "curve": [7.796, -0.99, 8.666, 4.52]}, {"time": 8.6667, "value": 4.52}]}, "toufa94": {"rotate": [{"value": 9.95, "curve": [0.308, 5.58, 0.565, 0]}, {"time": 0.7, "curve": [0.924, 0, 1.479, 15.11]}, {"time": 2.0333, "value": 15.11, "curve": [2.252, 15.11, 2.456, 12.95]}, {"time": 2.6667, "value": 9.95, "curve": [2.975, 5.58, 3.232, 0]}, {"time": 3.3667, "curve": [3.59, 0, 4.146, 15.11]}, {"time": 4.7, "value": 15.11, "curve": [4.919, 15.11, 5.122, 12.95]}, {"time": 5.3333, "value": 9.95, "curve": [5.556, 6.8, 5.876, 1.65]}, {"time": 6, "value": 1.42, "curve": [6.202, 1.04, 6.434, 10.69]}, {"time": 6.6667, "value": 10.74, "curve": [6.832, 10.77, 7.001, -0.26]}, {"time": 7.1667, "value": 1.41, "curve": [7.339, 3.15, 7.509, 4.56]}, {"time": 7.6667, "value": 4.49, "curve": [7.796, 4.43, 7.913, 10.74]}, {"time": 8, "value": 9.88, "curve": [8.412, 5.88, 8.666, 9.95]}, {"time": 8.6667, "value": 9.95}]}, "toufa95": {"rotate": [{"value": 31.03, "curve": [0.365, 19.25, 0.679, 0]}, {"time": 0.8333, "curve": [1.057, 0, 1.613, 39.97]}, {"time": 2.1667, "value": 39.97, "curve": [2.337, 39.97, 2.5, 36.41]}, {"time": 2.6667, "value": 31.03, "curve": [3.032, 19.25, 3.346, 0]}, {"time": 3.5, "curve": [3.724, 0, 4.279, 39.97]}, {"time": 4.8333, "value": 39.97, "curve": [5.004, 39.97, 5.167, 36.41]}, {"time": 5.3333, "value": 31.03, "curve": [5.556, 23.86, 5.876, 22.72]}, {"time": 6, "value": 22.49, "curve": [6.202, 22.11, 6.434, 31.75]}, {"time": 6.6667, "value": 31.81, "curve": [6.832, 31.84, 7.001, 20.81]}, {"time": 7.1667, "value": 22.48, "curve": [7.339, 24.21, 7.509, 25.63]}, {"time": 7.6667, "value": 25.56, "curve": [7.796, 25.5, 8.666, 31.03]}, {"time": 8.6667, "value": 31.03}]}, "toufa96": {"rotate": [{"curve": [0.224, 0, 0.779, 4.85]}, {"time": 1.3333, "value": 4.85, "curve": [1.89, 4.85, 2.222, 0]}, {"time": 2.6667, "curve": [2.89, 0, 3.446, 4.85]}, {"time": 4, "value": 4.85, "curve": [4.556, 4.85, 4.889, 0]}, {"time": 5.3333, "curve": [5.444, 0, 5.467, -2.71]}, {"time": 5.6667, "value": -3.27, "curve": [5.764, -3.54, 5.876, -1.91]}, {"time": 6, "value": -1.91, "curve": [6.203, -1.91, 6.436, -2.52]}, {"time": 6.6667, "value": -2.39, "curve": [6.777, -2.33, 6.889, -1.17]}, {"time": 7, "value": 0.56, "curve": [7.085, 1.88, 7.747, 6.6]}, {"time": 7.8333, "value": 6.45, "curve": [7.918, 6.31, 8.129, 5.1]}, {"time": 8.3333, "value": 3.43, "curve": [8.502, 2.07, 8.666, 0]}, {"time": 8.6667}]}, "toufa97": {"rotate": [{"value": 0.61, "curve": [0.054, 0.25, 0.098, 0]}, {"time": 0.1333, "curve": [0.357, 0, 0.913, 8.23]}, {"time": 1.4667, "value": 8.23, "curve": [1.934, 8.23, 2.267, 3.24]}, {"time": 2.6667, "value": 0.61, "curve": [2.72, 0.25, 2.764, 0]}, {"time": 2.8, "curve": [3.024, 0, 3.579, 8.23]}, {"time": 4.1333, "value": 8.23, "curve": [4.601, 8.23, 4.933, 3.24]}, {"time": 5.3333, "value": 0.61, "curve": [5.556, -0.86, 5.876, -7.68]}, {"time": 6, "value": -7.92, "curve": [6.202, -8.29, 6.434, 1.35]}, {"time": 6.6667, "value": 1.4, "curve": [6.832, 1.44, 7.001, -9.6]}, {"time": 7.1667, "value": -7.93, "curve": [7.339, -6.19, 7.509, -4.78]}, {"time": 7.6667, "value": -4.85, "curve": [7.796, -4.91, 8.666, 0.61]}, {"time": 8.6667, "value": 0.61}]}, "toufa98": {"rotate": [{"value": 1.01, "curve": [0.082, 0.41, 0.15, 0]}, {"time": 0.2, "curve": [0.424, 0, 0.979, 7.56]}, {"time": 1.5333, "value": 7.56, "curve": [1.964, 7.56, 2.289, 3.77]}, {"time": 2.6667, "value": 1.01, "curve": [2.749, 0.41, 2.816, 0]}, {"time": 2.8667, "curve": [3.09, 0, 3.646, 7.56]}, {"time": 4.2, "value": 7.56, "curve": [4.63, 7.56, 4.956, 3.77]}, {"time": 5.3333, "value": 1.01, "curve": [5.556, -0.61, 5.876, -7.28]}, {"time": 6, "value": -7.52, "curve": [6.202, -7.89, 6.434, 1.75]}, {"time": 6.6667, "value": 1.8, "curve": [6.832, 1.84, 7.001, -9.2]}, {"time": 7.1667, "value": -7.53, "curve": [7.339, -5.79, 7.509, -4.38]}, {"time": 7.6667, "value": -4.45, "curve": [7.796, -4.5, 8.666, 1.01]}, {"time": 8.6667, "value": 1.01}]}, "toufa99": {"rotate": [{"value": 2.67, "curve": [0.128, 1.15, 0.23, 0]}, {"time": 0.3, "curve": [0.524, 0, 1.079, 11.28]}, {"time": 1.6333, "value": 11.28, "curve": [2.015, 11.28, 2.322, 6.76]}, {"time": 2.6667, "value": 2.67, "curve": [2.795, 1.15, 2.897, 0]}, {"time": 2.9667, "curve": [3.19, 0, 3.746, 11.28]}, {"time": 4.3, "value": 11.28, "curve": [4.682, 11.28, 4.989, 6.76]}, {"time": 5.3333, "value": 2.67, "curve": [5.556, 0.03, 5.876, -5.62]}, {"time": 6, "value": -5.85, "curve": [6.202, -6.23, 6.434, 3.42]}, {"time": 6.6667, "value": 3.47, "curve": [6.833, 3.5, 7.003, -7.54]}, {"time": 7.1667, "value": -5.86, "curve": [7.341, -4.14, 7.51, -2.72]}, {"time": 7.6667, "value": -2.79, "curve": [7.796, -2.84, 8.666, 2.67]}, {"time": 8.6667, "value": 2.67}]}, "toufa100": {"rotate": [{"value": 8.09, "curve": [0.218, 3.93, 0.396, 0]}, {"time": 0.5, "curve": [0.724, 0, 1.279, 17.78]}, {"time": 1.8333, "value": 17.78, "curve": [2.128, 17.78, 2.389, 13.41]}, {"time": 2.6667, "value": 8.09, "curve": [2.884, 3.93, 3.062, 0]}, {"time": 3.1667, "curve": [3.39, 0, 3.946, 17.78]}, {"time": 4.5, "value": 17.78, "curve": [4.795, 17.78, 5.056, 13.41]}, {"time": 5.3333, "value": 8.09, "curve": [5.556, 3.84, 5.876, -0.21]}, {"time": 6, "value": -0.44, "curve": [6.202, -0.82, 6.434, 8.83]}, {"time": 6.6667, "value": 8.88, "curve": [6.832, 8.91, 7.001, -2.12]}, {"time": 7.1667, "value": -0.45, "curve": [7.339, 1.28, 7.509, 2.7]}, {"time": 7.6667, "value": 2.63, "curve": [7.796, 2.57, 8.666, 8.09]}, {"time": 8.6667, "value": 8.09}]}, "toufa101": {"rotate": [{"value": 1.31, "curve": [0.054, 0.55, 0.098, 0]}, {"time": 0.1333, "curve": [0.357, 0, 0.913, 17.75]}, {"time": 1.4667, "value": 17.75, "curve": [1.934, 17.75, 2.267, 7]}, {"time": 2.6667, "value": 1.31, "curve": [2.72, 0.55, 2.764, 0]}, {"time": 2.8, "curve": [3.024, 0, 3.579, 17.75]}, {"time": 4.1333, "value": 17.75, "curve": [4.601, 17.75, 4.933, 7]}, {"time": 5.3333, "value": 1.31, "curve": [5.556, -1.85, 5.876, -7]}, {"time": 6, "value": -7.24, "curve": [6.202, -7.61, 6.434, 2.03]}, {"time": 6.6667, "value": 2.08, "curve": [6.832, 2.12, 7.001, -8.92]}, {"time": 7.1667, "value": -7.25, "curve": [7.34, -5.51, 7.509, -4.09]}, {"time": 7.6667, "value": -4.16, "curve": [7.796, -4.22, 8.666, 1.31]}, {"time": 8.6667, "value": 1.31}]}, "toufa102": {"rotate": [{"value": 30.31, "curve": [0.308, 16.99, 0.565, 0]}, {"time": 0.7, "curve": [0.924, 0, 1.479, 46]}, {"time": 2.0333, "value": 46, "curve": [2.252, 46, 2.456, 39.44]}, {"time": 2.6667, "value": 30.31, "curve": [2.975, 16.99, 3.232, 0]}, {"time": 3.3667, "curve": [3.59, 0, 4.146, 46]}, {"time": 4.7, "value": 46, "curve": [4.919, 46, 5.122, 39.44]}, {"time": 5.3333, "value": 30.31, "curve": [5.534, 21.64, 5.877, 21.88]}, {"time": 6, "value": 21.64, "curve": [6.202, 21.26, 6.435, 30.91]}, {"time": 6.6667, "value": 30.97, "curve": [6.832, 31.01, 7.001, 19.98]}, {"time": 7.1667, "value": 21.65, "curve": [7.34, 23.4, 7.509, 24.83]}, {"time": 7.6667, "value": 24.77, "curve": [7.796, 24.72, 8.666, 30.31]}, {"time": 8.6667, "value": 30.31}]}, "toufa103": {"rotate": [{"curve": [0.224, 0, 0.779, 2.62]}, {"time": 1.3333, "value": 2.62, "curve": [1.89, 2.62, 2.222, 0]}, {"time": 2.6667, "curve": [2.89, 0, 3.446, 2.62]}, {"time": 4, "value": 2.62, "curve": [4.556, 2.62, 4.889, 0]}, {"time": 5.3333, "curve": [5.444, 0, 5.556, -3.55]}, {"time": 5.6667, "value": -2.69, "curve": [5.744, -2.1, 6.188, 0.58]}, {"time": 6.3333, "value": 0.58, "curve": [6.445, 0.58, 6.557, 1.93]}, {"time": 6.6667, "value": 1.67, "curve": [6.779, 1.41, 6.89, -1.55]}, {"time": 7, "value": -1.55, "curve": [7.034, -1.55, 7.764, -3.03]}, {"time": 7.8333, "value": -3.33, "curve": [7.881, -3.53, 8.057, -4.31]}, {"time": 8.2333, "value": -3.48, "curve": [8.437, -2.55, 8.641, 0]}, {"time": 8.6667}], "scale": [{"time": 5.3333, "curve": [6.167, 1, 7, 1.042, 6.167, 1, 7, 1.001]}, {"time": 7.8333, "x": 1.042, "y": 1.001, "curve": [8.057, 1.042, 8.445, 1, 8.057, 1.001, 8.445, 1]}, {"time": 8.6667}]}, "toufa104": {"rotate": [{"value": 0.32, "curve": [0.068, 0.13, 0.124, 0]}, {"time": 0.1667, "curve": [0.39, 0, 0.946, 3.18]}, {"time": 1.5, "value": 3.18, "curve": [1.949, 3.18, 2.278, 1.41]}, {"time": 2.6667, "value": 0.32, "curve": [2.735, 0.13, 2.79, 0]}, {"time": 2.8333, "curve": [3.057, 0, 3.613, 3.18]}, {"time": 4.1667, "value": 3.18, "curve": [4.615, 3.18, 4.944, 1.41]}, {"time": 5.3333, "value": 0.32, "curve": [5.444, 0.01, 5.478, -2.45]}, {"time": 5.6667, "value": -3.03, "curve": [5.763, -3.32, 5.876, -1.71]}, {"time": 6, "value": -1.71, "curve": [6.2, -1.71, 6.43, -2.3]}, {"time": 6.6667, "value": -2.15, "curve": [6.777, -2.08, 6.89, -0.93]}, {"time": 7, "value": 0.81, "curve": [7.086, 2.14, 7.748, 6.93]}, {"time": 7.8333, "value": 6.77, "curve": [7.919, 6.61, 8.129, 5.39]}, {"time": 8.3333, "value": 3.7, "curve": [8.497, 2.36, 8.656, 0.33]}, {"time": 8.6667, "value": 0.32}]}, "toufa105": {"rotate": [{"value": 1.07, "curve": [0.128, 0.46, 0.23, 0]}, {"time": 0.3, "curve": [0.524, 0, 1.079, 4.5]}, {"time": 1.6333, "value": 4.5, "curve": [2.015, 4.5, 2.322, 2.7]}, {"time": 2.6667, "value": 1.07, "curve": [2.795, 0.46, 2.897, 0]}, {"time": 2.9667, "curve": [3.19, 0, 3.746, 4.5]}, {"time": 4.3, "value": 4.5, "curve": [4.682, 4.5, 4.989, 2.7]}, {"time": 5.3333, "value": 1.07, "curve": [5.556, 0.01, 5.877, -7.24]}, {"time": 6, "value": -7.47, "curve": [6.202, -7.85, 6.435, 1.8]}, {"time": 6.6667, "value": 1.85, "curve": [6.832, 1.88, 7.001, -9.15]}, {"time": 7.1667, "value": -7.48, "curve": [7.34, -5.75, 7.509, -4.33]}, {"time": 7.6667, "value": -4.4, "curve": [7.796, -4.45, 8.666, 1.07]}, {"time": 8.6667, "value": 1.07}]}, "toufa106": {"rotate": [{"value": 2.2, "curve": [0.204, 1.05, 0.368, 0]}, {"time": 0.4667, "curve": [0.69, 0, 1.246, 5.25]}, {"time": 1.8, "value": 5.25, "curve": [2.11, 5.25, 2.378, 3.83]}, {"time": 2.6667, "value": 2.2, "curve": [2.871, 1.05, 3.035, 0]}, {"time": 3.1333, "curve": [3.357, 0, 3.913, 5.25]}, {"time": 4.4667, "value": 5.25, "curve": [4.776, 5.25, 5.044, 3.83]}, {"time": 5.3333, "value": 2.2, "curve": [5.556, 0.95, 5.876, -6.09]}, {"time": 6, "value": -6.32, "curve": [6.202, -6.7, 6.434, 2.95]}, {"time": 6.6667, "value": 3, "curve": [6.833, 3.03, 7.003, -8.01]}, {"time": 7.1667, "value": -6.34, "curve": [7.342, -4.61, 7.51, -3.19]}, {"time": 7.6667, "value": -3.26, "curve": [7.796, -3.31, 8.666, 2.2]}, {"time": 8.6667, "value": 2.2}]}, "toufa107": {"rotate": [{"value": 3.11, "curve": [0.233, 1.55, 0.424, 0]}, {"time": 0.5333, "curve": [0.757, 0, 1.313, 6.34]}, {"time": 1.8667, "value": 6.34, "curve": [2.149, 6.34, 2.4, 4.9]}, {"time": 2.6667, "value": 3.11, "curve": [2.9, 1.55, 3.09, 0]}, {"time": 3.2, "curve": [3.424, 0, 3.979, 6.34]}, {"time": 4.5333, "value": 6.34, "curve": [4.815, 6.34, 5.067, 4.9]}, {"time": 5.3333, "value": 3.11, "curve": [5.556, 1.62, 5.876, -5.18]}, {"time": 6, "value": -5.41, "curve": [6.202, -5.79, 6.434, 3.86]}, {"time": 6.6667, "value": 3.91, "curve": [6.833, 3.94, 7.003, -7.1]}, {"time": 7.1667, "value": -5.42, "curve": [7.342, -3.7, 7.51, -2.28]}, {"time": 7.6667, "value": -2.35, "curve": [7.796, -2.4, 8.666, 3.11]}, {"time": 8.6667, "value": 3.11}]}, "toufa108": {"rotate": [{"value": 5.49, "curve": [0.263, 2.86, 0.48, 0]}, {"time": 0.6, "curve": [0.824, 0, 1.379, 9.82]}, {"time": 1.9333, "value": 9.82, "curve": [2.189, 9.82, 2.422, 7.94]}, {"time": 2.6667, "value": 5.49, "curve": [2.93, 2.86, 3.147, 0]}, {"time": 3.2667, "curve": [3.49, 0, 4.046, 9.82]}, {"time": 4.6, "value": 9.82, "curve": [4.856, 9.82, 5.089, 7.94]}, {"time": 5.3333, "value": 5.49, "curve": [5.556, 3.26, 5.876, -2.81]}, {"time": 6, "value": -3.04, "curve": [6.202, -3.42, 6.434, 6.23]}, {"time": 6.6667, "value": 6.28, "curve": [6.832, 6.31, 7.001, -4.72]}, {"time": 7.1667, "value": -3.05, "curve": [7.339, -1.31, 7.509, 0.1]}, {"time": 7.6667, "value": 0.03, "curve": [7.796, -0.03, 8.666, 5.49]}, {"time": 8.6667, "value": 5.49}]}, "toufa109": {"rotate": [{"value": 25.11, "curve": [0.353, 15.15, 0.651, 0]}, {"time": 0.8, "curve": [1.024, 0, 1.579, 33.61]}, {"time": 2.1333, "value": 33.61, "curve": [2.317, 33.61, 2.489, 30.12]}, {"time": 2.6667, "value": 25.11, "curve": [3.02, 15.15, 3.318, 0]}, {"time": 3.4667, "curve": [3.69, 0, 4.246, 33.61]}, {"time": 4.8, "value": 33.61, "curve": [4.984, 33.61, 5.156, 30.12]}, {"time": 5.3333, "value": 25.11, "curve": [5.556, 18.84, 5.876, 16.8]}, {"time": 6, "value": 16.57, "curve": [6.202, 16.19, 6.434, 25.84]}, {"time": 6.6667, "value": 25.89, "curve": [6.832, 25.92, 7.001, 14.89]}, {"time": 7.1667, "value": 16.56, "curve": [7.339, 18.29, 7.509, 19.71]}, {"time": 7.6667, "value": 19.64, "curve": [7.796, 19.58, 8.666, 25.11]}, {"time": 8.6667, "value": 25.11}]}, "toufa110": {"rotate": [{"curve": [0.224, 0, 0.779, -2.88]}, {"time": 1.3333, "value": -2.88, "curve": [1.89, -2.88, 2.222, 0]}, {"time": 2.6667, "curve": [2.89, 0, 3.446, -2.88]}, {"time": 4, "value": -2.88, "curve": [4.556, -2.88, 4.889, 0]}, {"time": 5.3333, "curve": [5.556, 0, 5.631, -1.9]}, {"time": 6, "value": -1.88, "curve": [6.2, -1.87, 6.43, -3.72]}, {"time": 6.6667, "value": -3.58, "curve": [6.889, -3.45, 7.12, 2.04]}, {"time": 7.3333, "value": 2.4, "curve": [7.574, 2.79, 7.804, 5.55]}, {"time": 8, "value": 5.17, "curve": [8.372, 4.45, 8.636, 0.06]}, {"time": 8.6667}]}, "toufa111": {"rotate": [{"value": -0.27, "curve": [0.054, -0.11, 0.098, 0]}, {"time": 0.1333, "curve": [0.357, 0, 0.913, -3.65]}, {"time": 1.4667, "value": -3.65, "curve": [1.934, -3.65, 2.267, -1.44]}, {"time": 2.6667, "value": -0.27, "curve": [2.72, -0.11, 2.764, 0]}, {"time": 2.8, "curve": [3.024, 0, 3.579, -3.65]}, {"time": 4.1333, "value": -3.65, "curve": [4.601, -3.65, 4.933, -1.44]}, {"time": 5.3333, "value": -0.27, "curve": [5.556, 0.38, 5.631, -2.05]}, {"time": 6, "value": -2.04, "curve": [6.2, -2.03, 6.43, -3.9]}, {"time": 6.6667, "value": -3.77, "curve": [6.889, -3.66, 7.12, 1.81]}, {"time": 7.3333, "value": 2.15, "curve": [7.574, 2.52, 7.804, 5.26]}, {"time": 8, "value": 4.86, "curve": [8.372, 4.13, 8.636, -0.27]}, {"time": 8.6667, "value": -0.27}]}, "toufa112": {"rotate": [{"value": -1.9, "curve": [0.128, -0.82, 0.23, 0]}, {"time": 0.3, "curve": [0.524, 0, 1.079, -8.03]}, {"time": 1.6333, "value": -8.03, "curve": [2.015, -8.03, 2.322, -4.81]}, {"time": 2.6667, "value": -1.9, "curve": [2.795, -0.82, 2.897, 0]}, {"time": 2.9667, "curve": [3.19, 0, 3.746, -8.03]}, {"time": 4.3, "value": -8.03, "curve": [4.682, -8.03, 4.989, -4.81]}, {"time": 5.3333, "value": -1.9, "curve": [5.444, -0.96, 5.466, -9.41]}, {"time": 5.6667, "value": -9.39, "curve": [5.843, -9.37, 6.077, 3.17]}, {"time": 6.3333, "value": 3.38, "curve": [6.545, 3.56, 7.457, -9.61]}, {"time": 7.6667, "value": -9.94, "curve": [7.852, -10.23, 8.023, 5.71]}, {"time": 8.1667, "value": 5.24, "curve": [8.464, 4.31, 8.659, -1.9]}, {"time": 8.6667, "value": -1.9}]}, "toufa113": {"rotate": [{"value": -4.81, "curve": [0.174, -2.2, 0.313, 0]}, {"time": 0.4, "curve": [0.624, 0, 1.179, -13.87]}, {"time": 1.7333, "value": -13.87, "curve": [2.071, -13.87, 2.356, -9.47]}, {"time": 2.6667, "value": -4.81, "curve": [2.84, -2.2, 2.979, 0]}, {"time": 3.0667, "curve": [3.29, 0, 3.846, -13.87]}, {"time": 4.4, "value": -13.87, "curve": [4.737, -13.87, 5.022, -9.47]}, {"time": 5.3333, "value": -4.81, "curve": [5.444, -3.14, 5.466, -12.24]}, {"time": 5.6667, "value": -12.21, "curve": [5.843, -12.18, 6.077, 0.36]}, {"time": 6.3333, "value": 0.57, "curve": [6.545, 0.74, 7.457, -12.46]}, {"time": 7.6667, "value": -12.79, "curve": [7.852, -13.09, 8.023, 2.84]}, {"time": 8.1667, "value": 2.37, "curve": [8.464, 1.42, 8.659, -4.81]}, {"time": 8.6667, "value": -4.81}]}, "toufa114": {"rotate": [{"value": -10.29, "curve": [0.233, -5.13, 0.424, 0]}, {"time": 0.5333, "curve": [0.757, 0, 1.313, -20.95]}, {"time": 1.8667, "value": -20.95, "curve": [2.149, -20.95, 2.4, -16.21]}, {"time": 2.6667, "value": -10.29, "curve": [2.9, -5.13, 3.09, 0]}, {"time": 3.2, "curve": [3.424, 0, 3.979, -20.95]}, {"time": 4.5333, "value": -20.95, "curve": [4.815, -20.95, 5.067, -16.21]}, {"time": 5.3333, "value": -10.29, "curve": [5.444, -7.83, 5.466, -17.67]}, {"time": 5.6667, "value": -17.62, "curve": [5.843, -17.58, 6.077, -5.03]}, {"time": 6.3333, "value": -4.82, "curve": [6.545, -4.65, 7.457, -17.88]}, {"time": 7.6667, "value": -18.23, "curve": [7.852, -18.53, 8.023, -2.61]}, {"time": 8.1667, "value": -3.09, "curve": [8.464, -4.06, 8.659, -10.29]}, {"time": 8.6667, "value": -10.29}]}, "toufa115": {"rotate": [{"curve": [0.224, 0, 0.779, -1.75]}, {"time": 1.3333, "value": -1.75, "curve": [1.89, -1.75, 2.222, 0]}, {"time": 2.6667, "curve": [2.89, 0, 3.446, -1.75]}, {"time": 4, "value": -1.75, "curve": [4.556, -1.75, 4.889, 0]}, {"time": 5.3333, "curve": [5.556, 0, 5.631, -1.9]}, {"time": 6, "value": -1.89, "curve": [6.2, -1.88, 6.43, -3.74]}, {"time": 6.6667, "value": -3.6, "curve": [6.889, -3.47, 7.12, 2.01]}, {"time": 7.3333, "value": 2.38, "curve": [7.574, 2.76, 7.804, 5.52]}, {"time": 8, "value": 5.14, "curve": [8.372, 4.43, 8.636, 0.04]}, {"time": 8.6667}]}, "toufa116": {"rotate": [{"value": -0.3, "curve": [0.054, -0.13, 0.098, 0]}, {"time": 0.1333, "curve": [0.357, 0, 0.913, -4.09]}, {"time": 1.4667, "value": -4.09, "curve": [1.934, -4.09, 2.267, -1.61]}, {"time": 2.6667, "value": -0.3, "curve": [2.72, -0.13, 2.764, 0]}, {"time": 2.8, "curve": [3.024, 0, 3.579, -4.09]}, {"time": 4.1333, "value": -4.09, "curve": [4.601, -4.09, 4.933, -1.61]}, {"time": 5.3333, "value": -0.3, "curve": [5.556, 0.43, 5.631, -2.06]}, {"time": 6, "value": -2.05, "curve": [6.2, -2.04, 6.43, -3.91]}, {"time": 6.6667, "value": -3.79, "curve": [6.889, -3.67, 7.12, 1.79]}, {"time": 7.3333, "value": 2.13, "curve": [7.574, 2.5, 7.804, 5.24]}, {"time": 8, "value": 4.84, "curve": [8.372, 4.1, 8.636, -0.3]}, {"time": 8.6667, "value": -0.3}]}, "toufa117": {"rotate": [{"value": -1.16, "curve": [0.112, -0.48, 0.203, 0]}, {"time": 0.2667, "curve": [0.49, 0, 1.046, -5.75]}, {"time": 1.6, "value": -5.75, "curve": [1.997, -5.75, 2.311, -3.3]}, {"time": 2.6667, "value": -1.16, "curve": [2.779, -0.48, 2.87, 0]}, {"time": 2.9333, "curve": [3.157, 0, 3.713, -5.75]}, {"time": 4.2667, "value": -5.75, "curve": [4.663, -5.75, 4.978, -3.3]}, {"time": 5.3333, "value": -1.16, "curve": [5.556, 0.18, 5.631, 6.19]}, {"time": 6, "value": 6.21, "curve": [6.2, 6.22, 6.43, 4.35]}, {"time": 6.6667, "value": 4.46, "curve": [6.889, 4.57, 7.12, 10.02]}, {"time": 7.3333, "value": 10.35, "curve": [7.574, 10.7, 7.804, 4.44]}, {"time": 8, "value": 4.03, "curve": [8.372, 3.26, 8.636, -1.16]}, {"time": 8.6667, "value": -1.16}]}, "toufa118": {"rotate": [{"value": -3, "curve": [0.188, -1.39, 0.34, 0]}, {"time": 0.4333, "curve": [0.657, 0, 1.213, -7.83]}, {"time": 1.7667, "value": -7.83, "curve": [2.089, -7.83, 2.367, -5.55]}, {"time": 2.6667, "value": -3, "curve": [2.855, -1.39, 3.007, 0]}, {"time": 3.1, "curve": [3.324, 0, 3.879, -7.83]}, {"time": 4.4333, "value": -7.83, "curve": [4.756, -7.83, 5.033, -5.55]}, {"time": 5.3333, "value": -3, "curve": [5.444, -2.05, 5.465, -10.56]}, {"time": 5.6667, "value": -10.56, "curve": [5.763, -10.56, 5.877, 2.06]}, {"time": 6, "value": 5.46, "curve": [6.105, 8.37, 6.218, 11.08]}, {"time": 6.3333, "value": 11.18, "curve": [6.547, 11.36, 7.459, -1.77]}, {"time": 7.6667, "value": -2.08, "curve": [7.851, -2.35, 8.021, 4.61]}, {"time": 8.1667, "value": 4.15, "curve": [8.469, 3.22, 8.666, -3]}, {"time": 8.6667, "value": -3}]}, "toufa119": {"rotate": [{"value": -7.35, "curve": [0.277, -3.92, 0.508, 0]}, {"time": 0.6333, "curve": [0.857, 0, 1.413, -12.4]}, {"time": 1.9667, "value": -12.4, "curve": [2.21, -12.4, 2.433, -10.24]}, {"time": 2.6667, "value": -7.35, "curve": [2.944, -3.92, 3.175, 0]}, {"time": 3.3, "curve": [3.524, 0, 4.079, -12.4]}, {"time": 4.6333, "value": -12.4, "curve": [4.876, -12.4, 5.1, -10.24]}, {"time": 5.3333, "value": -7.35, "curve": [5.444, -5.98, 5.465, -14.91]}, {"time": 5.6667, "value": -14.9, "curve": [5.763, -14.9, 5.877, -2.29]}, {"time": 6, "value": 1.12, "curve": [6.105, 4.02, 6.218, 6.74]}, {"time": 6.3333, "value": 6.84, "curve": [6.547, 7.01, 7.459, -6.12]}, {"time": 7.6667, "value": -6.43, "curve": [7.851, -6.7, 8.021, 0.26]}, {"time": 8.1667, "value": -0.2, "curve": [8.469, -1.13, 8.666, -7.35]}, {"time": 8.6667, "value": -7.35}]}, "toufa120": {"rotate": [{"value": -8.33, "curve": [0.256, -4.51, 0.51, 0]}, {"time": 0.7667, "curve": [1.214, 0, 1.661, -13.57]}, {"time": 2.1, "value": -13.57, "curve": [2.291, -13.57, 2.478, -11.15]}, {"time": 2.6667, "value": -8.33, "curve": [2.923, -4.51, 3.177, 0]}, {"time": 3.4333, "curve": [3.881, 0, 4.328, -13.57]}, {"time": 4.7667, "value": -13.57, "curve": [4.957, -13.57, 5.144, -11.15]}, {"time": 5.3333, "value": -8.33, "curve": [5.444, -6.67, 5.465, -15.88]}, {"time": 5.6667, "value": -15.87, "curve": [5.763, -15.87, 5.877, -3.25]}, {"time": 6, "value": 0.15, "curve": [6.105, 3.06, 6.218, 5.77]}, {"time": 6.3333, "value": 5.87, "curve": [6.547, 6.05, 7.459, -7.09]}, {"time": 7.6667, "value": -7.4, "curve": [7.851, -7.68, 8.021, -0.72]}, {"time": 8.1667, "value": -1.17, "curve": [8.469, -2.11, 8.666, -8.33]}, {"time": 8.6667, "value": -8.33}]}, "yanbai": {"rotate": [{"time": 7.8333}, {"time": 8.3333, "value": -0.1}, {"time": 8.6667}], "translate": [{"curve": [0.447, 0, 0.895, 2.32, 0.447, 0, 0.895, 4.19]}, {"time": 1.3333, "x": 2.32, "y": 4.19, "curve": [1.784, 2.32, 2.222, 0, 1.784, 4.19, 2.222, 0]}, {"time": 2.6667, "curve": [3.114, 0, 3.561, 2.32, 3.114, 0, 3.561, 4.19]}, {"time": 4, "x": 2.32, "y": 4.19, "curve": [4.45, 2.32, 4.889, 0, 4.45, 4.19, 4.889, 0]}, {"time": 5.3333}, {"time": 5.8333, "x": -9.62, "y": -24.4, "curve": "stepped"}, {"time": 7.8333, "x": -9.62, "y": -24.4}, {"time": 8.3333}], "scale": [{}, {"time": 1.3333, "y": 0.934}, {"time": 2.6667}, {"time": 4, "y": 0.934}, {"time": 5.3333}, {"time": 5.8333, "y": 0.977, "curve": "stepped"}, {"time": 7.8333, "y": 0.977}, {"time": 8.3333}]}, "tongkong": {"translate": [{}, {"time": 1.3333, "x": -6.17, "y": -16.51}, {"time": 2.6667}, {"time": 4, "x": -6.17, "y": -16.51}, {"time": 5.3333}, {"time": 5.8333, "x": -22.35, "y": -71.23, "curve": "stepped"}, {"time": 7.8333, "x": -22.35, "y": -71.23}, {"time": 8.3333}]}, "lian2": {"translate": [{"curve": [0.224, 0, 0.779, 2.66, 0.224, 0, 0.779, -10.34]}, {"time": 1.3333, "x": 2.66, "y": -10.34, "curve": [1.89, 2.66, 2.222, 0, 1.89, -10.34, 2.222, 0]}, {"time": 2.6667, "curve": [2.89, 0, 3.446, 2.66, 2.89, 0, 3.446, -10.34]}, {"time": 4, "x": 2.66, "y": -10.34, "curve": [4.556, 2.66, 5.111, 0, 4.556, -10.34, 5.111, 0]}, {"time": 5.3333}, {"time": 5.8333, "x": -8.63, "y": -36.13, "curve": "stepped"}, {"time": 7.8333, "x": -8.63, "y": -36.13}, {"time": 8.3333}]}, "biyan": {"rotate": [{"time": 7.8333}, {"time": 8.3333, "value": -0.1}, {"time": 8.6667}], "translate": [{}, {"time": 1.3333, "x": -28.97, "y": -50.83}, {"time": 2.6667}, {"time": 3.3333, "x": 3.34, "y": 2.96, "curve": [3.412, 2.58, 3.487, 1.89, 3.412, 1.66, 3.487, 0.23]}, {"time": 3.5667, "x": 0.71, "y": -1.8, "curve": "stepped"}, {"time": 3.9333, "x": 0.71, "y": -1.8, "curve": [4.04, -0.81, 3.978, -28.97, 4.04, -4.41, 3.978, -50.83]}, {"time": 4, "x": -28.97, "y": -50.83}, {"time": 5.3333}, {"time": 5.8333, "x": -20.93, "y": -37.23, "curve": "stepped"}, {"time": 7.8333, "x": -20.93, "y": -37.23}, {"time": 8.3333, "x": -0.35, "y": -0.6, "curve": [8.435, 0.28, 8.556, 0, 8.435, 0.48, 8.556, 0]}, {"time": 8.6667}]}, "erji1": {"rotate": [{"time": 5.8333}, {"time": 7.8333, "value": -0.83}, {"time": 8.1667, "value": -0.1}, {"time": 8.6667}]}, "meimao": {"rotate": [{}, {"time": 1.3333, "value": 9.54}, {"time": 2.6667}, {"time": 4, "value": 9.54}, {"time": 5.3333}, {"time": 5.8333, "value": 19.58, "curve": "stepped"}, {"time": 7.8333, "value": 19.58}, {"time": 8.3333, "value": -0.1}, {"time": 8.6667}], "translate": [{"curve": [0.447, 0, 0.895, 4.74, 0.447, 0, 0.895, 4.5]}, {"time": 1.3333, "x": 4.74, "y": 4.5, "curve": [1.784, 4.74, 2.222, 0, 1.784, 4.5, 2.222, 0]}, {"time": 2.6667, "curve": [3.114, 0, 3.561, 4.74, 3.114, 0, 3.561, 4.5]}, {"time": 4, "x": 4.74, "y": 4.5, "curve": [4.45, 4.74, 4.889, 0, 4.45, 4.5, 4.889, 0]}, {"time": 5.3333}, {"time": 5.8333, "x": -14.38, "y": -22.6}, {"time": 7.8333, "x": -9.03, "y": -19.29}, {"time": 8.3333}], "scale": [{}, {"time": 1.3333, "y": 0.972}, {"time": 2.6667}, {"time": 4, "y": 0.972}, {"time": 5.3333}]}, "toufa121": {"rotate": [{"time": 7.8333}, {"time": 8.3333, "value": -0.1}, {"time": 8.6667}], "translate": [{"curve": [0.447, 0, 0.895, -6.11, 0.447, 0, 0.895, -8.37]}, {"time": 1.3333, "x": -6.11, "y": -8.37, "curve": [1.784, -6.11, 2.222, 0, 1.784, -8.37, 2.222, 0]}, {"time": 2.6667, "curve": [3.114, 0, 3.561, -6.11, 3.114, 0, 3.561, -8.37]}, {"time": 4, "x": -6.11, "y": -8.37, "curve": [4.45, -6.11, 4.889, 0, 4.45, -8.37, 4.889, 0]}, {"time": 5.3333}, {"time": 5.8333, "x": -15.79, "y": -11.18, "curve": "stepped"}, {"time": 7.8333, "x": -15.79, "y": -11.18}, {"time": 8.3333}]}, "shenti5": {"translate": [{"x": 11.96, "y": -0.94, "curve": [0.186, 6.95, 0.31, 1.92, 0.186, -0.71, 0.31, -0.37]}, {"time": 0.3333, "curve": [0.39, -4.68, 1.029, 22.3, 0.39, 0.9, 1.029, -0.7]}, {"time": 1.6667, "x": 24.63, "y": -1.15, "curve": [2.04, 25.99, 2.333, 20.95, 2.04, -1.41, 2.507, -1.15]}, {"time": 2.6667, "x": 11.96, "y": -0.94, "curve": [2.852, 6.95, 2.977, 1.92, 2.852, -0.71, 2.977, -0.37]}, {"time": 3, "curve": [3.056, -4.68, 3.695, 22.3, 3.056, 0.9, 3.695, -0.7]}, {"time": 4.3333, "x": 24.63, "y": -1.15, "curve": [4.706, 25.99, 5.079, 18.97, 4.706, -1.41, 5.079, -1.28]}, {"time": 5.3333, "x": 11.96, "y": -0.94}, {"time": 5.8333, "x": 81.52, "y": 12.63}, {"time": 6.3333, "x": 34.59, "y": 5.07}, {"time": 6.8333, "x": 54.73, "y": 34.14}, {"time": 7.4, "x": 10.44, "y": 26.9}, {"time": 8, "x": 60.23, "y": 18.64}, {"time": 8.6667, "x": 11.96, "y": -0.94}]}, "shenti6": {"translate": [{"x": 11.96, "y": -0.94, "curve": [0.186, 6.95, 0.31, 1.92, 0.186, -0.71, 0.31, -0.37]}, {"time": 0.3333, "curve": [0.39, -4.68, 1.029, 22.3, 0.39, 0.9, 1.029, -0.7]}, {"time": 1.6667, "x": 24.63, "y": -1.15, "curve": [2.04, 25.99, 2.333, 20.95, 2.04, -1.41, 2.507, -1.15]}, {"time": 2.6667, "x": 11.96, "y": -0.94, "curve": [2.852, 6.95, 2.977, 1.92, 2.852, -0.71, 2.977, -0.37]}, {"time": 3, "curve": [3.056, -4.68, 3.695, 22.3, 3.056, 0.9, 3.695, -0.7]}, {"time": 4.3333, "x": 24.63, "y": -1.15, "curve": [4.706, 25.99, 5.079, 18.97, 4.706, -1.41, 5.079, -1.28]}, {"time": 5.3333, "x": 11.96, "y": -0.94}, {"time": 5.8333, "x": 81.52, "y": 12.63}, {"time": 6.3333, "x": 34.59, "y": 5.07}, {"time": 6.8333, "x": 54.73, "y": 34.14}, {"time": 7.4, "x": 10.44, "y": 26.9}, {"time": 8, "x": 60.23, "y": 18.64}, {"time": 8.6667, "x": 11.96, "y": -0.94}]}, "bone2": {"rotate": [{"time": 5.8333}, {"time": 7.8333, "value": -0.83}, {"time": 8.1667, "value": -0.1}, {"time": 8.6667}], "translate": [{"curve": [0.447, 0, 0.895, 204.51, 0.447, 0, 0.895, 0]}, {"time": 1.3333, "x": 204.51, "curve": [1.784, 204.51, 2.222, 0, 1.784, 0, 2.222, 0]}, {"time": 2.6667, "curve": [3.114, 0, 3.561, 204.51, 3.114, 0, 3.561, 0]}, {"time": 4, "x": 204.51, "curve": [4.45, 204.51, 4.889, 0, 4.45, 0, 4.889, 0]}, {"time": 5.3333}]}, "lian3": {"rotate": [{"time": 7.8333}, {"time": 8.3333, "value": -0.1}, {"time": 8.6667}], "translate": [{"time": 5.3333}, {"time": 5.8333, "x": -20.49, "y": -39.98, "curve": "stepped"}, {"time": 7.8333, "x": -20.49, "y": -39.98}, {"time": 8.3333}]}, "target3": {"translate": [{"time": 5.3333, "curve": [5.356, -2.13, 5.627, 0.16, 5.356, -0.95, 5.627, -58.14]}, {"time": 6, "x": -0.05, "y": -58.24, "curve": [6.111, -0.12, 6.222, -0.05, 6.111, -58.26, 6.222, -56.63]}, {"time": 6.3333, "x": -0.05, "y": -54.42, "curve": [6.667, -0.05, 7, -38.51, 6.667, -47.8, 7, -39.52]}, {"time": 7.3333, "x": -38.51, "y": -31.74, "curve": [7.778, -38.51, 8.222, 0, 7.778, -21.37, 8.222, 0]}, {"time": 8.6667}]}, "meimao2": {"translate": [{}, {"time": 1.3333, "x": 0.96, "y": -0.11}, {"time": 2.6667}, {"time": 4, "x": 0.96, "y": -0.11}, {"time": 5.3333}, {"time": 5.8333, "x": -19.81, "y": -33.74, "curve": "stepped"}, {"time": 7.8333, "x": -19.81, "y": -33.74}, {"time": 8.3333}]}, "lian4": {"translate": [{}, {"time": 1.3333, "x": 1.91, "y": 5.01}, {"time": 2.6667}, {"time": 4, "x": 1.91, "y": 5.01}, {"time": 5.3333}, {"time": 5.8333, "x": -25.64, "y": -16.96, "curve": "stepped"}, {"time": 7.8333, "x": -25.64, "y": -16.96}, {"time": 8.3333}]}, "target4": {"translate": [{}, {"time": 1.3333, "x": 1.55}, {"time": 2.6667}, {"time": 4, "x": 1.55}, {"time": 5.3333}, {"time": 6, "x": -5.3, "y": 1.33, "curve": [6.111, -5.3, 6.222, -5.3, 6.111, 2.93, 6.222, 6.13]}, {"time": 6.3333, "x": -5.3, "y": 6.13, "curve": [6.667, -5.3, 7, -10.69, 6.667, 6.13, 7, 2.97]}, {"time": 7.3333, "x": -10.69, "y": 2.1, "curve": [7.778, -10.69, 8.222, -1.55, 7.778, 0.93, 8.222, 0]}, {"time": 8.6667}]}}, "attachments": {"default": {"biyan": {"biyan": {"deform": [{"time": 3.3333, "vertices": [-24.60425, 9.95404, 24.8269, 38.48798, 39.06641, -8.05139, -24.56689, -2.99921, -15.26709, 2.4397, 0, 0, 43.58008, 25.85577, 50.2417, 24.18927, 81.37427, -33.48193, 23.59497, 2.55145, 0, 0, -33.54053, 1.14465]}, {"time": 3.5667, "curve": "stepped"}, {"time": 3.7}, {"time": 3.9333, "vertices": [-24.60425, 9.95404, 24.8269, 38.48798, 39.06641, -8.05139, -24.56689, -2.99921, -15.26709, 2.4397, 0, 0, 43.58008, 25.85577, 50.2417, 24.18927, 81.37427, -33.48193, 23.59497, 2.55145, 0, 0, -33.54053, 1.14465], "curve": "stepped"}, {"time": 5.8333, "vertices": [-24.60425, 9.95404, 24.8269, 38.48798, 39.06641, -8.05139, -24.56689, -2.99921, -15.26709, 2.4397, 0, 0, 43.58008, 25.85577, 50.2417, 24.18927, 81.37427, -33.48193, 23.59497, 2.55145, 0, 0, -33.54053, 1.14465]}, {"time": 6, "offset": 4, "vertices": [-3.6394, -5.18213, 0, 0, -2.67688, 16.81433, -0.36755, 25.23376, 6.53882, 16.25159, 7.53772, 3.2193], "curve": "stepped"}, {"time": 7.6667, "offset": 4, "vertices": [-3.6394, -5.18213, 0, 0, -2.67688, 16.81433, -0.36755, 25.23376, 6.53882, 16.25159, 7.53772, 3.2193]}, {"time": 7.8333, "vertices": [-24.60425, 9.95404, 24.8269, 38.48798, 39.06641, -8.05139, -24.56689, -2.99921, -15.26709, 2.4397, 0, 0, 43.58008, 25.85577, 50.2417, 24.18927, 81.37427, -33.48193, 23.59497, 2.55145, 0, 0, -33.54053, 1.14465]}]}}, "shouyinying": {"shouyinying": {"deform": [{}, {"time": 1.3333, "offset": 10, "vertices": [11.0361, 29.67767, 3.03687, 34.06653, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -20.48334, 33.01192, 10.94318, 8.08449, 29.4245, 16.53258]}, {"time": 2.6667}, {"time": 4, "offset": 10, "vertices": [11.0361, 29.67767, 3.03687, 34.06653, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -20.48334, 33.01192, 10.94318, 8.08449, 29.4245, 16.53258]}, {"time": 5.3333}]}}}}}}}
%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!21 &2100000
Material:
  serializedVersion: 8
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: Fog Of War Plane
  m_Shader: {fileID: 4800000, guid: 55ecf44aac1a32a4e8889f7e1dc1d9c6, type: 3}
  m_Parent: {fileID: 0}
  m_ModifiedSerializedProperties: 0
  m_ValidKeywords: []
  m_InvalidKeywords: []
  m_LightmapFlags: 4
  m_EnableInstancingVariants: 0
  m_DoubleSidedGI: 0
  m_CustomRenderQueue: 3001
  stringTagMap: {}
  disabledShaderPasses: []
  m_LockedProperties: 
  m_SavedProperties:
    serializedVersion: 3
    m_TexEnvs:
    - _MainTex:
        m_Texture: {fileID: 2800000, guid: 9fec90f958d884d40839687ed3810e04, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _MaskTex:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _NoiseTex:
        m_Texture: {fileID: 2800000, guid: 5079108399dc49646bb327183db338fa, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    m_Ints: []
    m_Floats:
    - _BloomBlurRadius: 2
    - _BloomIntensity: 0.5
    - _BloomThreshold: 0.8
    - _BlurIterations: 1
    - _BlurRadius: 1
    - _BlurStrength: 1
    - _CloudSharpness: 60
    - _DistortionAmount: 0.01
    - _EdgeThreshold: 0.1
    - _EnableMask: 1
    - _NoiseScale: 0
    - _NoiseScrollSpeed: 0.1
    - _NoiseStrength: 0.01
    - _ScrollXSpeed: 1
    - _ScrollYSpeed: 0.4
    - _ShadowDirectionX: 0.94
    - _ShadowDirectionY: -0.94
    - _ShadowDistance: 1.82
    - _ShadowIntensity: 1
    - _ShadowOffset: 1.73
    - _Smoothness: 0.1
    - _TileFactor: 9
    m_Colors:
    - _Color: {r: 1, g: 1, b: 1, a: 1}
    - _ShadowColor: {r: 1, g: 0, b: 0, a: 1}
    - _ShadowDirection: {r: 0, g: -1, b: 0, a: 0}
    - _Smooth: {r: 1.05, g: 0.6, b: 0, a: 0}
  m_BuildTextureStacks: []
  m_AllowLocking: 1

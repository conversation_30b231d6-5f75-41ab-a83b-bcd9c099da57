using System;
using Game.Hotfix.Config;
using GameFramework.Event;
using UnityEngine;

namespace Game.Hotfix
 {
     public class WmpCityUiHud : WmpUiHudBase
     {
         [SerializeField]
         public GameObject goLevelA;
         [SerializeField]
         public UIText goTextLevelA;
         [SerializeField]
         public UIText m_goTextNameA;
         
         [SerializeField]
         public GameObject goLevelB;
         [SerializeField]
         public UIText goTextLevelB;
         [SerializeField]
         public UIText m_goTextNameB;
         
         
         
         private Camera m_MainCamera;
         private RectTransform m_SelfRect;
         private RectTransform m_ParentRect;
         private Vector2 m_TempV2;
         
         private Vector2Int m_Pos;
         private int m_Id;
         public void SetData(int id,int x,int y)
         {
             m_Id = id;
             m_Pos = new Vector2Int(x, y);

             var areaId = GameEntry.LogicData.WorldMapData.GetAreaId(m_Pos);
             
             var cityCfg = GameEntry.LDLTable.GetTableById<map_city>(id);
             goTextLevelB.text = goTextLevelA.text = cityCfg.city_lv.ToString();
             m_goTextNameB.text =
                 m_goTextNameA.text = ToolScriptExtend.GetLang(int.Parse(cityCfg.city_name)) + "_"+areaId;

             goLevelA.SetActive(CameraComponent.ZoomLevel <= WorldMapLOD.Level1);
             goLevelB.SetActive(CameraComponent.ZoomLevel > WorldMapLOD.Level1);
         }
         
         public override void Depool()
         {
             if (m_MainCamera == null)
                 m_MainCamera = GameEntry.Camera.WorldMapCamera;
             if(m_ParentRect == null)
                 m_ParentRect = transform.parent.GetComponent<RectTransform>();
             if (m_SelfRect == null)
                 m_SelfRect = transform.GetComponent<RectTransform>();
             
             GameEntry.Event?.Subscribe(OnWorldMapChangeLODArgs.EventId,OnWorldMapChangeLOD);
         }

         private void OnWorldMapChangeLOD(object sender, GameEventArgs e)
         {
             goLevelA.SetActive(CameraComponent.ZoomLevel <= WorldMapLOD.Level1);
             goLevelB.SetActive(CameraComponent.ZoomLevel > WorldMapLOD.Level1);
         }

         public override void Repool()
         {
             GameEntry.Event?.Unsubscribe(OnWorldMapChangeLODArgs.EventId, OnWorldMapChangeLOD);
         }

         private void LateUpdate()
         {
             ResetPos();
         }

         private void ResetPos()
         {
             var screenPoint = RectTransformUtility.WorldToScreenPoint(m_MainCamera, new Vector3(m_Pos.x, 0, m_Pos.y)+GameDefine.HalfOffset);
             RectTransformUtility.ScreenPointToLocalPointInRectangle(m_ParentRect, screenPoint,
                 GameEntry.Camera.UICamera, out m_TempV2);
             m_SelfRect.anchoredPosition = m_TempV2; 
         }
     }
 }
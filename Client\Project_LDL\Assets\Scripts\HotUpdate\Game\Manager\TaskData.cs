using System;
using System.Collections.Generic;
using Game.Hotfix.Config;
using Task;
namespace Game.Hotfix
{
    public class TaskData
    {
      
        public Dictionary<int, List<Task.Task>> m_TaskDic = new();
        public Task.TaskScoreRewards score_rewards = new TaskScoreRewards();
        public List<task_main> configs;
        public void Init(Roledata.RoleTask task)
        {
            if(task==null){
                return;
            }
           foreach (var item in task.TaskList)
           {
              //m_TaskDic[(int)item.Module] = item.ToList<TaskList>();
              if(!m_TaskDic.ContainsKey((int)item.Module)){
                m_TaskDic[(int)item.Module] = new List<Task.Task>();
              }
              if((int)item.Module==3){
                score_rewards = item.ScoreRewards;
              }
              foreach (var item2 in item.Tasks)
              {
                m_TaskDic[(int)item.Module].Add(item2);     
              }                               
           }
           SortTask(1);
           SortTask(2);
           SortTask(3);
        }
        public int GetSortIndex(int taskId){
            task_main config = GetTaskMainById(taskId);
            return config?.priority ?? 0;
        }
        public void SortTask(int module){
            if(m_TaskDic.ContainsKey(module) && m_TaskDic[module].Count > 1)
            {
                m_TaskDic[module].Sort((a, b) =>{
                    int aWeight = GetSortIndex(a.Id);
                    int bWeight = GetSortIndex(b.Id);
                    int aStatus = a.Status == Common.RewardStatus.Receivable ? -1000000 : 0;
                    int bStatus = b.Status == Common.RewardStatus.Receivable ? -1000000 : 0;

                    int aRewarded = a.Status == Common.RewardStatus.Received ? 1000000 : 0;
                    int bRewarded = b.Status == Common.RewardStatus.Received ? 1000000 : 0;
                    
                    return (aWeight+aStatus+aRewarded).CompareTo(bWeight+bStatus+bRewarded);
                }                                                         
                );
            }
            // if(m_TaskDic[module] != null && m_TaskDic[module].Count > 1){
            //     m_TaskDic[module].Sort((a, b) =>
            //         //GetSortIndex(a.Id).CompareTo(GetSortIndex(b.Id))
            //         CompareTaskStatus(a,b)                    
            //     );
            // }
            // if(m_TaskDic[module] != null && m_TaskDic[module].Count > 1){
            //     m_TaskDic[module].Sort((a, b) =>
            //         CompareTaskIndex(a,b)                    
            //     );
            // } 
        }
        public int CompareTaskStatus(Task.Task a, Task.Task b)
        {
            if(a.Status == Common.RewardStatus.Received || b.Status != Common.RewardStatus.Received){
                return 1;
            }

            int result = b.Status.CompareTo(a.Status);
            if (result == 0)
                return 0;
            else
                return -1;
        }
        public int CompareTaskIndex(Task.Task a, Task.Task b)
        {

            if(a.Status == Common.RewardStatus.Unfinished || b.Status == Common.RewardStatus.Unfinished){
                int result = GetSortIndex(b.Id).CompareTo(GetSortIndex(a.Id));           
                return result;
            }      
            else{
                return 0;
            }
            
         
        }
        public void ChangeTask(TaskChange change){
            //daily
            //if(change.Module != null){
                foreach (var item in change.Task)
                {
                    Task.Task newTask = item;
                    var localData = GetTaskByID((int)change.Module,newTask.Id);
                    localData.Process = newTask.Process;
                    localData.Status = newTask.Status;
                }
            //}
            SortTask((int)change.Module);
            GameEntry.Event.Fire(TaskChangeEventArgs.EventId, TaskChangeEventArgs.Create());
        }
        public int GetTaskBoxCount(){
            if(score_rewards == null){
                return 0;
            }
            return score_rewards.ReceivedIds.Count;
        }
        public void TaskListReq()
        {
            var req = new TaskListReq();
            req.Module =  PbGameconfig.taskmodule._1;
            GameEntry.LDLNet.Send(Protocol.MessageID.TaskList, req, (message) =>
            {
                var resp = (TaskListResp)message;
                //RepeatedField<TaskList> list = resp.TaskList;
                var list = resp.TaskList;
                
                //action?.Invoke(resp);
                //GameEntry.UI.OpenUIForm(EnumUIForm.UIRewardGetForm, rewards);                          
            });
        }
        //领取任务
        public void OnTaskReceiveReq(PbGameconfig.taskmodule module,int id,Action<List<reward>> callBack = null){
            var localData = GetTaskByID((int)module,id);
            if(localData.Status != Common.RewardStatus.Receivable){
                return;
            }
            var req = new TaskReceiveReq();
            req.Module = module;
            if (module == PbGameconfig.taskmodule._3)
            {
            req.Id = 0;
            }
            else
            {
              req.Id = id;  
            }
            
            GameEntry.LDLNet.Send(Protocol.MessageID.TaskReceive,req,(message) =>{
                var resp = (TaskReceiveResp)message;
                List<reward> rewards = new();
                foreach (var item in resp.Articles)
                {
                    rewards.Add(new reward()
                    {
                        item_id = (itemid)item.Code,
                        num = item.Amount
                    });
                }
                callBack?.Invoke(rewards);
                GameEntry.UI.OpenUIForm(EnumUIForm.UIRewardGetForm, rewards);
                GameEntry.Event.Fire(TaskChangeEventArgs.EventId, TaskChangeEventArgs.Create());
            });
        } 
        //领取积分宝箱
        public void OnTaskReceiveScoreRewardsReq(){
            var req = new TaskReceiveScoreRewardsReq();
            req.Module = PbGameconfig.taskmodule._3;
            req.Id = 0;
            GameEntry.LDLNet.Send(Protocol.MessageID.TaskReceiveScoreRewards,req,(message) =>{
                if(message != null){
                var resp = (TaskReceiveScoreRewardsResp)message;
                List<reward> rewards = new();
                score_rewards = resp.ScoreRewards;
                foreach (var item in resp.Articles)
                {
                    rewards.Add(new reward()
                    {
                        item_id = (itemid)item.Code,
                        num = item.Amount
                    });
                }
                GameEntry.UI.OpenUIForm(EnumUIForm.UIRewardGetForm, rewards); 
                }
                GameEntry.Event.Fire(TaskChangeEventArgs.EventId, TaskChangeEventArgs.Create());               
            });
        }
        public Task.Task GetMainTask(bool main = false){
            Task.Task mainTask = new Task.Task();
            if (main)
            {
                foreach (var item in m_TaskDic[(int)PbGameconfig.taskmodule._2])
                {
                    var config = GetTaskMainById(item.Id);
                    if (config.task_show_type == taskshowtype.taskshowtype_2)
                    {
                        return item;
                    }
                }
                return mainTask;
            }
            //章节任务
            foreach (var item in m_TaskDic[(int)PbGameconfig.taskmodule._1])
            {
                //var config = GetTaskMainById(item.Id);
                if (item.Status == Common.RewardStatus.Receivable)
                {
                    return item;
                }
            }
            //主线任务
            foreach (var item in m_TaskDic[(int)PbGameconfig.taskmodule._2])
            {
                var config = GetTaskMainById(item.Id);
                if (config.task_show_type == taskshowtype.taskshowtype_2 && item.Status == Common.RewardStatus.Receivable)
                {
                    return item;
                }
            }
            //支线任务
            foreach (var item in m_TaskDic[(int)PbGameconfig.taskmodule._2])
            {
                var config = GetTaskMainById(item.Id);
                if (config.task_show_type == taskshowtype.taskshowtype_3 && item.Status == Common.RewardStatus.Receivable)
                {
                    return item;
                }
            }
            //都没有返回第一个任务
            foreach (var module in m_TaskDic)
            {
              foreach (var item in module.Value)
              {
                return item;
              }  
            }
            return mainTask;
        }
        
        public List<Task.Task> GetTaskListByModule(int module)
        {
            SortTask(module);
            //支线任务
            if (module == (int)PbGameconfig.taskmodule._2)
            {
                List<Task.Task> canRewardlist = new List<Task.Task>();
                List<Task.Task> canNotRewardlist = new List<Task.Task>();
                foreach (var item in m_TaskDic[module])
                {
                    if (GetTaskMainById(item.Id).task_show_type == taskshowtype.taskshowtype_3)
                    {
                        if (item.Status == Common.RewardStatus.Receivable)
                        {
                            if (canRewardlist.Count < 3)
                            {
                                canRewardlist.Add(item);
                            }
                        }
                        else
                        {
                            if (canNotRewardlist.Count < 5)
                            {
                                canNotRewardlist.Add(item);
                            }
                        }
                    }
                }
                canRewardlist.AddRange(canNotRewardlist);
                return canRewardlist;
            }
            if (module == (int)PbGameconfig.taskmodule._1)
            {
                for (int i = m_TaskDic[module].Count - 1; i >= 0; i--)
                {
                    if (m_TaskDic[module][i].Status == Common.RewardStatus.Received) // 删除已完成任务
                    {
                        m_TaskDic[module].RemoveAt(i);
                    }
                }
            }
            if (m_TaskDic.ContainsKey(module))
            {
                return m_TaskDic[module];
            }
            return new List<Task.Task>();
        }
        public Task.Task GetTaskByID(int module,int taskID){
            foreach (var item in m_TaskDic[module])
            {
              if(item.Id == taskID){
                return item;
              }  
            }
            Task.Task task = new Task.Task();
            task.Process = 0;
            task.Status = Common.RewardStatus.Unfinished;
            return task;
        }
        public task_main GetTaskMainById(int taskId){
            if(configs == null){
                configs = GameEntry.LDLTable.GetTable<task_main>();   
            }
            foreach (var item in configs)
            {
                if(item.task_id == taskId){
                    return item;
                }
            }
            return default;
        }
        public List<task_main> GetTaskConfigByType(int clickTabIndex)
        {        
            List<task_main> config = new List<task_main>();
            configs = GameEntry.LDLTable.GetTable<task_main>();   
            foreach (var item in configs)
            {
                if((int)item.task_module == clickTabIndex+1 && isOpenTask(item.task_id)){
                    config.Add(item);
                }
            }
            return config;
        }
        public bool isOpenTask(int taskID){
            return true;
        }

        public bool isOpenDailyTask(){
            var config = GameEntry.LDLTable.GetTableById<task_setting>(2001);
            if(config != null){
               return ToolScriptExtend.GetDemandUnlock(int.Parse(config.value));
            }
            return false;
        }
        public bool isOpenChapterTask(){
            if (m_TaskDic.ContainsKey(1) && m_TaskDic[1].Count > 0)
            {
                return true;
            }
            return false;
        }
        /// <summary>
        /// 判断是否有已完成但未领取的任务（不包含每日任务）
        /// </summary>
        /// <returns>如果有可领取的任务返回true，否则返回false</returns>
        public bool HasMainTask()
        {
            // 检查所有模块的任务（除了每日任务模块）
            foreach (var modulePair in m_TaskDic)
            {
                // 跳过每日任务模块(模块3)
                if (modulePair.Key == (int)PbGameconfig.taskmodule._3)
                    continue;
                
                foreach (var task in modulePair.Value)
                {
                    // 检查任务状态是否为可领取
                    if (task.Status == Common.RewardStatus.Receivable)
                    {
                        return true;
                    }
                }
            }
            
            return false;
        }

        /// <summary>
        /// 判断是否有已完成但未领取的任务（包含所有任务类型）
        /// </summary>
        /// <returns>如果有可领取的任务返回true，否则返回false</returns>
        public bool HasAnyDailyTask()
        {
            if (!GameEntry.LogicData.TaskData.isOpenDailyTask())
            {
                return false;
            }
            // 检查所有模块的任务
            foreach (var modulePair in m_TaskDic[3])
            {
                // foreach (var task in modulePair.Value)
                // {
                    // 检查任务状态是否为可领取
                    if (modulePair.Status == Common.RewardStatus.Receivable)
                    {
                        return true;
                    }
                //}
            }
            
            // 检查积分宝箱是否可领取
            if (HasCompletedScoreBox())
            {
                return true;
            }
            
            return false;
        }

        /// <summary>
        /// 检查是否有可领取的积分宝箱
        /// </summary>
        /// <returns>如果有可领取的积分宝箱返回true，否则返回false</returns>
        public bool HasCompletedScoreBox()
        {
            // 如果没有每日任务模块，直接返回false
            if (!m_TaskDic.ContainsKey((int)PbGameconfig.taskmodule._3))
            {
                return false;
            }
            
            // 计算已完成每日任务的积分总和
            int totalScore = 0;
            var dailyTasks = m_TaskDic[(int)PbGameconfig.taskmodule._3];
            foreach (var task in dailyTasks)
            {
                if (task.Status == Common.RewardStatus.Received)
                {
                    var config = GetTaskMainById(task.Id);
                    if (config != null && config.task_reward.Count > 0)
                    {
                        totalScore += (int)config.task_reward[0].num;
                    }
                }
            }
            
            // 获取积分宝箱配置
            List<task_daily> dailyConfigs = GameEntry.LDLTable.GetTable<task_daily>();
            if (dailyConfigs == null)
            {
                return false;
            }
            
            // 检查是否有可领取的宝箱
            int receivedBoxCount = score_rewards?.ReceivedIds.Count ?? 0;
            for (int i = receivedBoxCount; i < dailyConfigs.Count; i++)
            {
                if (totalScore >= dailyConfigs[i].reward_level)
                {
                    return true;
                }
            }
            
            return false;
        }

        /// <summary>
        /// 获取每日任务中可领取奖励的任务数量
        /// </summary>
        /// <returns>可领取奖励的每日任务数量</returns>
        public int GetReceivableDailyTaskCount()
        {
            int receivableCount = 0;
            
            // 检查是否有每日任务模块
            if (!m_TaskDic.ContainsKey((int)PbGameconfig.taskmodule._3))
            {
                return receivableCount;
            }
            
            // 遍历每日任务列表
            var dailyTasks = m_TaskDic[(int)PbGameconfig.taskmodule._3];
            foreach (var task in dailyTasks)
            {
                // 统计可领取奖励的任务
                if (task.Status == Common.RewardStatus.Receivable)
                {
                    receivableCount++;
                }
            }
            
            return receivableCount;
        }

        /// <summary>
        /// 获取有已完成任务的模块ID
        /// </summary>
        /// <returns>有已完成任务的模块ID，如果没有则返回-1</returns>
        public int GetCompletedTaskModule()
        {
            
            // 检查章节任务（模块1）
            if (HasCompletedTaskInModule(1))
            {
                return 1;
            }
            // 检查主线任务（模块2）
            if (HasCompletedTaskInModule(2))
            {
                return 2;
            }
                                 
            // 检查每日任务（模块3）
            if (HasCompletedTaskInModule(3))
            {
                return 3;
            }
            
            // 如果没有已完成的任务，返回-1
            return -1;
        }

        /// <summary>
        /// 检查指定模块是否有已完成但未领取的任务
        /// </summary>
        /// <param name="moduleId">任务模块ID</param>
        /// <returns>如果有可领取的任务返回true，否则返回false</returns>
        public bool HasCompletedTaskInModule(int moduleId)
        {
            // 检查模块是否存在
            if (!m_TaskDic.ContainsKey(moduleId))
            {
                return false;
            }
            
            // 检查模块中的任务
            foreach (var task in m_TaskDic[moduleId])
            {
                if (task.Status == Common.RewardStatus.Receivable)
                {
                    return true;
                }
            }
            
            // 特殊处理每日任务的积分宝箱
            if (moduleId == (int)PbGameconfig.taskmodule._3)
            {
                return HasCompletedScoreBox();
            }
            
            return false;
        }

        /// <summary>
        /// 领取一个已完成的主线任务奖励
        /// </summary>
        /// <param name="callback">领取完成后的回调，参数为获得的奖励列表，如果没有可领取的任务则为null</param>
        public void ClaimOneCompletedMainTask(Action<List<reward>> callback = null)
        {
            // 获取推送的主界面任务
            Task.Task taskToReceive = GetMainTask();
            task_main config = GetTaskMainById(taskToReceive.Id);
            if(config == null){
                return;
            }
            PbGameconfig.taskmodule module =(PbGameconfig.taskmodule)config.task_module; // 主线任务模块

            // // 检查主线任务模块
            // if (m_TaskDic.ContainsKey((int)module))
            // {
            //     // 先按优先级排序任务
            //     SortTask((int)module);

            //     // 查找第一个可领取的任务
            //     foreach (var task in m_TaskDic[(int)module])
            //     {
            //         if (task.Status == Common.RewardStatus.Receivable && taskshowtype.taskshowtype_2 == GetTaskMainById(task.Id).task_show_type)
            //         {
            //             taskToReceive = task;
            //             break;
            //         }
            //     }
            // }

            // 如果没有可领取的任务，直接返回
            if (taskToReceive.Status == Common.RewardStatus.Receivable)
            {

                // 领取任务奖励
                OnTaskReceiveReq(module, taskToReceive.Id, (rewards) =>
                {
                    callback?.Invoke(rewards);
                });

            }
            else
            {
                GameEntry.UI.OpenUIForm(EnumUIForm.UIFlyTextForm, new FlyTextParams()
                    {
                        Content = "前往"
                    });
                callback?.Invoke(null);
                return;
            }                   
        }
    }
}
 

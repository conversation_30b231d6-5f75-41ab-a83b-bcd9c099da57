using System.Collections.Generic;
using System.Linq;
using Game.Hotfix.Config;
using UnityEngine;

namespace Game.Hotfix
{
    public class WMCmpCityDrawer : IWMCmp
    {

        private Dictionary<Vector2Int, List<int>> m_ShowListDic = new Dictionary<Vector2Int, List<int>>();
        private HashSet<Vector2Int> m_ShowList = new HashSet<Vector2Int>();

        private WorldMapComponent m_WorldMapComponent;

        public WMCmpCityDrawer(WorldMapComponent component)
        {
            m_WorldMapComponent = component;
        }

        public void Init()
        {
            m_WorldMapComponent.OnCameraMoveCall += OnCameraMove;
        }

        public void UnInit()
        {
            m_WorldMapComponent.OnCameraMoveCall -= OnCameraMove;
        }

        private void OnCameraMove()
        {
            m_WorldMapComponent.GetInShowLIstByGrid(GameDefine.WorldMapDataGridSize, 0, WorldMapLOD.Level1,
                WorldMapLOD.Level4, out List<Vector2Int> showListNew);

            List<Vector2Int> add = showListNew.Except(m_ShowList).ToList();
            List<Vector2Int> remove = m_ShowList.Except(showListNew).ToList();

            foreach (var t in remove)
            {
                Remove(t);
            }

            foreach (var t in add)
            {
                Add(t);
            }
        }

        private void Add(Vector2Int grid)
        {
            if (m_ShowList.Add(grid))
            {
                if (!m_ShowListDic.ContainsKey(grid))
                {
                    WorldMapGridData gridData = GameEntry.LogicData.WorldMapData.GetGridDataByPos(grid);
                    var list = gridData.CityData;
                    if (list != null)
                    {
                        var idList = new List<int>();
                        for (int i = 0; i < list.Count; i++)
                        {
                            Vector3Int data = list[i];
                            int cityId = data.z;
                            var path = GetPathById(cityId);

                            ED_WorldMapCity param =
                                new ED_WorldMapCity(Game.GameEntry.Entity.GenerateSerialId(), cityId);
                            param.Position = new Vector3(data.x, 0, data.y);
                            int id = GameEntry.Entity.ShowWorldMapDisplay(path, param, typeof(EL_WorldMapCity));
                            idList.Add(id);
                        }
                    
                        m_ShowListDic.Add(grid, idList);
                    }
                }
            }
        }

        private void Remove(Vector2Int grid)
        {
            if (m_ShowList.Remove(grid))
            {
                if (m_ShowListDic.TryGetValue(grid, out var idList))
                {
                    for (int i = 0; i < idList.Count; i++)
                    {
                        var id = idList[i];
                        GameEntry.Entity.HideEntity(id);
                    }

                    m_ShowListDic.Remove(grid);
                }
            }
        }

        private string GetPathById(int id)
        {
            var rowData = GameEntry.LDLTable.GetTableById<map_city>(id);
            if (rowData != null)
            {
                var config = GameEntry.LDLTable.GetTableById<map_buildpre>(rowData.city_pre);
                return config?.pre;
            }
            return string.Empty;
        }
    }
}
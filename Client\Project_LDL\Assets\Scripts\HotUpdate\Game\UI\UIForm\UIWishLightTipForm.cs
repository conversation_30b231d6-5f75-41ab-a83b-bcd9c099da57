using System;
using System.Collections.Generic;
using System.Linq;
using Game.Hotfix.Config;
using UnityEngine;
using UnityEngine.UI;
using attrbuild = PbGameconfig.attrbuild;

namespace Game.Hotfix
{
    public partial class UIWishLightTipForm : UGuiFormEx
    {
        private Dictionary<attrbuild,ulong> powerData;
        
        protected override void OnInit(object userData)
        {
            base.OnInit(userData);
            m_goPrefab.SetActive(false);
            InitBind();
        }

        protected override void OnOpen(object userData)
        {
            base.OnOpen(userData);
            powerData = new Dictionary<attrbuild, ulong>();
            GameEntry.LogicData.RoleData.RequestRoleQueryPowerBuild((resp) =>
            {
                foreach (var data in resp.Builds)
                {
                    powerData.TryAdd(data.Type, data.Power);
                }
                InitPageView();
            });
        }

        private void InitPageView()
        {
            if (!ToolScriptExtend.GetTable<activity_power>(out var data)) return;
            var mainCity = GameEntry.LogicData.BuildingData.GetBuildingModuleById(101);
            var level = mainCity.LEVEL;
            var target = data.FirstOrDefault(x => x.player_level == level) ?? data.LastOrDefault();
            var powerDic = new List<ValueTuple<string,int>>();
            if (target != null)
            {
                //英雄
                if (target.hero_power > 0)
                {
                    powerDic.Add(("hero_power",target.hero_power));
                }
                
                //建筑
                if (target.build_power > 0)
                {
                    powerDic.Add(("build_power",target.build_power));
                }
                
                //科技
                if (target.tech_power > 0)
                {
                    powerDic.Add(("tech_power",target.tech_power));
                }
                
                //训练士兵
                if (target.soldier_power > 0)
                {
                    powerDic.Add(("soldier_power",target.soldier_power));
                }
                
                //无人机
                if (target.uav_power > 0)
                {
                    powerDic.Add(("uav_power",target.uav_power));
                }
            }

            var root = m_scrollviewList.content.transform;
            ToolScriptExtend.RecycleOrCreate(m_goTipItem,m_scrollviewList.content.transform,powerDic.Count);
            for (var i = 0; i < powerDic.Count; i++)
            {
                var info = powerDic[i];
                var child = root.GetChild(i);
                SetTipInfo(i,child.gameObject,info.Item1,(ulong)info.Item2);
            }
            
            m_scrollviewList.normalizedPosition = new Vector2(0, 1);
        }
        
        protected override void OnClose(bool isShutdown, object userData)
        {
            base.OnClose(isShutdown, userData);
        }

        public override void OnRefresh(object userData)
        {
            base.OnRefresh(userData);
        }

        private void OnBtnCloseClick()
        {
            Close();
        }

        private void SetTipInfo(int index,GameObject obj,string type,ulong value)
        {
            var root = obj.transform;
            var icon = root.Find("icon").GetComponent<UIImage>();
            var mark = root.Find("icon/mark");
            var btn = root.Find("btn").GetComponent<UIButton>();
            var desc = root.Find("desc").GetComponent<UIText>();
            var slider = root.Find("slider").GetComponent<Slider>();
            var sliderTxt = root.Find("slider/value").GetComponent<UIText>();
            
            mark.gameObject.SetActive(type == "hero_power");
            //图标
            var iconPath = "xwddh_tujing_icon1.png";
            ulong curValue = 0;
            var descId = 1100494;
            if (type == "hero_power")//英雄
            {
                iconPath = "xwddh_tujing_icon1.png";
                powerData.TryGetValue(attrbuild.Team, out curValue);
                descId = 1100494;
            }
            else if (type == "build_power")//建筑
            {
                iconPath = "xwddh_tujing_icon2.png";
                powerData.TryGetValue(attrbuild.Build, out curValue);
                descId = 1100495;
            }
            else if (type == "tech_power") //科技
            {
                iconPath = "xwddh_tujing_icon4.png";
                powerData.TryGetValue(attrbuild.Tech, out curValue);
                descId = 1100496;
            }
            else if (type == "soldier_power")//训练士兵
            {
                iconPath = "xwddh_tujing_icon3.png";
                powerData.TryGetValue(attrbuild.Soilder, out curValue);
                descId = 1100497;
            }
            else if (type == "uav_power")//无人机
            {
                iconPath = "xwddh_tujing_icon5.png";
                powerData.TryGetValue(attrbuild.Uav, out curValue);
                descId = 1100498;
            }
            icon.SetImage($"Sprite/ui_shijian/{iconPath}");
            var txtHex = curValue >= value ? "#3FFF75" : "#FFFFFF";
            sliderTxt.text = $"<color={txtHex}>{ToolScriptExtend.FormatNumberWithSeparator(curValue)}</color>/{ToolScriptExtend.FormatNumberWithSeparator(value)}";
            float ratio = 0;
            if (curValue >= value)
            {
                ratio = 1;
            }
            else
            {
                ratio = curValue*1.0f/value;
            }
            slider.value = ratio;
            var hex = "#5C504A";
            if (ratio < 0.3f)
            {
                hex = "#ff5400";
            }
            else if (ratio >= 0.3f && ratio < 1)
            {
                hex = "#ff9c00";
            }
            else
            {
                hex = "#009d42";
            }
            desc.text = $"{ToolScriptExtend.GetLang(descId)} <color={hex}>{Mathf.FloorToInt(ratio*100)}%</color>";
            ToolScriptExtend.BindBtnLogic(btn, () =>
            {
                JumpToUpPower(type);
            });
        }
        
        //跳转指定界面进行战力提升
        private void JumpToUpPower(string type)
        {
            Close();
            GameEntry.UI.CloseUIForm(EnumUIForm.UISpecialEventForm);
            if (type == "hero_power")//英雄
            {
                GameEntry.UI.OpenUIForm(EnumUIForm.UIHeroForm);
            }
            else if (type == "build_power")//建筑
            {
               
            }
            else if (type == "tech_power") //科技
            {
               
            }
            else if (type == "soldier_power")//训练士兵
            {
               
            }
            else if (type == "uav_power")//无人机
            {
                
            }
        }
    }
}

using System.Collections;
using System.Collections.Generic;
using Game.Hotfix.Config;
using GameFramework.Event;
using Team;
using UnityEngine;
using UnityEngine.UI;
using UnityGameFramework.Runtime;

namespace Game.Hotfix
{
    
    
    
    public partial class UIWorldMapTeamSelectForm : UGuiFormEx
    {
        private List<TeamType> m_TeamTypes = new List<TeamType>()
        {
            TeamType.Common1,
            TeamType.Common2,
            TeamType.Common3,
            TeamType.Common4,
        };

        private List<RectTransform> m_AnchorPos;
        
        private int m_CurTeamIndex = 0;
        
        protected override void OnInit(object userData)
        {
            base.OnInit(userData);

            InitBind();

            m_AnchorPos = new List<RectTransform>()
            {
                m_goAnchorPos1.GetComponent<RectTransform>(),
                m_goAnchorPos2.GetComponent<RectTransform>(), 
                m_goAnchorPos3.GetComponent<RectTransform>(), 
                m_goAnchorPos4.GetComponent<RectTransform>()
            };
        }

        protected override void OnOpen(object userData)
        {
            base.OnOpen(userData);

            //TODO 根据优先级 默认选择(包含英雄并且没有执行任务)
            m_CurTeamIndex = 0;
            
            UpdateTeamItems();
            
            GameEntry.Event.Subscribe(TeamChangeEventArgs.EventId, OnTeamChange);
        }

        private void OnTeamChange(object sender, GameEventArgs e)
        {
            UpdateTeamItems();
        }

        protected override void OnClose(bool isShutdown, object userData)
        {
            base.OnClose(isShutdown, userData);
            GameEntry.Event.Unsubscribe(TeamChangeEventArgs.EventId, OnTeamChange);
        }

        public override void OnRefresh(object userData)
        {
            base.OnRefresh(userData);
        }

        private void UpdateInfoPanel()
        {
            var teamType = m_TeamTypes[m_CurTeamIndex];
            var teamDic = GameEntry.LogicData.TeamData.GetTeamDic(teamType);

            if (teamDic.Count <= 0)
            {
                m_rectMenu.gameObject.SetActive(false);
                m_rectCreate.gameObject.SetActive(true);
                m_rectCreate.GetComponent<RectTransform>().anchoredPosition =
                    m_AnchorPos[m_CurTeamIndex].anchoredPosition;
                return;
            }
            m_rectMenu.gameObject.SetActive(true);
            m_rectCreate.gameObject.SetActive(false);
            
            m_goArrMenu.GetComponent<RectTransform>().position =
                m_AnchorPos[m_CurTeamIndex].position;
            
            var parent = m_transHeros;
            ToolScriptExtend.RecycleOrCreate(m_goHeroItem, parent, 5);
            
            for (int i = 0; i < 5; i++)
            {
                var item = parent.GetChild(i);
                UIHeroItem heroItem = item.transform.Find("UIHeroItem")?.GetComponent<UIHeroItem>();
                Slider slider = item.transform.Find("slider")?.GetComponent<Slider>();
                UIText txtSoilderCnt = item.transform.Find("txtSoilderCnt")?.GetComponent<UIText>();

                if (teamDic.TryGetValue((EnumBattlePos)(i + 1),out int heroId))
                {
                    heroItem.gameObject.SetActive(true);
                    slider.gameObject.SetActive(true);
                    txtSoilderCnt.gameObject.SetActive(true);

                    HeroModule heroModule = GameEntry.LogicData.HeroData.GetHeroModule((itemid)heroId);
                    heroItem.Refresh(heroModule);
                    heroItem.SetTeamIndex(null);
                    slider.value = 0.9f;
                    txtSoilderCnt.text = "999";
                }
                else
                {
                    heroItem.gameObject.SetActive(false);
                    slider.gameObject.SetActive(false);
                    txtSoilderCnt.gameObject.SetActive(false);
                }

            }


            //刷新面板
        }
        
        private void UpdateTeamItems()
        {
            int teamTotalCount = 4;
            var parent = m_goTeamList.transform;
            ToolScriptExtend.RecycleOrCreate(m_goTeamItem, parent, teamTotalCount);
            for (int i = 0; i < teamTotalCount; i++)
            {
                bool isLock = !GameEntry.LogicData.BuildingData.GetTeamIsUnlockByIndex(i);
                bool isEmpty = false;
                var item = parent.GetChild(i);
                var goLock = item.transform.Find("goLock");
                var goUnlock = item.transform.Find("goUnlock");
                goLock.gameObject.SetActive(isLock);
                goUnlock.gameObject.SetActive(!isLock);

                UIText teamId = item.transform.Find("teamBg/txtTeamId")?.GetComponent<UIText>();
                if (teamId != null)
                    teamId.text = (i + 1).ToString();
                
                if (!isLock)
                {
                    var goSelect = item.transform.Find("goUnlock/goSelect");
                    goSelect.gameObject.SetActive(i == m_CurTeamIndex);
                    
                    UIImage bg = item.transform.Find("goUnlock/goBgColor")?.GetComponent<UIImage>();
                    var goEmpty = item.transform.Find("goUnlock/goEmpty");
                    var goNotEmpty = item.transform.Find("goUnlock/goNotEmpty");
                    
                    //已经解锁
                    var team = GameEntry.LogicData.TeamData.GetTeam(m_TeamTypes[i]);
                    var leader = GameEntry.LogicData.TeamData.GetTeamLeader(team);
                    
                    isEmpty = leader == null;
                    goEmpty.gameObject.SetActive(isEmpty);
                    goNotEmpty.gameObject.SetActive(!isEmpty);
                    if (leader != null)
                    {
                        var heroModule = GameEntry.LogicData.HeroData.GetHeroModule((itemid)leader.HeroId);
                        if (heroModule != null)
                        {
                            bg.color = GetColorByQuality(heroModule.Quality);
                            
                            UIImage head = item.transform.Find("goUnlock/goNotEmpty/imgHead")?.GetComponent<UIImage>();
                            if (head != null)
                            {
                                head.SetImage(heroModule.HeroHead);
                            }
                        }
                        else
                        {
                            bg.color = GetColorByQuality(0);
                        }
                    }
                    else
                    {
                        //英雄数量为0
                        bg.color = GetColorByQuality(0); 
                    }

                    var btn = item.transform.GetComponent<UIButton>();
                    if (btn != null)
                    {
                        var index = i;
                        btn.onClick.RemoveAllListeners();
                        btn.onClick.AddListener(() =>
                        {
                            if (isLock)
                            {
                                GameEntry.UI.OpenUIForm(EnumUIForm.UIFlyTextForm, new FlyTextParams()
                                {
                                    Content = "队列未解锁",
                                });
                                Debug.LogError("锁住");
                            }
                            else
                            {
                                m_CurTeamIndex = index;
                                UpdateTeamItems();
                            }
                        });
                    }
                }
            }
            
            UpdateInfoPanel();
            
        }
        
        private void OnBtnInfoClick()
        {

        }

        private void OnBtnConfirmClick()
        {

        }

        private void OnBtnSettingClick()
        {
            UITeamFormParam param = new()
            {
                Index = m_CurTeamIndex,
                TeamFormType = UITeamFormType.Common
            };
            GameEntry.UI.OpenUIForm(EnumUIForm.UITeamForm, param);
        }

        private void OnBtnCreateClick()
        {
            UITeamFormParam param = new()
            {
                Index = m_CurTeamIndex,
                TeamFormType = UITeamFormType.Common
            };
            GameEntry.UI.OpenUIForm(EnumUIForm.UITeamForm, param);
        }

        private void OnBtnStaminaAddClick()
        {

        }

        private Color GetColorByQuality(int quality)
        {
            if (quality == 0)
                return new Color(151/255f, 149/255f, 156/255f);
            if (quality == 1)
                return new Color(255/255f, 255/255f, 255/255f);
            if (quality == 2)
                return new Color(12/255f, 191/255f, 121/255f);
            if (quality == 3)
                return new Color(20/255f, 165/255f, 210/255f);
            else if (quality == 4)
                return new Color(174/255f, 81/255f, 227/255f);
            else if (quality == 5)
                return new Color(244/255f, 161/255f, 64/255f);
            else if (quality == 6)
                return new Color(232/255f, 91/255f, 81/255f);
            return Color.black;
        }
    }
}

using System;
using System.Collections.Generic;
using Game.Hotfix.Config;
using Google.Protobuf.Collections;
using Uav;

namespace Game.Hotfix
{
    public class UAVData
    {
        private UAVModule m_UAVModule;
        private Dictionary<componenttype, Dictionary<int, component_level>> componentLevelDic;
        public UAVData()
        {
        }
        
        public void Init(UAV uav)
        {
            if (uav == null)
            {
                return;
            }

            m_UAVModule = new UAVModule(uav);
            InitConfig();
        }

        private void InitConfig()
        {
            componentLevelDic = new Dictionary<componenttype, Dictionary<int, component_level>>();
            List<component_level> componentLevels = GameEntry.LDLTable.GetTable<component_level>();
            for (int i = 0; i < componentLevels.Count; i++)
            {
                component_level componentLevelCfg = componentLevels[i];
                if (!componentLevelDic.TryGetValue(componentLevelCfg.component_type,out Dictionary<int, component_level> levelCfgDic))
                {
                    levelCfgDic = new Dictionary<int, component_level>();
                }
                levelCfgDic[componentLevelCfg.level] = componentLevelCfg;
                componentLevelDic[componentLevelCfg.component_type] = levelCfgDic;
            }
        }
        
        public UAVModule UavModule
        {
            get
            {
                return m_UAVModule;
            }  
        }

        #region 外部调用

        
        public uav_level GetUavLevelConfig(int congfigId)
        {
            uav_level config = GameEntry.LDLTable.GetTableById<uav_level>((int)congfigId);
            return config;
        }

        public bool IsUnLockSkinLevel(int level = 0)
        {
            List<uav_skin> uavSkins = GameEntry.LDLTable.GetTable<uav_skin>();
            for (var i = 0; i < uavSkins.Count; i++)
            {
                uav_skin uavSkin = uavSkins[i];
                string[] demandList = uavSkin.unclock.Split("|");
                if (demandList.Length > 0)
                {
                    int demandType = int.Parse(demandList[0]);
                    if (demandType == 1)
                    {
                        int demandValue = int.Parse(demandList[1]);
                        if (level == demandValue)
                        {
                            return true;
                        }
                    }
                }
            }
            return false;
        }
        
        public uav_skin GetSkinConfigByUnLockLevel(int level = 0)
        {
            List<uav_skin> uavSkins = GameEntry.LDLTable.GetTable<uav_skin>();
            for (var i = 0; i < uavSkins.Count; i++)
            {
                uav_skin uavSkin = uavSkins[i];
                string[] demandList = uavSkin.unclock.Split("|");
                if (demandList.Length > 0)
                {
                    int demandType = int.Parse(demandList[0]);
                    if (demandType == 1)
                    {
                        int demandValue = int.Parse(demandList[1]);
                        if (level == demandValue)
                        {
                            return uavSkin;
                        }
                    }
                }
            }
            return null;
        }
        
        /// <summary>
        /// 是否是技能升星等级
        /// </summary>
        /// <param name="level"></param>
        /// <returns></returns>
        public bool IsUpSkillStarLevel(int level = 0)
        {
            List<uav_skill> uavSkills = GameEntry.LDLTable.GetTable<uav_skill>();
            for (var i = 0; i < uavSkills.Count; i++)
            {
                uav_skill uavSkill = uavSkills[i];
                int skillStarDemand = uavSkill.star_demand;
                if (skillStarDemand != 0)
                {
                    if (level == skillStarDemand)
                    {
                        return true;
                    }
                }
            }
            return false;
        }
        
        public uav_skill GetSkillConfigByUpLevel(int level)
        {
            List<uav_skill> uavSkills = GameEntry.LDLTable.GetTable<uav_skill>();
            for (int i = 0; i < uavSkills.Count; i++)
            {
                uav_skill uavSkill = uavSkills[i];
                if ((level == 1 && i == 0) || (uavSkill.star_demand != 0 && uavSkill.star_demand == level))
                {
                    return uavSkill;
                }
            }
            return null;
        }

        public bool CheckAllComponentIsHaveBatter()
        {
            // 遍历组件部位类型枚举 判断是否有更好的
            foreach (componenttype value in Enum.GetValues(typeof(componenttype)))
            {
                bool isFind = FindIsHaveBatterComponentByType(value);
                if (isFind)
                {
                    return true;
                }
            }

            return false;
        }
        
        public bool FindIsHaveBatterComponentByType(componenttype cType)
        {
            uint componentId = UavModule.GetComponentByType(cType);
            itemsubtype itemSubType = GameEntry.LogicData.UAVData.GetItemSubTypeMappingByComponentType(cType);
            List<ItemModule> itemModules = GameEntry.LogicData.BagData.GetDataBySubType(itemSubType);
            if (componentId == 0)
            {
                return itemModules.Count > 0;
            }
            else
            {
                // todo 有装备的判断是否有比当前的装备品质更好或者等级更高的
                component_level curComponentLevelCfg = GameEntry.LDLTable.GetTableById<component_level>((int)componentId);
                for (int i = 0; i < itemModules.Count; i++)
                {
                    var itemModule = itemModules[i];
                    component_level levelCfg = GameEntry.LDLTable.GetTableById<component_level>((int)itemModule.ItemId);
                    return levelCfg.level > curComponentLevelCfg.level;
                }
            }
            return false;
        }

        public itemsubtype GetItemSubTypeMappingByComponentType(componenttype cType)
        {
            return cType switch
            {
                componenttype.component_type_1 => itemsubtype.itemsubtype_171,
                componenttype.component_type_2 => itemsubtype.itemsubtype_172,
                componenttype.component_type_3 => itemsubtype.itemsubtype_173,
                componenttype.component_type_4 => itemsubtype.itemsubtype_174,
                componenttype.component_type_5 => itemsubtype.itemsubtype_175,
                componenttype.component_type_6 => itemsubtype.itemsubtype_176,
                _ => itemsubtype.itemsubtype_nil,
            };
        }
        
        public string GetcomponentNormalIcon(componenttype cTpye)
        {
            return cTpye switch
            {
                componenttype.component_type_1 => "Sprite/ui_jianzhu_wurenji/wurenji_zujian_tubiao1.png",
                componenttype.component_type_2 => "Sprite/ui_jianzhu_wurenji/wurenji_zujian_tubiao2.png",
                componenttype.component_type_3 => "Sprite/ui_jianzhu_wurenji/wurenji_zujian_tubiao6.png",
                componenttype.component_type_4 => "Sprite/ui_jianzhu_wurenji/wurenji_zujian_tubiao3.png",
                componenttype.component_type_5 => "Sprite/ui_jianzhu_wurenji/wurenji_zujian_tubiao5.png",
                componenttype.component_type_6 => "Sprite/ui_jianzhu_wurenji/wurenji_zujian_tubiao4.png",
            };
        }

        public string GetComponentNameByType(componenttype cType)
        {
            int langId = cType switch
            {
                componenttype.component_type_1 => 1280,
                componenttype.component_type_2 => 1281,
                componenttype.component_type_3 => 1282,
                componenttype.component_type_4 => 1283,
                componenttype.component_type_5 => 1284,
                componenttype.component_type_6 => 1285,
            };
            string name = ToolScriptExtend.GetLang(langId);
            return name;
        }

        public component_level GetComponentLevelCfgByLevelAndType(int level,componenttype cType)
        {
            if (cType == componenttype.component_type_nil)
            {
                return null;
            }
            Dictionary<int,component_level> componentLevels = componentLevelDic[cType];
            if (componentLevels.ContainsKey(level))
            {
                return componentLevels[level];
            }

            return null;
        }
        
        public List<component_level> GetComponentLevelCfgByType(componenttype cType)
        {
            if (cType == componenttype.component_type_nil)
            {
                return null;
            }

            List<component_level> list = new List<component_level>();
            Dictionary<int,component_level> componentLevels = componentLevelDic[cType];
            foreach (KeyValuePair<int,component_level> levelCfg in componentLevels)
            {
                list.Add(levelCfg.Value);
            }
            
            list.Sort(((a, b) => a.level.CompareTo(b.level)));

            return list;
        }     
        
        /// <summary>
        /// 判断当前部位是否有可合成的
        /// </summary>
        /// <param name="cType"></param>
        /// <returns></returns>
        public bool GetComponentCanCompoundByType(componenttype cType)
        {
            if (cType == componenttype.component_type_nil)
            {
                return false;
            }
            uint curComponentId = UavModule.GetComponentByType(cType);
            bool isEquip = curComponentId != 0;
            itemsubtype itemSubType = GameEntry.LogicData.UAVData.GetItemSubTypeMappingByComponentType(cType);
            List<ItemModule> itemModules = GameEntry.LogicData.BagData.GetDataBySubType(itemSubType);
            foreach (ItemModule itemModule in itemModules)
            {
                long itemModuleCount = itemModule.Count;
                if (isEquip && (itemid)curComponentId == itemModule.ItemId)
                {
                    itemModuleCount = itemModuleCount - 1;
                }

                if (itemModuleCount >= 3)
                {
                    return true;
                }
            }
            
            return false;
        }

        public string GetUavDisplayPath()
        {
            string path = string.Empty;
            int skinId = (int)UavModule.Skin;
            uav_skin uavSkinCfg = GameEntry.LDLTable.GetTableById<uav_skin>(skinId);
            if (uavSkinCfg != null)
            {
                return uavSkinCfg.model;
            }
            return path;
        }

        #endregion

        #region 协议相关

        /// <summary>
        /// 请求无人机升级
        /// </summary>
        /// <param name="callBack">回调方法</param>
        public void UavUpgradeReq(Action callBack = null)
        {
            UavUpgradeReq uavUpgradeReq = new UavUpgradeReq();
            GameEntry.LDLNet.Send(Protocol.MessageID.UavUpgrade, uavUpgradeReq, (message) =>
            {
                UavUpgradeResp resp = (UavUpgradeResp)message;
                if (resp != null)
                {
                    GameEntry.LogicData.UAVData.UavModule.UpdateUAVInfo(resp.Code,resp.Exp);
                    if (GameEntry.UI.HasUIForm(EnumUIForm.UIBuildingUAVForm))
                    {
                        GameEntry.UI.RefreshUIForm(EnumUIForm.UIBuildingUAVForm,new UIBuildingUAVFormParams(UIUavFormRefreshType.UpgradeRefresh));
                    }
                    callBack?.Invoke();
                    GameEntry.Event.Fire(UavChangeLevelEventArgs.EventId, UavChangeLevelEventArgs.Create());
                }
            });
        }
        
        /// <summary>
        /// 请求更换皮肤
        /// </summary>
        /// <param name="skinId">皮肤id</param>
        /// <param name="callBack"></param>
        public void UavUseSkinReq(uint skinId,Action callBack = null)
        {
            UavUseSkinReq uavUseSkinReq = new UavUseSkinReq();
            uavUseSkinReq.Skin = skinId;
            GameEntry.LDLNet.Send(Protocol.MessageID.UavUseSkin, uavUseSkinReq, (message) =>
            {
                UavUseSkinResp resp = (UavUseSkinResp)message;
                if (resp != null)
                {
                    callBack?.Invoke();
                }
            });
        }
        
        /// <summary>
        /// 请求装备组件
        /// </summary>
        /// <param name="code">装备id</param>
        /// <param name="callBack"></param>
        public void UavEquipComponentReq(uint code,Action callBack = null)
        {
            UavEquipComponentReq uavEquipReq = new UavEquipComponentReq();
            uavEquipReq.Code = code;
            GameEntry.LDLNet.Send(Protocol.MessageID.UavEquipComponent, uavEquipReq, (message) =>
            {
                UavEquipComponentResp resp = (UavEquipComponentResp)message;
                if (resp != null)
                {
                    m_UAVModule.UpdateUAVInfo(resp.Component.Code,resp.Component.Exp);
                    callBack?.Invoke();
                    if (GameEntry.UI.HasUIForm(EnumUIForm.UIUAVComponentUpForm))
                    {
                        GameEntry.UI.RefreshUIForm(EnumUIForm.UIUAVComponentUpForm);
                    }
                    GameEntry.UI.RefreshUIForm(EnumUIForm.UIBuildingUAVForm,new UIBuildingUAVFormParams(UIUavFormRefreshType.OneClickEquipComponentRefresh));
                }
            });
        }
        
        /// <summary>
        /// 请求一建装备组件
        /// </summary>
        /// <param name="callBack"></param>
        public void UavOneClickEquipComponentReq(Action callBack = null)
        {
            UavOneClickEquipComponentReq uavOneClickEquipComponentReq = new UavOneClickEquipComponentReq();
            GameEntry.LDLNet.Send(Protocol.MessageID.UavOneClickEquipComponent, uavOneClickEquipComponentReq, (message) =>
            {
                UavOneClickEquipComponentResp resp = (UavOneClickEquipComponentResp)message;
                if (resp != null)
                {
                    m_UAVModule.UpdateUAVInfo(resp.Components);
                    callBack?.Invoke();
               
                    GameEntry.UI.RefreshUIForm(EnumUIForm.UIBuildingUAVForm,new UIBuildingUAVFormParams(UIUavFormRefreshType.OneClickEquipComponentRefresh));
                }
            });
        }
        
        /// <summary>
        /// 请求一键合成
        /// </summary>
        /// <param name="componentType">组件类型 即某个部位</param>
        /// <param name="callBack"></param>
        public void UavOneClickSynthesisReq(uint componentType,Action callBack = null)
        {
            UavOneClickSynthesisReq uavOneClickSynthesisReq = new UavOneClickSynthesisReq();
            uavOneClickSynthesisReq.Componenttype = componentType;
            GameEntry.LDLNet.Send(Protocol.MessageID.UavOneClickSynthesis, uavOneClickSynthesisReq, (message) =>
            {
                UavOneClickSynthesisResp resp = (UavOneClickSynthesisResp)message;
                if (resp != null)
                {
                    callBack?.Invoke();
                }
            });
        }
        
        /// <summary>
        /// 请求研究
        /// </summary>
        /// <param name="componentType">组件类型 即某个部位</param>
        /// <param name="articles">消耗的组件</param>
        /// <param name="callBack"></param>
        public void UavResearchReq(uint componentType,List<Article.Article> articles,Action callBack = null)
        {
            UavResearchReq uavOneClickSynthesisReq = new UavResearchReq();
            uavOneClickSynthesisReq.Componenttype = componentType;
            uavOneClickSynthesisReq.Articles.AddRange(articles);
            GameEntry.LDLNet.Send(Protocol.MessageID.UavResearch, uavOneClickSynthesisReq, (message) =>
            {
                UavOneClickSynthesisResp resp = (UavOneClickSynthesisResp)message;
                if (resp != null)
                {
                    callBack?.Invoke();
                }
            });
        }

        #endregion
    }
}
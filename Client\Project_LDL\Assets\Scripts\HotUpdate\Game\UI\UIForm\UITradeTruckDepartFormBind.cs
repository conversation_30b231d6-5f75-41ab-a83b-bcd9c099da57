using UnityEngine;
using UnityEngine.UI;

namespace Game.Hotfix
{
    public partial class UITradeTruckDepartForm : UGuiFormEx
    {
        [SerializeField] private UIButton m_btnRefresh;
        [SerializeField] private UIButton m_btnTip;
        [SerializeField] private UIButton m_btnGo;
        [SerializeField] private UIButton m_btnSetTeam;
        [SerializeField] private UIButton m_btnMask;
        [SerializeField] private UIButton m_btnBuffGoto;
        [SerializeField] private UIButton m_btnNotRefresh;
        [SerializeField] private UIButton m_btnContinueRefresh;

        [SerializeField] private UIText m_txtTodayCount;
        [SerializeField] private UIText m_txtArriveTime;
        [SerializeField] private UIText m_txtRefreshCount;
        [SerializeField] private UIText m_txtTeam;
        [SerializeField] private UIText m_txtBuffTip;
        [SerializeField] private UIText m_txtRefreshConfirm;

        [SerializeField] private UIImage m_imgCardQuality;
        [SerializeField] private UIImage m_imgTruckQuality_1;
        [SerializeField] private UIImage m_imgTruckQuality;
        [SerializeField] private UIImage m_imgDice;
        [SerializeField] private UIImage m_imgQuality;

        [SerializeField] private UIToggle m_togRefreshConfirm;

        [SerializeField] private GameObject m_goDiceLight;
        [SerializeField] private GameObject m_goDiceAnim;
        [SerializeField] private Transform m_transBuff;
        [SerializeField] private Transform m_transBuffItem;
        [SerializeField] private Transform m_transContentReward;
        [SerializeField] private Transform m_transReward;
        [SerializeField] private GameObject m_goRefreshTip;
        [SerializeField] private GameObject m_goTeam;
        [SerializeField] private Transform m_transHeroItem;
        [SerializeField] private Transform m_transContentHero;
        [SerializeField] private GameObject m_goBuffTip;
        [SerializeField] private GameObject m_goBuffTipArrow;
        [SerializeField] private GameObject m_goRefreshConfirm;

        void InitBind()
        {
            m_btnRefresh.onClick.AddListener(OnBtnRefreshClick);
            m_btnTip.onClick.AddListener(OnBtnTipClick);
            m_btnGo.onClick.AddListener(OnBtnGoClick);
            m_btnSetTeam.onClick.AddListener(OnBtnSetTeamClick);
            m_btnMask.onClick.AddListener(OnBtnMaskClick);
            m_btnBuffGoto.onClick.AddListener(OnBtnBuffGotoClick);
            m_btnNotRefresh.onClick.AddListener(OnBtnNotRefreshClick);
            m_btnContinueRefresh.onClick.AddListener(OnBtnContinueRefreshClick);
        }
    }
}
